# 桌面应用架构简化设计文档

##  概述
本设计文档描述了如何将当前基于AI+MCP复杂系统的电商需求洞察桌面应用，简化为传统爬虫+AI分析的架构。设计目标是保持所有用户功能和体验不变，同时大幅降低系统复杂性。

## 架构设计

### 整体架构对比

#### 当前架构（复杂）
前端组件 → Electron IPC → IntelligentTaskOrchestrator → AIMCPSystem → AI Agent → MCP Tools → BrowserManager/PlatformScraper

#### 目标架构（简化）
前端组件 → Electron IPC → SimpleTaskExecutor → BrowserManager/PlatformScraper + SimpleAIService

### 核心组件设计

#### 1. SimpleTaskExecutor（新增核心组件）

##### 职责：
替代IntelligentTaskOrchestrator，提供简化的任务执行逻辑

'''
class SimpleTaskExecutor {
  constructor(
    private browserManager: BrowserManager,
    private aiService: SimpleAIService,
    private localStorage: LocalStorage
  )

  // 主要方法
  async executeTask(keywords: string[], options: TaskOptions): Promise<TaskResult>
  async executeQuickScan(keywords: string[]): Promise<TaskResult>
  async executeDeepScan(keywords: string[]): Promise<TaskResult>
  
  // 进度管理
  setProgressCallback(callback: (progress: TaskProgress) => void): void
  private reportProgress(phase: string, message: string, current: number, total: number): void
}
'''

##### 设计原则：
-- 保持与IntelligentTaskOrchestrator相同的接口
-- 内部使用简单的if-else逻辑替代AI决策
-- 直接调用BrowserManager和PlatformScraper
-- 保持现有的进度回调机制

#### 2. SimpleAIService（新增AI服务）

##### 职责：
封装所有AI调用，保留精心制作的prompt

'''
class SimpleAIService {
  constructor(private openaiClient: OpenAI)

  // 核心AI功能（保留现有prompt）
  async expandKeywords(keyword: string): Promise<string[]>
  async clusterKeywords(keywords: string[]): Promise<ClusterResult[]>
  async analyzeComments(comments: string[], productTitle?: string): Promise<AnalysisResult>
  async generateMarketInsights(summaries: AnalysisSummary[]): Promise<InsightReport>
  
  // 辅助方法
  private buildSystemPrompt(): string  // 保留现有的精心制作prompt
  private parseJSONResponse<T>(content: string): T
}
'''

##### 设计原则：
-- 保留web/lib/prompts.ts中的所有prompt工程
-- 保留ai-agent.ts中的精心制作的系统提示词
-- 使用统一的OpenAI客户端配置
-- 保持现有的AI I/O系统输出格式

#### 3. 保留的核心组件

##### BrowserManager：完全保留
-- 双平台浏览器管理（淘宝/小红书）
-- 登录状态管理和持久化
-- 页面池管理和并发控制
-- 可视化模式切换

##### PlatformScraper：完全保留
-- 平台特定的爬虫逻辑
-- 反爬虫策略和异常处理
-- 链接发现和评论抓取
-- 登录检测和验证码处理

##### AI I/O System：完全保留
-- 多种输出渲染器（Console、Electron、File）
-- 结构化的AI输出格式
-- 实时进度展示和思考过程

##### LocalStorage：完全保留
-- 任务数据持久化
-- 历史记录管理
-- 数据导出功能

### 数据流设计

#### 快速扫描模式流程

graph TD
    A[用户输入关键词] --> B[SimpleTaskExecutor.executeQuickScan]
    B --> C[直接调用BrowserManager]
    C --> D[PlatformScraper爬取链接]
    D --> E[PlatformScraper爬取评论]
    E --> F[SimpleAIService.analyzeComments]
    F --> G[生成分析报告]
    G --> H[保存到LocalStorage]
    H --> I[返回结果给前端]

#### 深度扫描模式流程

graph TD
    A[用户输入关键词] --> B[SimpleTaskExecutor.executeDeepScan]
    B --> C{关键词数量 < 5?}
    C -->|是| D[SimpleAIService.expandKeywords]
    C -->|否| E[SimpleAIService.clusterKeywords]
    D --> F[等待用户确认]
    E --> F
    F --> G[用户确认关键词]
    G --> H[BrowserManager爬取链接]
    H --> I[PlatformScraper爬取评论]
    I --> J[SimpleAIService.analyzeComments]
    J --> K[SimpleAIService.generateMarketInsights]
    K --> L[保存到LocalStorage]
    L --> M[返回结果给前端]

### 接口兼容性设计

#### IPC接口保持不变

'''
// main.ts中的IPC处理器保持完全相同的签名
ipcMain.handle('start-task', async (event, taskData) => {
  // 内部实现改为使用SimpleTaskExecutor
  const executor = new SimpleTaskExecutor(browserManager, aiService, localStorage);
  return executor.executeTask(taskData.initialKeywords, taskData);
});

ipcMain.handle('get-tasks', async () => {
  return localStorage.getAllTasks(); // 保持不变
});

ipcMain.handle('get-task-by-id', async (event, taskId) => {
  return localStorage.getTask(taskId); // 保持不变
});
'''

#### 前端组件零修改
-- PromptInputForm.tsx：无需修改，继续调用window.electron.startTask()
-- TaskMonitor.tsx：无需修改，继续监听任务状态变化
-- ReportContainer.tsx：无需修改，继续渲染分析结果
-- 所有其他组件：完全不需要修改

### 错误处理设计

#### 登录错误处理

'''
class SimpleTaskExecutor {
  private async handleLoginRequired(platform: Platform): Promise<void> {
    // 保持现有的登录处理逻辑
    this.reportProgress('login_required', `需要${platform}平台登录`, 0, 1);
    
    // 触发登录流程（保持现有实现）
    await this.browserManager.startLoginSession(platform);
    
    // 等待登录完成
    await this.waitForLoginCompletion(platform);
  }
}
'''

#### 验证码处理

'''
class SimpleTaskExecutor {
  private async handleCaptcha(platform: Platform, captchaType: string): Promise<void> {
    // 保持现有的验证码处理逻辑
    this.reportProgress('captcha_required', `需要处理${platform}验证码`, 0, 1);
    
    // 使用现有的验证码处理器
    await this.platformScraper.handleCaptcha(platform, captchaType);
  }
}
'''

### 性能优化设计

#### 并发控制
-- 保持现有的页面池管理（每平台最多5个页面）
-- 保持现有的并发限制和资源管理
-- 移除复杂的智能调度，使用简单的队列机制

#### 内存管理
-- 移除复杂的缓存系统，减少内存占用
-- 保持必要的数据缓存（登录状态、任务结果）
-- 及时清理不需要的浏览器页面和上下文

### 配置管理设计

#### 统一配置系统

'''
class SimplifiedConfig {
  // AI配置（保留现有配置管理器）
  getAIConfig(): AIConfig
  
  // 爬虫配置
  getScrapingConfig(): ScrapingConfig
  
  // 浏览器配置
  getBrowserConfig(): BrowserConfig
  
  // 简化配置，移除复杂的多模型配置
}
'''

## 组件和接口

### 新增组件

#### SimpleTaskExecutor
-- 输入：关键词数组、任务选项
-- 输出：任务结果（保持现有格式）
-- 依赖：BrowserManager、SimpleAIService、LocalStorage

#### SimpleAIService
-- 输入：各种AI分析请求
-- 输出：AI分析结果（保持现有格式）
-- 依赖：OpenAI客户端、现有prompt系统

### 保留组件

#### BrowserManager
-- 接口：完全保持不变
-- 功能：双平台浏览器管理、登录管理、页面池管理

#### PlatformScraper
-- 接口：完全保持不变
-- 功能：平台特定爬虫逻辑、反爬虫处理

#### AI I/O System
-- 接口：完全保持不变
-- 功能：AI输出格式化、多渲染器支持

#### LocalStorage
-- 接口：完全保持不变
-- 功能：数据持久化、历史管理

### 移除组件

#### 复杂AI决策系统
-- IntelligentTaskOrchestrator
-- IntelligentDecisionEngine
-- IntelligentCacheManager
-- IntelligentRetryManager
-- IntelligentAnalytics（部分功能迁移到SimpleAIService）

#### MCP相关组件
-- MCPScrapingServer
-- MCPToolHandlers
-- MCP SDK依赖

## 数据模型

### 任务数据模型（保持不变）

'''
interface Task {
  id: string;
  status: TaskStatus;
  scanMode: 'quick' | 'deep';
  initialKeywords: string[];
  finalKeywords?: string[];
  discoveredLinks: string[];
  scrapedComments: Record<string, string[]>;
  analysis: AnalysisResult;
  createdAt: string;
  updatedAt: string;
}
'''

### 任务结果模型（保持不变）

'''
interface TaskResult {
  success: boolean;
  discoveredLinks: string[];
  scrapedComments: Record<string, string[]>;
  analysis: AnalysisResult;
  metadata: {
    timestamp: string;
    duration: number;
    tokensUsed?: number;
  };
}
'''

### AI分析结果模型（保持不变）

'''
interface AnalysisResult {
  summary: string;
  insights: MarketInsight[];
  opportunities: MarketOpportunity[];
  keywordCloud: KeywordCloudData[];
}
'''

## 错误处理

### 错误分类
-- 登录错误：使用现有登录管理系统处理
-- 验证码错误：使用现有验证码处理逻辑
-- 网络错误：简单重试机制（移除复杂的智能重试）
-- AI分析错误：降级处理，返回基础分析结果
-- 数据存储错误：文件系统错误处理

### 错误恢复策略
-- 保持现有的任务状态管理
-- 支持任务暂停和恢复
-- 提供清晰的错误信息和用户指导
-- 移除复杂的自动恢复逻辑，改为用户手动重试

## 测试策略

### 单元测试
-- SimpleTaskExecutor的各个方法
-- SimpleAIService的AI调用功能
-- 数据转换和格式化逻辑

### 集成测试
-- 完整的任务执行流程
-- 前端组件与后端的IPC通信
-- 浏览器管理和爬虫功能

### 用户验收测试
-- 快速扫描和深度扫描流程
-- 登录管理和验证码处理
-- 数据导出和历史查看功能