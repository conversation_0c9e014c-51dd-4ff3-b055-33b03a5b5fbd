# 桌面应用架构简化需求文档

## 项目概述
将当前基于AI+MCP复杂系统的电商需求洞察桌面应用，简化为传统爬虫+AI分析的架构，保持所有核心功能和用户体验不变，同时大幅降低系统复杂性和维护成本。

## 需求列表

### 需求1：保持桌面应用架构不变

#### 用户故事
作为用户，我希望应用保持现有的桌面应用体验，这样我可以继续享受本地存储、离线使用和浏览器控制等优势。

#### 验收标准
WHEN 用户启动应用 THEN 系统应以Electron桌面应用形式运行
WHEN 用户执行任务 THEN 系统应使用IPC通信而非HTTP API
WHEN 用户关闭应用 THEN 所有数据应保存在本地存储中
WHEN 用户重新打开应用 THEN 应能恢复之前的任务状态和历史记录

### 需求2：保留所有前端组件和用户界面

#### 用户故事
作为用户，我希望界面和交互方式完全不变，这样我无需重新学习如何使用应用。

#### 验收标准
WHEN 用户访问需求输入页 THEN PromptInputForm组件应正常工作
WHEN 用户查看任务监控 THEN TaskMonitor和EnhancedTaskMonitor应正常显示
WHEN 用户查看分析报告 THEN ReportContainer和InsightCard应正常渲染
WHEN 用户确认关键词 THEN KeywordConfirmationModal应正常弹出和交互
WHEN 用户查看任务列表 THEN TaskList和TaskItem应正常显示历史任务

### 需求3：简化为传统爬虫架构

#### 用户故事
作为开发者，我希望移除复杂的AI+MCP系统，使用直接的爬虫调用，这样系统更容易理解和维护。

#### 验收标准
WHEN 系统执行爬虫任务 THEN 应直接调用BrowserManager和PlatformScraper
WHEN 系统需要爬取链接 THEN 应使用传统的Playwright页面操作
WHEN 系统遇到登录需求 THEN 应使用现有的登录管理系统
WHEN 系统需要处理验证码 THEN 应使用现有的验证码处理逻辑
WHEN 系统执行任务 THEN 不应依赖MCP协议和工具调用

### 需求4：保留精心制作的AI分析能力

#### 用户故事
作为用户，我希望AI分析的质量和格式保持不变，这样我能继续获得高质量的市场洞察。

#### 验收标准
WHEN 系统进行关键词扩展 THEN 应使用现有的expansion prompt
WHEN 系统进行关键词聚类 THEN 应使用现有的clustering prompt
WHEN 系统分析评论数据 THEN 应使用现有的map analysis prompt
WHEN 系统生成最终报告 THEN 应使用现有的reduce analysis prompt
WHEN 系统输出AI内容 THEN 应使用现有的AI I/O系统格式

### 需求5：保持任务执行流程不变

#### 用户故事
作为用户，我希望快速扫描和深度扫描的流程完全不变，这样我的使用习惯不会被打断。

#### 验收标准
WHEN 用户选择快速扫描 THEN 系统应跳过AI关键词预处理，直接执行爬虫
WHEN 用户选择深度扫描且关键词<5个 THEN 系统应进行关键词扩展
WHEN 用户选择深度扫描且关键词≥5个 THEN 系统应进行关键词聚类
WHEN 系统完成关键词处理 THEN 应等待用户确认后继续
WHEN 系统执行爬虫 THEN 应按淘宝44个、小红书50个的规则抓取
WHEN 系统完成数据采集 THEN 应进行AI分析生成最终报告

### 需求6：保持任务状态和进度管理

#### 用户故事
作为用户，我希望能实时看到任务执行进度和状态变化，这样我能了解任务的执行情况。

#### 验收标准
WHEN 任务开始执行 THEN 状态应从PENDING变为相应的执行状态
WHEN 任务需要用户确认 THEN 状态应变为WAITING_CONFIRMATION
WHEN 任务正在爬取数据 THEN 状态应显示DISCOVERING或SCRAPING
WHEN 任务正在AI分析 THEN 状态应显示ANALYZING
WHEN 任务完成 THEN 状态应变为COMPLETED并显示结果
WHEN 任务失败 THEN 状态应显示错误信息和重试选项

### 需求7：保持数据存储和导出功能

#### 用户故事
作为用户，我希望能正常保存任务数据并导出分析结果，这样我能持久化保存我的工作成果。

#### 验收标准
WHEN 任务完成 THEN 数据应保存到本地存储
WHEN 用户重启应用 THEN 应能加载历史任务数据
WHEN 用户导出数据 THEN 应支持Excel、CSV、JSON格式
WHEN 用户删除任务 THEN 相关数据应从本地存储中移除
WHEN 系统存储数据 THEN 应保持现有的数据格式兼容性

### 需求8：移除复杂的AI决策系统

#### 用户故事
作为开发者，我希望移除过度复杂的AI智能决策组件，使用固定的业务逻辑，这样系统更稳定可靠。

#### 验收标准
WHEN 系统初始化 THEN 不应加载IntelligentDecisionEngine
WHEN 系统执行任务 THEN 不应使用IntelligentCacheManager
WHEN 系统遇到错误 THEN 不应使用IntelligentRetryManager
WHEN 系统需要分析 THEN 不应使用IntelligentTaskOrchestrator
WHEN 系统运行 THEN 不应依赖MCP协议和相关组件

### 需求9：保持登录和浏览器管理功能

#### 用户故事
作为用户，我希望登录管理和浏览器控制功能保持不变，这样我能继续正常使用平台登录和可视化功能。

#### 验收标准
WHEN 用户需要登录平台 THEN 应弹出可视化登录窗口
WHEN 用户完成登录 THEN 系统应保存登录状态到本地
WHEN 用户切换浏览器模式 THEN 应支持headless和visible模式切换
WHEN 系统检测登录状态 THEN 应使用现有的登录验证逻辑
WHEN 用户清除登录 THEN 应能正常清除平台登录状态

### 需求10：保持性能监控和系统状态

#### 用户故事
作为用户，我希望能查看系统运行状态和性能指标，这样我能了解系统的健康状况。

#### 验收标准
WHEN 用户查看系统状态 THEN 应显示浏览器管理器状态
WHEN 用户查看工作器状态 THEN 应显示页面池使用情况
WHEN 用户查看实时统计 THEN 应显示任务执行统计
WHEN 系统运行异常 THEN 应提供错误信息和恢复建议
WHEN 用户导出日志 THEN 应能获取系统运行日志