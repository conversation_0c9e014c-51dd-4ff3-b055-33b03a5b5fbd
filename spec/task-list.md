# 桌面应用架构简化实施计划

## 实施概述
将当前复杂的AI+MCP系统简化为传统爬虫+AI分析架构，保持所有用户功能不变。按照渐进式重构的方式，确保每个步骤都可以独立测试和回滚。

## 任务列表

[x] 1. 创建核心简化组件
-- 实现SimpleTaskExecutor类，提供与IntelligentTaskOrchestrator相同的接口
-- 实现SimpleAIService类，封装所有AI调用并保留现有prompt
-- 确保新组件能够正确处理快速扫描和深度扫描模式
-- 需求: 1.1, 3.1, 4.1, 5.1

[x] 1.1 实现SimpleTaskExecutor基础结构
-- 创建web/lib/simple-task-executor.ts文件
-- 实现构造函数和基本属性初始化
-- 实现setProgressCallback方法保持进度回调兼容性
-- 需求: 3.1, 6.1

[x] 1.2 实现SimpleAIService基础结构
-- 创建web/lib/simple-ai-service.ts文件
-- 集成现有的OpenAI客户端配置
-- 保留web/lib/prompts.ts中的所有prompt工程
-- 需求: 4.1, 4.2, 4.3

[x] 1.3 实现快速扫描模式逻辑
-- 在SimpleTaskExecutor中实现executeQuickScan方法
-- 直接调用BrowserManager和PlatformScraper进行数据采集
-- 跳过AI关键词预处理，直接进行爬虫操作
-- 需求: 5.1, 3.1

[x] 1.4 实现深度扫描模式逻辑
-- 在SimpleTaskExecutor中实现executeDeepScan方法
-- 实现关键词数量判断逻辑（<5个扩展，≥5个聚类）
-- 集成用户关键词确认流程
-- 需求: 5.2, 5.3, 5.4

[x] 2. 集成AI分析功能
-- 在SimpleAIService中实现关键词扩展功能，使用现有expansion prompt
-- 实现关键词聚类功能，使用现有clustering prompt
-- 实现评论分析功能，使用现有map和reduce analysis prompt
-- 保持AI I/O系统的输出格式和渲染器
-- 需求: 4.1, 4.2, 4.3, 4.4

[x] 2.1 实现关键词扩展功能
-- 在SimpleAIService中实现expandKeywords方法
-- 使用web/lib/prompts.ts中的getExpansionPrompt
-- 保持现有的JSON响应解析逻辑
-- 需求: 4.1, 5.2

[x] 2.2 实现关键词聚类功能
-- 在SimpleAIService中实现clusterKeywords方法
-- 使用web/lib/prompts.ts中的getClusteringPrompt
-- 返回用户确认所需的聚类结果格式
-- 需求: 4.2, 5.3

[x] 2.3 实现评论分析功能
-- 实现analyzeComments方法，使用getMapAnalysisPrompt
-- 实现generateMarketInsights方法，使用getReduceAnalysisPrompt
-- 保持现有的分析结果数据结构
-- 需求: 4.3, 4.4

[x] 2.4 集成AI I/O系统
-- 在SimpleAIService中集成现有的AI I/O系统
-- 保持thinking、decision、action、result等输出格式
-- 确保Electron渲染器正常工作
-- 需求: 4.5

[x] 3. 更新Electron主进程集成
-- 修改web/main/main.ts中的初始化逻辑，使用新的简化组件
-- 保持所有IPC处理器的接口不变，内部实现改为调用SimpleTaskExecutor
-- 移除复杂AI+MCP系统的初始化代码
-- 确保任务状态管理和进度回调正常工作
-- 需求: 1.1, 6.1, 6.2, 8.1

[x] 3.1 更新系统初始化逻辑
-- 移除AIMCPSystem和IntelligentTaskOrchestrator的初始化
-- 添加SimpleTaskExecutor和SimpleAIService的初始化
-- 保持BrowserManager的初始化不变
-- 需求: 8.1, 8.2, 8.3

[x] 3.2 更新IPC处理器实现
-- 修改start-task处理器，使用SimpleTaskExecutor.executeTask ✅
-- 保持get-tasks和get-task-by-id处理器不变 ✅
-- 确保confirm-keywords处理器正常工作 ✅
-- 修改resume-task和retry-task处理器支持V2.5架构 ✅
-- 需求: 1.1, 5.4

[x] 3.3 更新进度回调系统
-- 确保SimpleTaskExecutor的进度回调能正确传递到前端 ✅
-- 保持现有的任务状态流转逻辑 ✅
-- 维护task-progress IPC事件的发送 ✅
-- 创建V2.5到V3.0进度格式的适配器 ✅
-- 需求: 6.1, 6.2, 6.3

[x] 4. 保持数据兼容性和存储功能
-- 确保新系统生成的任务数据与现有LocalStorage系统兼容
-- 保持任务历史记录和数据导出功能正常工作
-- 验证数据格式的向后兼容性
-- 需求: 7.1, 7.2, 7.3, 7.5

[x] 4.1 验证任务数据格式兼容性
-- 确保SimpleTaskExecutor返回的TaskResult格式正确
-- 验证discoveredLinks和scrapedComments数据结构
-- 测试analysis结果的数据格式
-- 需求: 7.5

[x] 4.2 测试数据存储功能
-- 验证任务完成后数据正确保存到LocalStorage
-- 测试应用重启后历史任务数据加载
-- 确认任务删除功能正常工作
-- 需求: 7.1, 7.2, 7.4

[x] 4.3 验证数据导出功能
-- 测试Excel、CSV、JSON格式导出
-- 确保导出的数据包含完整的分析结果
-- 验证ExportManager与新系统的兼容性
-- 需求: 7.3

[x] 5. 保持登录和浏览器管理功能 ✅ **100%完成** (2025-07-29)
-- ✅ 确保BrowserManager的所有功能在新架构下正常工作
-- ✅ 验证平台登录管理和状态持久化
-- ✅ 测试浏览器可视化模式切换功能
-- ✅ 保持现有的验证码处理和异常恢复逻辑
-- ✅ 9项测试全部通过，成功率100%，总耗时23.6秒
-- 需求: 9.1, 9.2, 9.3, 9.4, 9.5

[x] 5.1 测试登录管理功能
-- 验证淘宝和小红书平台登录流程
-- 测试登录状态检测和持久化
-- 确认登录失败时的错误处理
-- 需求: 9.1, 9.2, 9.4

[x] 5.2 测试浏览器控制功能
-- 验证headless和visible模式切换
-- 测试页面池管理和并发控制
-- 确认浏览器资源清理正常
-- 需求: 9.3

[x] 5.3 测试异常处理逻辑
-- 验证验证码检测和处理流程
-- 测试网络异常和重试机制
-- 确认登录过期时的自动处理
-- 需求: 9.5

[x] 6. 系统测试和性能验证
-- 进行完整的端到端测试，验证快速扫描和深度扫描流程 ✅
-- 测试前端组件与新后端系统的集成 ✅
-- 验证系统性能和资源使用情况 ✅
-- 确保所有现有功能正常工作 ✅
-- 需求: 10.1, 10.2, 10.3, 10.4

[x] 6.1 端到端功能测试
-- 测试完整的快速扫描流程 ✅
-- 测试完整的深度扫描流程（包括关键词确认） ✅
-- 验证任务状态变化和进度显示 ✅
-- 需求: 5.1, 5.2, 5.3, 5.4, 5.5, 5.6

[x] 6.2 前端集成测试
-- 验证PromptInputForm组件正常工作 ✅
-- 测试TaskMonitor和EnhancedTaskMonitor显示 ✅
-- 确认ReportContainer和InsightCard渲染 ✅
-- 需求: 2.1, 2.2, 2.3, 2.4

[x] 6.3 性能和稳定性测试
-- 测试系统资源使用情况 ✅
-- 验证内存泄漏和资源清理 ✅
-- 进行长时间运行稳定性测试 ✅
-- 需求: 10.1, 10.2, 10.3

[ ] 7. 清理和优化
-- 移除不再使用的AI+MCP相关文件和依赖
-- 清理package.json中的MCP相关依赖
-- 更新文档和注释
-- 进行代码优化和重构
-- 需求: 8.1, 8.2, 8.3, 8.4

[ ] 7.1 移除废弃组件
-- 删除web/lib/ai-mcp/目录中的复杂组件文件
-- 保留ai-io-system.ts、config.ts等有价值的文件
-- 移除IntelligentTaskOrchestrator相关代码
-- 需求: 8.1, 8.2, 8.3, 8.4

[ ] 7.2 清理依赖和配置
-- 从package.json移除@modelcontextprotocol/sdk依赖
-- 清理不再使用的import语句
-- 更新TypeScript配置和类型定义
-- 需求: 8.1

[ ] 7.3 代码优化和文档更新
-- 添加新组件的详细注释和文档
-- 更新README和技术文档
-- 进行代码格式化和lint检查
-- 需求: 所有需求