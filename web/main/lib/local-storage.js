"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAllTasks = exports.deleteTask = exports.updateTask = exports.getTask = exports.createTask = void 0;
const promises_1 = __importDefault(require("fs/promises"));
const path = __importStar(require("path"));
const electron_1 = require("electron");
const data_migration_1 = require("./data-migration");
// 移除文件锁依赖，使用更简单的重试机制
// 统一用 userData 目录
const userDataDir = electron_1.app.getPath('userData');
const storageDir = path.join(userDataDir, 'tasks');
// Ensure the storage directory exists
const ensureStorageDir = async () => {
    try {
        await promises_1.default.mkdir(storageDir, { recursive: true });
    }
    catch (error) {
        console.error('Failed to create storage directory:', error);
    }
};
// 🔧 简化的重试配置（移除复杂的文件锁）
const RETRY_OPTIONS = {
    maxRetries: 3,
    baseDelay: 50, // 50ms基础延迟
    maxDelay: 500, // 最大500ms延迟
};
// 🔒 安全的文件读取（带重试）
const safeReadFile = async (filePath) => {
    const { maxRetries, baseDelay, maxDelay } = RETRY_OPTIONS;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const content = await promises_1.default.readFile(filePath, 'utf8');
            return content;
        }
        catch (error) {
            if (error.code === 'ENOENT') {
                throw error; // 文件不存在，直接抛出
            }
            if (attempt < maxRetries) {
                // 指数退避延迟
                const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay);
                await new Promise(resolve => setTimeout(resolve, delay));
                console.warn(`[Storage] Read retry ${attempt}/${maxRetries} for ${path.basename(filePath)}`);
            }
            else {
                throw error; // 最后一次尝试失败
            }
        }
    }
    throw new Error('Unexpected end of retry loop');
};
// 🔒 安全的文件写入（带重试）
const safeWriteFile = async (filePath, content) => {
    // 确保目录存在
    await ensureStorageDir();
    const { maxRetries, baseDelay, maxDelay } = RETRY_OPTIONS;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            await promises_1.default.writeFile(filePath, content, 'utf8');
            return; // 成功写入
        }
        catch (error) {
            if (attempt < maxRetries) {
                // 指数退避延迟
                const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay);
                await new Promise(resolve => setTimeout(resolve, delay));
                console.warn(`[Storage] Write retry ${attempt}/${maxRetries} for ${path.basename(filePath)}`);
            }
            else {
                throw error; // 最后一次尝试失败
            }
        }
    }
};
// 🔍 验证JSON格式并自动修复
const validateAndParseJSON = (content, taskId) => {
    try {
        const parsed = JSON.parse(content);
        // 验证必要字段
        if (!parsed.id || !parsed.status) {
            console.warn(`[Storage] Task ${taskId} missing required fields, treating as corrupted`);
            return null;
        }
        return parsed;
    }
    catch (error) {
        console.error(`[Storage] JSON parse error for task ${taskId}:`, error);
        return null;
    }
};
// 🗑️ 自动清理损坏的文件
const cleanupCorruptedFile = async (filePath, taskId) => {
    try {
        console.warn(`[Storage] 🗑️ Auto-cleaning corrupted task file: ${taskId}`);
        await promises_1.default.unlink(filePath);
        console.log(`[Storage] ✅ Corrupted file cleaned: ${taskId}`);
    }
    catch (error) {
        console.error(`[Storage] ❌ Failed to cleanup corrupted file ${taskId}:`, error);
    }
};
// Call it once at module load time
ensureStorageDir();
const createTask = async (taskData) => {
    const taskId = `task-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    // 确保id和jobId完全一致，避免ID不匹配问题
    const newTask = {
        id: taskId,
        jobId: taskId, // 🔥 确保id和jobId完全一致
        status: 'PENDING',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ...taskData,
    };
    // 🔥 创建标准化数据结构
    try {
        newTask.standardData = data_migration_1.DataMigrator.migrateTaskToStandardFormat(newTask);
        console.log(`[Storage] ✅ Created task ${taskId} with standardized data format`);
    }
    catch (error) {
        console.warn(`[Storage] ⚠️ Failed to create standardized data for task ${taskId}:`, error);
        // 继续创建任务，但没有标准化数据
    }
    const filePath = path.join(storageDir, `${taskId}.json`);
    // 🔒 使用安全的文件写入（带锁）
    const content = JSON.stringify(newTask, null, 2);
    await safeWriteFile(filePath, content);
    console.log(`[Storage] Created task file: ${filePath} with id: ${taskId}`);
    return newTask;
};
exports.createTask = createTask;
const getTask = async (taskId) => {
    const filePath = path.join(storageDir, `${taskId}.json`);
    // 🔧 修复：添加重试机制，解决文件系统并发访问冲突
    const maxRetries = 3;
    const retryDelay = 100; // 100ms
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            // 🔒 使用安全的文件读取（带锁）
            const content = await safeReadFile(filePath);
            // 🔍 验证JSON格式并解析
            const task = validateAndParseJSON(content, taskId);
            if (!task) {
                // JSON格式损坏，自动清理文件
                await cleanupCorruptedFile(filePath, taskId);
                return null;
            }
            // 🔥 自动数据迁移：检查是否需要迁移到标准化格式
            if (data_migration_1.DataMigrator.needsMigration(task)) {
                console.log(`[Storage] 🔄 Auto-migrating task ${taskId} to standard format`);
                try {
                    task.standardData = data_migration_1.DataMigrator.migrateTaskToStandardFormat(task);
                    // 异步保存迁移后的数据，不阻塞读取
                    setImmediate(async () => {
                        try {
                            await (0, exports.updateTask)(taskId, task);
                            console.log(`[Storage] ✅ Task ${taskId} migration saved successfully`);
                        }
                        catch (error) {
                            console.warn(`[Storage] ⚠️ Failed to save migrated task ${taskId}:`, error);
                        }
                    });
                }
                catch (error) {
                    console.warn(`[Storage] ⚠️ Failed to migrate task ${taskId}:`, error);
                    // 继续返回原始任务，不影响功能
                }
            }
            return task;
        }
        catch (error) {
            // 如果文件不存在，直接返回null
            if (error.code === 'ENOENT') {
                return null;
            }
            console.warn(`[Storage] Failed to read task ${taskId}, attempt ${attempt}/${maxRetries}:`, error.message);
            if (attempt < maxRetries) {
                // 等待后重试，使用指数退避
                await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, attempt - 1)));
            }
            else {
                // 最后一次尝试失败，检查是否为JSON损坏
                if (error.message?.includes('JSON') || error.message?.includes('parse')) {
                    console.warn(`[Storage] 🗑️ Task ${taskId} appears to be corrupted, attempting cleanup`);
                    await cleanupCorruptedFile(filePath, taskId);
                }
                console.error(`[Storage] Failed to read task ${taskId} after ${maxRetries} attempts:`, error);
                return null;
            }
        }
    }
    return null;
};
exports.getTask = getTask;
const updateTask = async (taskId, updates) => {
    const task = await (0, exports.getTask)(taskId);
    if (!task) {
        console.warn(`[Storage] Cannot update task ${taskId}: task not found`);
        return null;
    }
    const updatedTask = { ...task, ...updates };
    const filePath = path.join(storageDir, `${taskId}.json`);
    // 🔧 修复：添加写入重试机制，解决文件系统并发访问冲突
    const maxRetries = 3;
    const retryDelay = 100; // 100ms
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            // 🔒 使用安全的文件写入（带锁）
            const content = JSON.stringify(updatedTask, null, 2);
            await safeWriteFile(filePath, content);
            console.log(`[Storage] Task ${taskId} updated successfully`);
            return updatedTask;
        }
        catch (error) {
            console.warn(`[Storage] Failed to write task ${taskId}, attempt ${attempt}/${maxRetries}:`, error.message);
            if (attempt < maxRetries) {
                // 等待后重试，使用指数退避
                await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, attempt - 1)));
            }
            else {
                // 最后一次尝试失败，抛出异常
                console.error(`[Storage] Failed to write task ${taskId} after ${maxRetries} attempts:`, error);
                throw error;
            }
        }
    }
    return updatedTask;
};
exports.updateTask = updateTask;
const deleteTask = async (taskId, force = false) => {
    console.log(`[Storage] Attempting to delete task with id: ${taskId}, force: ${force}`);
    try {
        // 🔧 修复：检查任务是否正在执行中，如果是则拒绝删除（除非强制删除）
        const task = await (0, exports.getTask)(taskId);
        if (!force && task && ['PENDING', 'DISCOVERING', 'SCRAPING', 'ANALYZING'].includes(task.status)) {
            console.log(`[Storage] 🚫 Refusing to delete active task ${taskId} with status ${task.status}`);
            throw new Error(`Cannot delete task ${taskId}: task is currently ${task.status.toLowerCase()}. Use 'Stop Task' first or force delete.`);
        }
        // 确保存储目录存在
        await ensureStorageDir();
        // 首先尝试直接用taskId作为文件名删除
        const directFilePath = path.join(storageDir, `${taskId}.json`);
        try {
            await promises_1.default.unlink(directFilePath);
            console.log(`[Storage] Task file deleted successfully: ${directFilePath}`);
            return;
        }
        catch (error) {
            if (error.code !== 'ENOENT') {
                throw error;
            }
            // 文件不存在，可能是id和jobId不一致的情况
            console.log(`[Storage] Direct file not found, searching through all tasks`);
        }
        // 如果直接删除失败，遍历所有任务文件找到匹配的id
        let files;
        try {
            files = await promises_1.default.readdir(storageDir);
        }
        catch (error) {
            if (error.code === 'ENOENT') {
                console.warn(`[Storage] Storage directory does not exist, task ${taskId} already deleted`);
                return;
            }
            throw error;
        }
        const taskFiles = files.filter(file => file.endsWith('.json'));
        for (const fileName of taskFiles) {
            const filePath = path.join(storageDir, fileName);
            try {
                const data = await promises_1.default.readFile(filePath, 'utf-8');
                const task = JSON.parse(data);
                // 检查id或jobId是否匹配
                if (task.id === taskId || task.jobId === taskId) {
                    await promises_1.default.unlink(filePath);
                    console.log(`[Storage] Task file deleted successfully: ${filePath} (matched ${task.id === taskId ? 'id' : 'jobId'})`);
                    return;
                }
            }
            catch (error) {
                console.error(`[Storage] Error processing file ${fileName}:`, error);
                continue;
            }
        }
        // 如果没有找到匹配的任务，这可能是正常的（任务已被删除）
        console.warn(`[Storage] No task found with id: ${taskId} - may have been already deleted`);
    }
    catch (error) {
        console.error(`[Storage] Failed to delete task ${taskId}:`, error);
        throw error;
    }
};
exports.deleteTask = deleteTask;
const getAllTasks = async () => {
    try {
        const files = await promises_1.default.readdir(storageDir);
        const taskPromises = files
            .filter(file => file.endsWith('.json'))
            .map(file => (0, exports.getTask)(path.basename(file, '.json')));
        const tasks = await Promise.all(taskPromises);
        return tasks.filter((t) => t !== null).sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    }
    catch (error) {
        console.error('Failed to get all tasks:', error);
        return [];
    }
};
exports.getAllTasks = getAllTasks;
