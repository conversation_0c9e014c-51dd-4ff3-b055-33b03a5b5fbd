"use strict";
/**
 * V2.0 专业日志管理器
 *
 * 使用 electron-log 替换所有的 console.log，实现：
 * 1. 日志的持久化存储
 * 2. 分级管理（debug, info, warn, error）
 * 3. 文件轮转和大小限制
 * 4. 结构化日志格式
 * 5. 性能优化的异步写入
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logError = exports.logWarn = exports.logInfo = exports.logDebug = exports.logger = exports.Logger = void 0;
exports.logPerformance = logPerformance;
const electron_log_1 = __importDefault(require("electron-log"));
const path = __importStar(require("path"));
const electron_1 = require("electron");
const config_1 = require("../config");
/**
 * 日志管理器类
 */
class Logger {
    constructor() {
        this.initialized = false;
        // 私有构造函数，确保单例
    }
    static getInstance() {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }
    /**
     * 初始化日志系统
     */
    initialize() {
        if (this.initialized) {
            return;
        }
        const config = (0, config_1.getLoggingConfig)();
        try {
            // 设置日志级别
            electron_log_1.default.transports.console.level = config.logLevel;
            electron_log_1.default.transports.file.level = config.logLevel;
            // 配置文件日志
            if (config.enableFileLogging) {
                const logDir = electron_1.app ? path.join(electron_1.app.getPath('userData'), 'logs') : './logs';
                // 主日志文件
                electron_log_1.default.transports.file.resolvePathFn = () => path.join(logDir, 'main.log');
                electron_log_1.default.transports.file.maxSize = config.maxLogFileSize * 1024 * 1024; // 转换为字节
                // 启用文件轮转
                electron_log_1.default.transports.file.archiveLogFn = (file) => {
                    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                    return file.toString().replace('.log', `-${timestamp}.log`);
                };
            }
            else {
                // 禁用文件日志
                electron_log_1.default.transports.file.level = false;
            }
            // 自定义日志格式
            electron_log_1.default.transports.console.format = '[{y}-{m}-{d} {h}:{i}:{s}.{ms}] [{level}] {text}';
            electron_log_1.default.transports.file.format = '[{y}-{m}-{d} {h}:{i}:{s}.{ms}] [{level}] [{processType}] {text}';
            // 设置控制台颜色
            electron_log_1.default.transports.console.useStyles = true;
            this.initialized = true;
            this.info('Logger initialized successfully', { component: 'Logger' });
        }
        catch (error) {
            console.error('Failed to initialize logger:', error);
            // 降级到 console 日志
            this.initialized = false;
        }
    }
    /**
     * 格式化日志消息
     */
    formatMessage(message, context) {
        if (!context || Object.keys(context).length === 0) {
            return message;
        }
        const contextStr = Object.entries(context)
            .map(([key, value]) => `${key}=${value}`)
            .join(' ');
        return `${message} | ${contextStr}`;
    }
    /**
     * Debug 级别日志
     */
    debug(message, context) {
        const formattedMessage = this.formatMessage(message, context);
        if (this.initialized) {
            electron_log_1.default.debug(formattedMessage);
        }
        else {
            console.debug(`[DEBUG] ${formattedMessage}`);
        }
    }
    /**
     * Info 级别日志
     */
    info(message, context) {
        const formattedMessage = this.formatMessage(message, context);
        if (this.initialized) {
            electron_log_1.default.info(formattedMessage);
        }
        else {
            console.info(`[INFO] ${formattedMessage}`);
        }
    }
    /**
     * Warning 级别日志
     */
    warn(message, context) {
        const formattedMessage = this.formatMessage(message, context);
        if (this.initialized) {
            electron_log_1.default.warn(formattedMessage);
        }
        else {
            console.warn(`[WARN] ${formattedMessage}`);
        }
    }
    /**
     * Error 级别日志
     */
    error(message, error, context) {
        let formattedMessage = this.formatMessage(message, context);
        if (error) {
            if (error instanceof Error) {
                formattedMessage += ` | Error: ${error.message} | Stack: ${error.stack}`;
            }
            else {
                formattedMessage += ` | Error: ${JSON.stringify(error)}`;
            }
        }
        if (this.initialized) {
            electron_log_1.default.error(formattedMessage);
        }
        else {
            console.error(`[ERROR] ${formattedMessage}`);
        }
    }
    /**
     * 性能日志 - 记录操作耗时
     */
    performance(operation, startTime, context) {
        const duration = Date.now() - startTime;
        this.info(`Performance: ${operation} completed in ${duration}ms`, {
            ...context,
            operation,
            duration,
            type: 'performance'
        });
    }
    /**
     * 任务相关日志
     */
    task(level, message, taskId, context) {
        const taskContext = { ...context, taskId, component: 'Task' };
        this[level](message, taskContext);
    }
    /**
     * Worker相关日志
     */
    worker(level, message, workerId, context) {
        const workerContext = { ...context, workerId, component: 'Worker' };
        this[level](message, workerContext);
    }
    /**
     * 平台相关日志
     */
    platform(level, message, platform, context) {
        const platformContext = { ...context, platform, component: 'Platform' };
        this[level](message, platformContext);
    }
    /**
     * 系统相关日志
     */
    system(level, message, context) {
        const systemContext = { ...context, component: 'System' };
        this[level](message, systemContext);
    }
    /**
     * 网络相关日志
     */
    network(level, message, context) {
        const networkContext = { ...context, component: 'Network' };
        this[level](message, networkContext);
    }
    /**
     * 获取日志文件路径
     */
    getLogFilePath() {
        if (this.initialized && electron_log_1.default.transports.file.getFile) {
            return electron_log_1.default.transports.file.getFile().path;
        }
        return '';
    }
    /**
     * 清理旧日志文件
     */
    async cleanupOldLogs(daysToKeep = 7) {
        try {
            const fs = require('fs').promises;
            const logDir = path.dirname(this.getLogFilePath());
            if (!logDir)
                return;
            const files = await fs.readdir(logDir);
            const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);
            for (const file of files) {
                if (file.endsWith('.log') && file !== 'main.log') {
                    const filePath = path.join(logDir, file);
                    const stats = await fs.stat(filePath);
                    if (stats.mtime.getTime() < cutoffTime) {
                        await fs.unlink(filePath);
                        this.info(`Cleaned up old log file: ${file}`, { component: 'Logger' });
                    }
                }
            }
        }
        catch (error) {
            this.error('Failed to cleanup old logs', error, { component: 'Logger' });
        }
    }
    /**
     * 获取日志统计信息
     */
    getLogStats() {
        const filePath = this.getLogFilePath();
        let fileSize = 0;
        try {
            if (filePath) {
                const fs = require('fs');
                const stats = fs.statSync(filePath);
                fileSize = stats.size;
            }
        }
        catch (error) {
            // 文件不存在或无法访问
        }
        return {
            filePath,
            fileSize,
            isEnabled: this.initialized && (0, config_1.getLoggingConfig)().enableFileLogging
        };
    }
}
exports.Logger = Logger;
/**
 * 全局日志实例
 */
exports.logger = Logger.getInstance();
/**
 * 便捷的日志函数
 */
const logDebug = (message, context) => exports.logger.debug(message, context);
exports.logDebug = logDebug;
const logInfo = (message, context) => exports.logger.info(message, context);
exports.logInfo = logInfo;
const logWarn = (message, context) => exports.logger.warn(message, context);
exports.logWarn = logWarn;
const logError = (message, error, context) => exports.logger.error(message, error, context);
exports.logError = logError;
/**
 * 性能监控装饰器
 */
function logPerformance(operation, context) {
    return function (target, propertyName, descriptor) {
        const method = descriptor.value;
        descriptor.value = async function (...args) {
            const startTime = Date.now();
            try {
                const result = await method.apply(this, args);
                exports.logger.performance(`${operation || propertyName}`, startTime, context);
                return result;
            }
            catch (error) {
                exports.logger.performance(`${operation || propertyName} (failed)`, startTime, context);
                throw error;
            }
        };
        return descriptor;
    };
}
