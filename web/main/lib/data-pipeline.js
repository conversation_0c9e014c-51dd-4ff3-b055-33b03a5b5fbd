"use strict";
/**
 * 🔥 数据转换管道
 *
 * 构建完整的数据流转换链：
 * 爬虫原始数据 → 标准化存储 → AI分析 → 用户导出
 *
 * 这是整个数据格式规范化的核心管道，确保数据在各个环节
 * 都使用正确的格式，消除数据格式混乱问题。
 *
 * 作者: 毒舌界祖师爷的小贱狗 🐕
 * 创建时间: 2025-07-23
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.prepareExportData = exports.processAIResult = exports.prepareAIInput = exports.processRawData = exports.DataPipeline = exports.PipelineStage = void 0;
const data_converters_1 = require("./data-converters");
/**
 * 🎯 管道阶段枚举
 */
var PipelineStage;
(function (PipelineStage) {
    PipelineStage["RAW_DATA"] = "raw_data";
    PipelineStage["STANDARDIZED"] = "standardized";
    PipelineStage["AI_READY"] = "ai_ready";
    PipelineStage["ANALYZED"] = "analyzed";
    PipelineStage["EXPORT_READY"] = "export_ready";
})(PipelineStage || (exports.PipelineStage = PipelineStage = {}));
/**
 * 🔥 数据转换管道核心类
 */
class DataPipeline {
    /**
     * 🚀 完整管道处理：从原始数据到导出就绪
     */
    static processComplete(rawData) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            console.log(`[DataPipeline] 🚀 Starting complete pipeline for task ${rawData.taskId}`);
            const startTime = Date.now();
            try {
                // 阶段1: 原始数据 → 标准化存储
                const standardizedResult = yield this.standardizeRawData(rawData);
                if (!standardizedResult.success) {
                    throw new Error(`Standardization failed: ${(_a = standardizedResult.errors) === null || _a === void 0 ? void 0 : _a.join(', ')}`);
                }
                // 阶段2: 数据验证
                const validation = data_converters_1.DataValidator.validateTaskStorage(standardizedResult.data);
                if (!validation.isValid) {
                    console.warn(`[DataPipeline] ⚠️ Data validation warnings:`, validation.warnings);
                }
                const processingTime = Date.now() - startTime;
                console.log(`[DataPipeline] ✅ Complete pipeline finished in ${processingTime}ms`);
                return {
                    success: true,
                    stage: PipelineStage.EXPORT_READY,
                    data: standardizedResult.data,
                    metadata: {
                        processingTime,
                        dataQuality: validation.dataIntegrityScore,
                        recordCount: standardizedResult.data.discoveredProducts.length + standardizedResult.data.scrapedComments.length,
                        timestamp: new Date().toISOString()
                    },
                    warnings: validation.warnings
                };
            }
            catch (error) {
                console.error(`[DataPipeline] ❌ Complete pipeline failed:`, error);
                return {
                    success: false,
                    stage: PipelineStage.RAW_DATA,
                    data: {},
                    metadata: {
                        processingTime: Date.now() - startTime,
                        dataQuality: 0,
                        recordCount: 0,
                        timestamp: new Date().toISOString()
                    },
                    errors: [error instanceof Error ? error.message : String(error)]
                };
            }
        });
    }
    /**
     * 🔄 阶段1: 原始数据标准化
     */
    static standardizeRawData(rawData) {
        return __awaiter(this, void 0, void 0, function* () {
            console.log(`[DataPipeline] 🔄 Stage 1: Standardizing raw data`);
            const startTime = Date.now();
            try {
                // 转换商品数据
                const standardProducts = this.convertRawLinksToProducts(rawData.discoveredLinks, rawData.keywords);
                // 转换评论数据
                const standardComments = this.convertRawCommentsToStandard(rawData.scrapedComments);
                // 创建标准化存储结构
                const standardData = {
                    taskId: rawData.taskId,
                    version: '1.0.0',
                    basicInfo: {
                        keywords: rawData.keywords,
                        platforms: rawData.platforms,
                        createdAt: rawData.metadata.startTime,
                        updatedAt: rawData.metadata.endTime,
                        status: 'PROCESSING'
                    },
                    discoveredProducts: standardProducts,
                    scrapedComments: standardComments,
                    analysisResults: [], // 初始为空，等待AI分析
                    statistics: {
                        totalProducts: standardProducts.length,
                        totalComments: standardComments.length,
                        successRate: rawData.metadata.successRate,
                        processingTime: Date.now() - new Date(rawData.metadata.startTime).getTime(),
                        dataQualityScore: this.calculateDataQuality(standardProducts, standardComments)
                    },
                    metadata: {
                        dataIntegrity: true,
                        lastValidation: new Date().toISOString(),
                        exportHistory: []
                    }
                };
                const processingTime = Date.now() - startTime;
                console.log(`[DataPipeline] ✅ Stage 1 completed: ${standardProducts.length} products, ${standardComments.length} comments`);
                return {
                    success: true,
                    stage: PipelineStage.STANDARDIZED,
                    data: standardData,
                    metadata: {
                        processingTime,
                        dataQuality: standardData.statistics.dataQualityScore,
                        recordCount: standardProducts.length + standardComments.length,
                        timestamp: new Date().toISOString()
                    }
                };
            }
            catch (error) {
                console.error(`[DataPipeline] ❌ Stage 1 failed:`, error);
                return {
                    success: false,
                    stage: PipelineStage.RAW_DATA,
                    data: null,
                    metadata: {
                        processingTime: Date.now() - startTime,
                        dataQuality: 0,
                        recordCount: 0,
                        timestamp: new Date().toISOString()
                    },
                    errors: [error instanceof Error ? error.message : String(error)]
                };
            }
        });
    }
    /**
     * 🔄 阶段2: 准备AI分析输入
     */
    static prepareAIInput(standardData, analysisType) {
        return __awaiter(this, void 0, void 0, function* () {
            console.log(`[DataPipeline] 🔄 Stage 2: Preparing AI input for ${analysisType}`);
            const startTime = Date.now();
            try {
                let aiInput;
                switch (analysisType) {
                    case 'keyword_analysis':
                        aiInput = data_converters_1.aiInputConverter.convertKeywordAnalysis(standardData.basicInfo.keywords);
                        break;
                    case 'product_discovery':
                        aiInput = data_converters_1.aiInputConverter.convertProductDiscovery(standardData.discoveredProducts, standardData.basicInfo.keywords);
                        break;
                    case 'comment_analysis':
                        aiInput = data_converters_1.aiInputConverter.convertCommentAnalysis(standardData.scrapedComments);
                        break;
                    default:
                        throw new Error(`Unsupported analysis type: ${analysisType}`);
                }
                // 验证AI输入格式
                const validation = data_converters_1.DataValidator.validateAIInput(aiInput);
                if (!validation.isValid) {
                    throw new Error(`AI input validation failed: ${validation.errors.join(', ')}`);
                }
                const processingTime = Date.now() - startTime;
                console.log(`[DataPipeline] ✅ Stage 2 completed: AI input prepared for ${analysisType}`);
                return {
                    success: true,
                    stage: PipelineStage.AI_READY,
                    data: aiInput,
                    metadata: {
                        processingTime,
                        dataQuality: validation.dataIntegrityScore,
                        recordCount: 1, // AI输入是单个对象
                        timestamp: new Date().toISOString()
                    },
                    warnings: validation.warnings
                };
            }
            catch (error) {
                console.error(`[DataPipeline] ❌ Stage 2 failed:`, error);
                return {
                    success: false,
                    stage: PipelineStage.STANDARDIZED,
                    data: null,
                    metadata: {
                        processingTime: Date.now() - startTime,
                        dataQuality: 0,
                        recordCount: 0,
                        timestamp: new Date().toISOString()
                    },
                    errors: [error instanceof Error ? error.message : String(error)]
                };
            }
        });
    }
    /**
     * 🔄 阶段3: 处理AI分析结果
     */
    static processAIResult(standardData, aiResult, analysisType) {
        return __awaiter(this, void 0, void 0, function* () {
            console.log(`[DataPipeline] 🔄 Stage 3: Processing AI result for ${analysisType}`);
            const startTime = Date.now();
            try {
                // 创建标准化分析结果
                const analysisResult = {
                    type: analysisType,
                    input: {
                        keywords: standardData.basicInfo.keywords,
                        products: standardData.discoveredProducts.map(p => p.title),
                        comments: standardData.scrapedComments.map(c => c.content)
                    },
                    output: aiResult,
                    confidence: aiResult.confidence || 0.8,
                    processingTime: Date.now() - startTime,
                    timestamp: new Date().toISOString()
                };
                // 更新标准数据
                const updatedData = Object.assign(Object.assign({}, standardData), { analysisResults: [...standardData.analysisResults, analysisResult], metadata: Object.assign(Object.assign({}, standardData.metadata), { lastValidation: new Date().toISOString() }) });
                const processingTime = Date.now() - startTime;
                console.log(`[DataPipeline] ✅ Stage 3 completed: AI result processed`);
                return {
                    success: true,
                    stage: PipelineStage.ANALYZED,
                    data: updatedData,
                    metadata: {
                        processingTime,
                        dataQuality: standardData.statistics.dataQualityScore,
                        recordCount: updatedData.analysisResults.length,
                        timestamp: new Date().toISOString()
                    }
                };
            }
            catch (error) {
                console.error(`[DataPipeline] ❌ Stage 3 failed:`, error);
                return {
                    success: false,
                    stage: PipelineStage.AI_READY,
                    data: standardData,
                    metadata: {
                        processingTime: Date.now() - startTime,
                        dataQuality: 0,
                        recordCount: 0,
                        timestamp: new Date().toISOString()
                    },
                    errors: [error instanceof Error ? error.message : String(error)]
                };
            }
        });
    }
    /**
     * 🔄 阶段4: 准备导出数据
     */
    static prepareExportData(standardData) {
        return __awaiter(this, void 0, void 0, function* () {
            console.log(`[DataPipeline] 🔄 Stage 4: Preparing export data`);
            const startTime = Date.now();
            try {
                const exportData = data_converters_1.exportConverter.convert(standardData);
                // 验证导出数据
                const validation = data_converters_1.exportConverter.validate(exportData);
                if (!validation) {
                    throw new Error('Export data validation failed');
                }
                const totalRecords = exportData.products.length + exportData.comments.length + exportData.insights.length;
                const processingTime = Date.now() - startTime;
                console.log(`[DataPipeline] ✅ Stage 4 completed: Export data prepared (${totalRecords} records)`);
                return {
                    success: true,
                    stage: PipelineStage.EXPORT_READY,
                    data: exportData,
                    metadata: {
                        processingTime,
                        dataQuality: standardData.statistics.dataQualityScore,
                        recordCount: totalRecords,
                        timestamp: new Date().toISOString()
                    }
                };
            }
            catch (error) {
                console.error(`[DataPipeline] ❌ Stage 4 failed:`, error);
                return {
                    success: false,
                    stage: PipelineStage.ANALYZED,
                    data: null,
                    metadata: {
                        processingTime: Date.now() - startTime,
                        dataQuality: 0,
                        recordCount: 0,
                        timestamp: new Date().toISOString()
                    },
                    errors: [error instanceof Error ? error.message : String(error)]
                };
            }
        });
    }
    /**
     * 转换原始链接为标准商品数据
     */
    static convertRawLinksToProducts(rawLinks, keywords) {
        return rawLinks.map((link, index) => ({
            id: `product_${Date.now()}_${index}`,
            platform: link.platform || 'taobao',
            title: link.title || link.name || `商品_${index + 1}`,
            url: link.url || link.link || '',
            price: link.price ? {
                current: parseFloat(String(link.price).replace(/[^\d.]/g, '')) || 0,
                currency: 'CNY'
            } : undefined,
            sales: link.sales ? {
                count: parseInt(String(link.sales).replace(/[^\d]/g, '')) || 0,
                period: '月销量'
            } : undefined,
            rating: link.rating ? {
                score: parseFloat(String(link.rating)) || 0,
                maxScore: 5,
                reviewCount: link.reviewCount || 0
            } : undefined,
            images: link.images || [],
            category: link.category || '',
            brand: link.brand || '',
            discoveredAt: new Date().toISOString(),
            discoveryKeyword: keywords.join(', ')
        }));
    }
    /**
     * 转换原始评论为标准评论数据
     */
    static convertRawCommentsToStandard(rawComments) {
        return rawComments.map((comment, index) => ({
            id: `comment_${Date.now()}_${index}`,
            platform: comment.platform || 'taobao',
            productId: comment.productId || 'unknown',
            productTitle: comment.productTitle || '未知商品',
            content: comment.content || comment.text || '',
            rating: comment.rating ? {
                score: parseFloat(String(comment.rating)) || 0,
                maxScore: 5
            } : undefined,
            author: comment.author ? {
                name: comment.author.name || '匿名用户',
                level: comment.author.level || '',
                verified: comment.author.verified || false
            } : undefined,
            engagement: {
                likes: comment.likes || 0,
                replies: comment.replies || 0,
                helpful: comment.helpful || 0
            },
            metadata: {
                date: comment.date || new Date().toISOString(),
                isVerifiedPurchase: comment.isVerifiedPurchase || false,
                hasImages: comment.hasImages || false,
                hasVideo: comment.hasVideo || false
            },
            scrapedAt: new Date().toISOString()
        }));
    }
    /**
     * 计算数据质量分数
     */
    static calculateDataQuality(products, comments) {
        let score = 0.5; // 基础分
        // 商品数据质量
        if (products.length > 0) {
            const hasPrice = products.filter(p => p.price).length / products.length;
            const hasRating = products.filter(p => p.rating).length / products.length;
            score += (hasPrice + hasRating) * 0.2;
        }
        // 评论数据质量
        if (comments.length > 0) {
            const hasRating = comments.filter(c => c.rating).length / comments.length;
            const hasAuthor = comments.filter(c => c.author).length / comments.length;
            score += (hasRating + hasAuthor) * 0.15;
        }
        return Math.min(score, 1.0);
    }
    /**
     * 获取管道状态摘要
     */
    static getPipelineStatus(results) {
        const completedStages = results.filter(r => r.success).map(r => r.stage);
        const currentStage = completedStages[completedStages.length - 1] || PipelineStage.RAW_DATA;
        const totalProcessingTime = results.reduce((sum, r) => sum + r.metadata.processingTime, 0);
        const overallQuality = results.length > 0
            ? results.reduce((sum, r) => sum + r.metadata.dataQuality, 0) / results.length
            : 0;
        const totalRecords = results.reduce((sum, r) => sum + r.metadata.recordCount, 0);
        return {
            currentStage,
            completedStages,
            totalProcessingTime,
            overallQuality,
            totalRecords
        };
    }
}
exports.DataPipeline = DataPipeline;
// 导出便捷函数
exports.processRawData = DataPipeline.standardizeRawData;
exports.prepareAIInput = DataPipeline.prepareAIInput;
exports.processAIResult = DataPipeline.processAIResult;
exports.prepareExportData = DataPipeline.prepareExportData;
