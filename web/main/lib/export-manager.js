"use strict";
/**
 * 🔥 真正的数据导出管理器
 *
 * 彻底解决假Excel导出问题，实现真正的：
 * 1. Excel格式导出 (.xlsx) - 使用xlsx库
 * 2. CSV格式导出 (.csv) - 标准CSV格式
 * 3. JSON格式导出 (.json) - 结构化JSON
 *
 * 作者: 毒舌界祖师爷的小贱狗 🐕
 * 创建时间: 2025-07-23
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.batchExportTasks = exports.exportTaskData = exports.ExportManager = void 0;
const XLSX = __importStar(require("xlsx"));
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const electron_1 = require("electron");
const data_format_standards_1 = require("./data-format-standards");
const data_converters_1 = require("./data-converters");
const data_migration_1 = require("./data-migration");
/**
 * 🔥 真正的导出管理器
 */
class ExportManager {
    /**
     * 主导出方法 - 根据格式调用相应的导出器
     */
    static async exportTaskData(task, options) {
        const startTime = Date.now();
        console.log(`[ExportManager] 🚀 Starting ${options.format.toUpperCase()} export for task ${task.id}`);
        try {
            // 获取标准化数据
            const standardData = data_migration_1.DataCompatibilityHelper.getStandardData(task);
            // 转换为导出格式
            const exportData = data_converters_1.exportConverter.convert(standardData);
            // 生成文件名和路径
            const { fileName, filePath } = this.generateFilePaths(task, options);
            let recordCount = 0;
            // 根据格式调用相应的导出方法
            switch (options.format) {
                case 'excel':
                    recordCount = await this.exportToExcel(exportData, filePath, options);
                    break;
                case 'csv':
                    recordCount = await this.exportToCSV(exportData, filePath, options);
                    break;
                case 'json':
                    recordCount = await this.exportToJSON(standardData, filePath, options);
                    break;
                default:
                    throw new Error(`Unsupported export format: ${options.format}`);
            }
            // 获取文件大小
            const stats = fs.statSync(filePath);
            const fileSize = stats.size;
            const processingTime = Date.now() - startTime;
            console.log(`[ExportManager] ✅ ${options.format.toUpperCase()} export completed in ${processingTime}ms`);
            console.log(`[ExportManager] 📁 File: ${fileName} (${this.formatFileSize(fileSize)})`);
            console.log(`[ExportManager] 📊 Records: ${recordCount}`);
            return {
                success: true,
                filePath,
                fileName,
                fileSize,
                recordCount,
                exportTime: new Date().toISOString()
            };
        }
        catch (error) {
            console.error(`[ExportManager] ❌ Export failed:`, error);
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error),
                exportTime: new Date().toISOString()
            };
        }
    }
    /**
     * 🔥 真正的Excel导出 - 使用xlsx库
     */
    static async exportToExcel(exportData, filePath, options) {
        console.log(`[ExportManager] 📊 Creating Excel workbook...`);
        // 创建工作簿
        const workbook = XLSX.utils.book_new();
        let totalRecords = 0;
        // 根据数据类型添加工作表
        if (options.dataType === 'all' || options.dataType === 'products') {
            if (exportData.products.length > 0) {
                const worksheet = XLSX.utils.json_to_sheet(exportData.products);
                XLSX.utils.book_append_sheet(workbook, worksheet, '商品数据');
                totalRecords += exportData.products.length;
                console.log(`[ExportManager] ✅ Added products sheet: ${exportData.products.length} records`);
            }
        }
        if (options.dataType === 'all' || options.dataType === 'comments') {
            if (exportData.comments.length > 0) {
                const worksheet = XLSX.utils.json_to_sheet(exportData.comments);
                XLSX.utils.book_append_sheet(workbook, worksheet, '用户评论');
                totalRecords += exportData.comments.length;
                console.log(`[ExportManager] ✅ Added comments sheet: ${exportData.comments.length} records`);
            }
        }
        if (options.dataType === 'all' || options.dataType === 'insights') {
            if (exportData.insights.length > 0) {
                const worksheet = XLSX.utils.json_to_sheet(exportData.insights);
                XLSX.utils.book_append_sheet(workbook, worksheet, '洞察分析');
                totalRecords += exportData.insights.length;
                console.log(`[ExportManager] ✅ Added insights sheet: ${exportData.insights.length} records`);
            }
        }
        // 添加汇总表
        if (options.includeMetadata !== false) {
            const summaryWorksheet = XLSX.utils.json_to_sheet(exportData.summary);
            XLSX.utils.book_append_sheet(workbook, summaryWorksheet, '数据汇总');
            console.log(`[ExportManager] ✅ Added summary sheet`);
        }
        // 写入文件
        XLSX.writeFile(workbook, filePath);
        console.log(`[ExportManager] 💾 Excel file saved: ${filePath}`);
        return totalRecords;
    }
    /**
     * 🔥 真正的CSV导出 - 标准CSV格式
     */
    static async exportToCSV(exportData, filePath, options) {
        console.log(`[ExportManager] 📄 Creating CSV file...`);
        let csvContent = '';
        let totalRecords = 0;
        // 根据数据类型生成CSV内容
        switch (options.dataType) {
            case 'products':
                csvContent = this.arrayToCSV(exportData.products);
                totalRecords = exportData.products.length;
                break;
            case 'comments':
                csvContent = this.arrayToCSV(exportData.comments);
                totalRecords = exportData.comments.length;
                break;
            case 'insights':
                csvContent = this.arrayToCSV(exportData.insights);
                totalRecords = exportData.insights.length;
                break;
            case 'all':
                // 合并所有数据，添加数据类型标识
                const allData = [
                    ...exportData.products.map(item => ({ ...item, dataType: '商品' })),
                    ...exportData.comments.map(item => ({ ...item, dataType: '评论' })),
                    ...exportData.insights.map(item => ({ ...item, dataType: '洞察' }))
                ];
                csvContent = this.arrayToCSV(allData);
                totalRecords = allData.length;
                break;
        }
        // 写入文件
        fs.writeFileSync(filePath, csvContent, 'utf-8');
        console.log(`[ExportManager] 💾 CSV file saved: ${filePath}`);
        return totalRecords;
    }
    /**
     * 🔥 JSON导出 - 结构化JSON格式
     */
    static async exportToJSON(standardData, filePath, options) {
        console.log(`[ExportManager] 📋 Creating JSON file...`);
        let jsonData;
        let totalRecords = 0;
        // 根据数据类型选择导出内容
        switch (options.dataType) {
            case 'products':
                jsonData = {
                    dataType: 'products',
                    exportTime: new Date().toISOString(),
                    taskId: standardData.taskId,
                    data: standardData.discoveredProducts
                };
                totalRecords = standardData.discoveredProducts.length;
                break;
            case 'comments':
                jsonData = {
                    dataType: 'comments',
                    exportTime: new Date().toISOString(),
                    taskId: standardData.taskId,
                    data: standardData.scrapedComments
                };
                totalRecords = standardData.scrapedComments.length;
                break;
            case 'insights':
                jsonData = {
                    dataType: 'insights',
                    exportTime: new Date().toISOString(),
                    taskId: standardData.taskId,
                    data: standardData.analysisResults
                };
                totalRecords = standardData.analysisResults.length;
                break;
            case 'all':
                jsonData = {
                    dataType: 'all',
                    exportTime: new Date().toISOString(),
                    taskInfo: standardData.basicInfo,
                    products: standardData.discoveredProducts,
                    comments: standardData.scrapedComments,
                    insights: standardData.analysisResults,
                    statistics: standardData.statistics,
                    metadata: standardData.metadata
                };
                totalRecords = standardData.discoveredProducts.length +
                    standardData.scrapedComments.length +
                    standardData.analysisResults.length;
                break;
        }
        // 写入文件
        const jsonContent = JSON.stringify(jsonData, null, 2);
        fs.writeFileSync(filePath, jsonContent, 'utf-8');
        console.log(`[ExportManager] 💾 JSON file saved: ${filePath}`);
        return totalRecords;
    }
    /**
     * 数组转CSV格式
     */
    static arrayToCSV(data) {
        if (data.length === 0)
            return '';
        // 获取所有字段名
        const headers = Object.keys(data[0]);
        // 生成CSV头部
        const csvHeaders = headers.map(header => `"${header}"`).join(',');
        // 生成CSV数据行
        const csvRows = data.map(row => {
            return headers.map(header => {
                const value = row[header];
                // 处理特殊字符和换行符
                const stringValue = String(value || '').replace(/"/g, '""');
                return `"${stringValue}"`;
            }).join(',');
        });
        return [csvHeaders, ...csvRows].join('\n');
    }
    /**
     * 生成文件名和路径
     */
    static generateFilePaths(task, options) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        const keywords = task.keywords.slice(0, 3).join('-').replace(/[^\w\u4e00-\u9fa5-]/g, '_');
        const fileName = options.customFileName ||
            `${keywords}-${options.dataType}-${timestamp}.${options.format === 'excel' ? 'xlsx' : options.format}`;
        const outputDir = options.outputPath || electron_1.app.getPath('downloads');
        const filePath = path.join(outputDir, fileName);
        return { fileName, filePath };
    }
    /**
     * 格式化文件大小
     */
    static formatFileSize(bytes) {
        if (bytes === 0)
            return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    /**
     * 批量导出多个任务
     */
    static async batchExport(tasks, options) {
        console.log(`[ExportManager] 🔄 Starting batch export for ${tasks.length} tasks`);
        const results = [];
        for (const task of tasks) {
            const result = await this.exportTaskData(task, {
                ...options,
                customFileName: `batch-${task.id}-${options.dataType}.${options.format === 'excel' ? 'xlsx' : options.format}`
            });
            results.push(result);
        }
        const successCount = results.filter(r => r.success).length;
        console.log(`[ExportManager] ✅ Batch export completed: ${successCount}/${tasks.length} successful`);
        return results;
    }
    /**
     * 获取支持的导出格式
     */
    static getSupportedFormats() {
        return data_format_standards_1.SUPPORTED_EXPORT_FORMATS;
    }
    /**
     * 获取支持的数据类型
     */
    static getSupportedDataTypes() {
        return data_format_standards_1.SUPPORTED_DATA_TYPES;
    }
    /**
     * 验证导出选项
     */
    static validateExportOptions(options) {
        const errors = [];
        if (!data_format_standards_1.SUPPORTED_EXPORT_FORMATS.includes(options.format)) {
            errors.push(`不支持的导出格式: ${options.format}`);
        }
        if (!data_format_standards_1.SUPPORTED_DATA_TYPES.includes(options.dataType)) {
            errors.push(`不支持的数据类型: ${options.dataType}`);
        }
        return {
            valid: errors.length === 0,
            errors
        };
    }
}
exports.ExportManager = ExportManager;
// 导出便捷函数
exports.exportTaskData = ExportManager.exportTaskData;
exports.batchExportTasks = ExportManager.batchExport;
