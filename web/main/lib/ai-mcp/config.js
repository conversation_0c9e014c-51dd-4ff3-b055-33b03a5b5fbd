"use strict";
/**
 * V3.0 AI+MCP 爬虫系统 - 配置管理
 *
 * 🔥 重构说明：
 * 1. 集成统一API配置管理器，消除硬编码
 * 2. 所有API配置通过函数传参，不再直接访问配置对象
 * 3. 保持向后兼容性，逐步迁移到新的配置系统
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getMCPServerConfig = exports.getAgentConfig = exports.getAIMCPConfig = exports.aiMCPConfig = exports.AIMCPConfigManager = exports.DEFAULT_AI_MCP_CONFIG = void 0;
/**
 * 默认AI+MCP配置
 */
exports.DEFAULT_AI_MCP_CONFIG = {
    agent: {
        // 不再硬编码，完全依赖外部配置
        apiKey: '', // 将通过配置文件或环境变量提供
        baseURL: '', // 将通过配置文件或环境变量提供
        model: '', // 将通过配置文件或环境变量提供
        // AI行为配置
        maxTokens: 4000,
        temperature: 0.3, // 较低的温度确保更稳定的决策
        // 任务配置
        maxRetries: 3,
        retryDelay: 2000,
    },
    mcpServer: {
        name: 'demand-insight-scraping-server',
        version: '3.0.0',
        capabilities: {
            tools: true,
            resources: false,
            prompts: false,
        },
        transport: {
            type: 'stdio',
        },
    },
    tools: {
        executionTimeout: 60000, // 60秒
        maxRetries: 3,
        retryDelay: 1000,
        maxConcurrentCalls: 5,
    },
    monitoring: {
        enabled: true,
        interval: 10000, // 10秒
        thresholds: {
            responseTime: 30000, // 30秒
            memoryUsage: 1024, // 1GB
            errorRate: 10, // 10%
        },
    },
    logging: {
        level: 'info',
        enableAIDecisionLogs: true,
        enableToolCallLogs: true,
        logFilePath: './logs/ai-mcp.log',
    },
};
/**
 * AI+MCP配置管理器
 */
class AIMCPConfigManager {
    constructor() {
        this.multiModelConfig = null;
        this.config = Object.assign({}, exports.DEFAULT_AI_MCP_CONFIG);
        this.loadConfiguration();
    }
    static getInstance() {
        if (!AIMCPConfigManager.instance) {
            AIMCPConfigManager.instance = new AIMCPConfigManager();
        }
        return AIMCPConfigManager.instance;
    }
    /**
     * 统一配置加载方法
     */
    loadConfiguration() {
        // 1. 首先尝试从配置文件加载
        this.loadFromConfigFile();
        // 2. 然后从环境变量覆盖
        this.loadFromEnvironment();
        // 3. 最后验证配置完整性
        this.validateConfiguration();
    }
    /**
     * 从配置文件加载多模型配置
     */
    loadFromConfigFile() {
        try {
            const os = require('os');
            const path = require('path');
            const fs = require('fs');
            // 支持多个配置文件路径
            const configPaths = [
                path.join(os.homedir(), 'Library/Application Support/demand-insight-assistant/.insight.config.json'),
                path.join(process.cwd(), '.insight.config.json'),
                path.join(process.cwd(), 'config/ai-config.json')
            ];
            for (const configPath of configPaths) {
                if (fs.existsSync(configPath)) {
                    const configData = JSON.parse(fs.readFileSync(configPath, 'utf-8'));
                    // 加载多模型配置
                    if (configData.multiModel) {
                        this.multiModelConfig = configData.multiModel;
                        console.log(`[AIMCPConfig] ✅ Multi-model configuration loaded from: ${configPath}`);
                    }
                    // 加载单一配置（向后兼容）
                    if (configData.apiKey && configData.modelId) {
                        this.config.agent.apiKey = configData.apiKey;
                        this.config.agent.model = configData.modelId;
                        // 确保baseURL正确设置
                        this.config.agent.baseURL = configData.endpoint || configData.baseURL || 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
                        console.log(`[AIMCPConfig] ✅ Legacy configuration loaded from: ${configPath}`);
                        console.log(`[AIMCPConfig] 🔧 API Config: baseURL=${this.config.agent.baseURL}, model=${this.config.agent.model}`);
                    }
                    break; // 使用第一个找到的配置文件
                }
            }
        }
        catch (error) {
            console.warn(`[AIMCPConfig] ⚠️ Failed to load configuration file: ${error}`);
        }
    }
    /**
     * 从环境变量加载配置
     */
    loadFromEnvironment() {
        // 通用AI配置
        if (process.env.AI_API_KEY) {
            this.config.agent.apiKey = process.env.AI_API_KEY;
        }
        if (process.env.AI_BASE_URL) {
            this.config.agent.baseURL = process.env.AI_BASE_URL;
        }
        if (process.env.AI_MODEL) {
            this.config.agent.model = process.env.AI_MODEL;
        }
        // 火山引擎配置（向后兼容）
        if (process.env.VOLCENGINE_API_KEY) {
            this.config.agent.apiKey = process.env.VOLCENGINE_API_KEY;
        }
        if (process.env.VOLCENGINE_BASE_URL) {
            this.config.agent.baseURL = process.env.VOLCENGINE_BASE_URL;
        }
        if (process.env.VOLCENGINE_MODEL) {
            this.config.agent.model = process.env.VOLCENGINE_MODEL;
        }
        // AI参数配置
        if (process.env.AI_MAX_TOKENS) {
            this.config.agent.maxTokens = parseInt(process.env.AI_MAX_TOKENS);
        }
        if (process.env.AI_TEMPERATURE) {
            this.config.agent.temperature = parseFloat(process.env.AI_TEMPERATURE);
        }
        // 日志配置
        if (process.env.AI_LOG_LEVEL) {
            this.config.logging.level = process.env.AI_LOG_LEVEL;
        }
        console.log('[AIMCPConfig] ✅ Configuration loaded from environment');
    }
    /**
     * 验证配置完整性
     */
    validateConfiguration() {
        const validation = this.validateConfig();
        if (!validation.isValid) {
            console.error('[AIMCPConfig] ❌ Configuration validation failed:', validation.errors);
            throw new Error(`Configuration validation failed: ${validation.errors.join(', ')}`);
        }
    }
    /**
     * 获取完整配置
     */
    getConfig() {
        return Object.assign({}, this.config);
    }
    /**
     * 获取AI代理配置
     */
    getAgentConfig() {
        return Object.assign({}, this.config.agent);
    }
    /**
     * 获取MCP服务器配置
     */
    getMCPServerConfig() {
        return Object.assign({}, this.config.mcpServer);
    }
    /**
     * 更新AI代理配置
     */
    updateAgentConfig(updates) {
        this.config.agent = Object.assign(Object.assign({}, this.config.agent), updates);
        console.log('[AIMCPConfig] 🔄 Agent configuration updated');
    }
    /**
     * 更新工具配置
     */
    updateToolsConfig(updates) {
        this.config.tools = Object.assign(Object.assign({}, this.config.tools), updates);
        console.log('[AIMCPConfig] 🔄 Tools configuration updated');
    }
    /**
     * 获取多模型配置
     */
    getMultiModelConfig() {
        return this.multiModelConfig;
    }
    /**
     * 设置多模型配置
     */
    setMultiModelConfig(config) {
        this.multiModelConfig = config;
        console.log('[AIMCPConfig] 🔄 Multi-model configuration updated');
    }
    /**
     * 根据用途获取AI配置
     */
    getAIConfigForPurpose(purpose) {
        if (this.multiModelConfig && this.multiModelConfig[purpose]) {
            return this.multiModelConfig[purpose];
        }
        // 回退到默认配置
        return {
            apiKey: this.config.agent.apiKey,
            baseURL: this.config.agent.baseURL,
            model: this.config.agent.model,
            maxTokens: this.config.agent.maxTokens,
            temperature: this.config.agent.temperature,
            enabled: true
        };
    }
    /**
     * 验证配置有效性
     */
    validateConfig() {
        const errors = [];
        // 验证火山API配置
        if (!this.config.agent.apiKey) {
            errors.push('VOLCENGINE_API_KEY is required');
        }
        if (!this.config.agent.baseURL) {
            errors.push('VOLCENGINE_BASE_URL is required');
        }
        if (!this.config.agent.model) {
            errors.push('VOLCENGINE_MODEL is required');
        }
        // 验证数值配置
        if (this.config.agent.maxTokens <= 0) {
            errors.push('maxTokens must be greater than 0');
        }
        if (this.config.agent.temperature < 0 || this.config.agent.temperature > 2) {
            errors.push('temperature must be between 0 and 2');
        }
        if (this.config.tools.executionTimeout <= 0) {
            errors.push('executionTimeout must be greater than 0');
        }
        if (this.config.tools.maxRetries < 0) {
            errors.push('maxRetries must be non-negative');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    /**
     * 从JSON文件加载配置
     */
    loadFromFile(configPath) {
        try {
            const fs = require('fs');
            if (fs.existsSync(configPath)) {
                const fileConfig = JSON.parse(fs.readFileSync(configPath, 'utf-8'));
                this.config = Object.assign(Object.assign({}, exports.DEFAULT_AI_MCP_CONFIG), fileConfig);
                console.log(`[AIMCPConfig] ✅ Configuration loaded from file: ${configPath}`);
            }
        }
        catch (error) {
            console.error(`[AIMCPConfig] ❌ Failed to load configuration from file: ${error}`);
            console.log(`[AIMCPConfig] 🔄 Using default configuration`);
        }
    }
    /**
     * 保存配置到JSON文件
     */
    saveToFile(configPath) {
        try {
            const fs = require('fs');
            const path = require('path');
            // 确保目录存在
            const dir = path.dirname(configPath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
            // 保存配置（排除敏感信息）
            const configToSave = Object.assign({}, this.config);
            configToSave.agent.apiKey = '[REDACTED]'; // 不保存API密钥
            fs.writeFileSync(configPath, JSON.stringify(configToSave, null, 2));
            console.log(`[AIMCPConfig] ✅ Configuration saved to file: ${configPath}`);
        }
        catch (error) {
            console.error(`[AIMCPConfig] ❌ Failed to save configuration to file: ${error}`);
        }
    }
    /**
     * 重置为默认配置
     */
    reset() {
        this.config = Object.assign({}, exports.DEFAULT_AI_MCP_CONFIG);
        this.loadFromEnvironment();
        console.log('[AIMCPConfig] 🔄 Configuration reset to defaults');
    }
    /**
     * 获取环境变量配置示例
     */
    getEnvironmentExample() {
        return `
# 火山引擎API配置
VOLCENGINE_API_KEY=your_api_key_here
VOLCENGINE_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
VOLCENGINE_MODEL=ep-20241220161543-8xqzr

# AI参数配置
AI_MAX_TOKENS=4000
AI_TEMPERATURE=0.3

# 日志配置
AI_LOG_LEVEL=info
    `.trim();
    }
}
exports.AIMCPConfigManager = AIMCPConfigManager;
/**
 * 全局配置实例
 */
exports.aiMCPConfig = AIMCPConfigManager.getInstance();
/**
 * 便捷的配置访问函数
 */
const getAIMCPConfig = () => exports.aiMCPConfig.getConfig();
exports.getAIMCPConfig = getAIMCPConfig;
const getAgentConfig = () => exports.aiMCPConfig.getAgentConfig();
exports.getAgentConfig = getAgentConfig;
const getMCPServerConfig = () => exports.aiMCPConfig.getMCPServerConfig();
exports.getMCPServerConfig = getMCPServerConfig;
