"use strict";
/**
 * V3.0 AI+MCP 爬虫系统 - 实时性能监控器
 *
 * 这个文件实现了实时性能监控和自适应优化系统，
 * 通过监控系统负载、执行效果等指标，自动调整策略参数。
 *
 * 核心功能：
 * 1. 实时性能指标收集
 * 2. 系统负载监控
 * 3. 自适应参数调整
 * 4. 性能预警和报告
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceMonitor = void 0;
/**
 * 实时性能监控器
 */
class PerformanceMonitor {
    constructor(thresholds) {
        this.metrics = [];
        this.alerts = [];
        this.isMonitoring = false;
        this.monitoringInterval = null;
        this.maxMetricsHistory = 1000;
        this.adaptiveAdjustments = [];
        this.thresholds = Object.assign({ system: {
                maxCpuUsage: 80,
                maxMemoryUsage: 85,
                maxNetworkLatency: 5000,
            }, application: {
                maxResponseTime: 30000,
                minSuccessRate: 0.8,
                maxQueueSize: 100,
            }, ai: {
                maxDecisionTime: 10000,
                minCacheHitRate: 0.3,
                maxModelLatency: 5000,
            }, scraping: {
                minLinksPerMinute: 10,
                maxErrorRate: 0.2,
            } }, thresholds);
    }
    /**
     * 开始监控
     */
    startMonitoring(intervalMs = 10000) {
        if (this.isMonitoring) {
            console.warn('[PerformanceMonitor] ⚠️ Monitoring is already running');
            return;
        }
        console.log(`[PerformanceMonitor] 🚀 Starting performance monitoring (interval: ${intervalMs}ms)`);
        this.isMonitoring = true;
        this.monitoringInterval = setInterval(() => {
            this.collectMetrics();
        }, intervalMs);
    }
    /**
     * 停止监控
     */
    stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }
        console.log('[PerformanceMonitor] 🛑 Stopping performance monitoring');
        this.isMonitoring = false;
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
    }
    /**
     * 收集性能指标
     */
    collectMetrics() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const metrics = {
                    timestamp: new Date().toISOString(),
                    system: yield this.collectSystemMetrics(),
                    application: yield this.collectApplicationMetrics(),
                    ai: yield this.collectAIMetrics(),
                    scraping: yield this.collectScrapingMetrics(),
                };
                this.metrics.push(metrics);
                // 限制历史记录数量
                if (this.metrics.length > this.maxMetricsHistory) {
                    this.metrics = this.metrics.slice(-this.maxMetricsHistory);
                }
                // 检查阈值和生成警报
                this.checkThresholds(metrics);
                // 生成自适应调整建议
                this.generateAdaptiveAdjustments(metrics);
                console.log(`[PerformanceMonitor] 📊 Metrics collected: CPU ${metrics.system.cpuUsage}%, Memory ${metrics.system.memoryUsage}%, Success Rate ${Math.round(metrics.application.successRate * 100)}%`);
            }
            catch (error) {
                console.error('[PerformanceMonitor] ❌ Error collecting metrics:', error);
            }
        });
    }
    /**
     * 收集系统指标
     */
    collectSystemMetrics() {
        return __awaiter(this, void 0, void 0, function* () {
            // 获取系统资源使用情况
            const memoryUsage = process.memoryUsage();
            const totalMemory = memoryUsage.heapTotal + memoryUsage.external;
            const usedMemory = memoryUsage.heapUsed;
            return {
                cpuUsage: yield this.getCPUUsage(),
                memoryUsage: Math.round((usedMemory / totalMemory) * 100),
                networkLatency: yield this.measureNetworkLatency(),
                diskIO: 0 // 简化实现
            };
        });
    }
    /**
     * 收集应用指标
     */
    collectApplicationMetrics() {
        return __awaiter(this, void 0, void 0, function* () {
            // 这里需要从实际的应用状态中获取数据
            // 目前使用模拟数据
            return {
                activeWorkers: 5,
                queuedTasks: 2,
                completedTasks: 150,
                failedTasks: 8,
                averageResponseTime: 2500,
                successRate: 0.95
            };
        });
    }
    /**
     * 收集AI指标
     */
    collectAIMetrics() {
        return __awaiter(this, void 0, void 0, function* () {
            // 这里需要从AI Agent中获取实际数据
            return {
                tokensPerSecond: 25,
                averageDecisionTime: 3000,
                cacheHitRate: 0.65,
                modelLatency: 1500
            };
        });
    }
    /**
     * 收集爬虫指标
     */
    collectScrapingMetrics() {
        return __awaiter(this, void 0, void 0, function* () {
            return {
                linksPerMinute: 45,
                commentsPerMinute: 120,
                errorRate: 0.05,
                platformLatency: {
                    taobao: 2000,
                    xiaohongshu: 1800
                }
            };
        });
    }
    /**
     * 获取CPU使用率
     */
    getCPUUsage() {
        return __awaiter(this, void 0, void 0, function* () {
            // 简化的CPU使用率计算
            const startUsage = process.cpuUsage();
            yield new Promise(resolve => setTimeout(resolve, 100));
            const endUsage = process.cpuUsage(startUsage);
            const totalUsage = endUsage.user + endUsage.system;
            const percentage = (totalUsage / 100000) * 100; // 转换为百分比
            return Math.min(100, Math.max(0, percentage));
        });
    }
    /**
     * 测量网络延迟
     */
    measureNetworkLatency() {
        return __awaiter(this, void 0, void 0, function* () {
            const start = Date.now();
            try {
                // 简单的网络延迟测试
                yield fetch('https://www.baidu.com', {
                    method: 'HEAD',
                    signal: AbortSignal.timeout(5000)
                });
                return Date.now() - start;
            }
            catch (error) {
                return 5000; // 超时或错误时返回5秒
            }
        });
    }
    /**
     * 检查阈值并生成警报
     */
    checkThresholds(metrics) {
        const alerts = [];
        // 检查系统指标
        if (metrics.system.cpuUsage > this.thresholds.system.maxCpuUsage) {
            alerts.push(this.createAlert('high', 'system', 'cpuUsage', metrics.system.cpuUsage, this.thresholds.system.maxCpuUsage, 'CPU使用率过高', ['降低并发数', '优化算法', '增加服务器资源']));
        }
        if (metrics.system.memoryUsage > this.thresholds.system.maxMemoryUsage) {
            alerts.push(this.createAlert('high', 'system', 'memoryUsage', metrics.system.memoryUsage, this.thresholds.system.maxMemoryUsage, '内存使用率过高', ['清理缓存', '优化内存使用', '重启服务']));
        }
        // 检查应用指标
        if (metrics.application.successRate < this.thresholds.application.minSuccessRate) {
            alerts.push(this.createAlert('critical', 'application', 'successRate', metrics.application.successRate, this.thresholds.application.minSuccessRate, '任务成功率过低', ['检查网络连接', '调整重试策略', '更新爬虫逻辑']));
        }
        // 检查AI指标
        if (metrics.ai.averageDecisionTime > this.thresholds.ai.maxDecisionTime) {
            alerts.push(this.createAlert('medium', 'ai', 'averageDecisionTime', metrics.ai.averageDecisionTime, this.thresholds.ai.maxDecisionTime, 'AI决策时间过长', ['优化提示词', '增加缓存', '调整模型参数']));
        }
        // 检查爬虫指标
        if (metrics.scraping.errorRate > this.thresholds.scraping.maxErrorRate) {
            alerts.push(this.createAlert('medium', 'scraping', 'errorRate', metrics.scraping.errorRate, this.thresholds.scraping.maxErrorRate, '爬虫错误率过高', ['检查目标网站变化', '更新选择器', '调整访问频率']));
        }
        // 添加新警报
        this.alerts.push(...alerts);
        // 限制警报历史数量
        if (this.alerts.length > 100) {
            this.alerts = this.alerts.slice(-100);
        }
        if (alerts.length > 0) {
            console.warn(`[PerformanceMonitor] ⚠️ Generated ${alerts.length} new alerts`);
        }
    }
    /**
     * 创建警报
     */
    createAlert(severity, category, metric, currentValue, threshold, message, suggestions) {
        return {
            id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            timestamp: new Date().toISOString(),
            severity,
            category,
            metric,
            currentValue,
            threshold,
            message,
            suggestions
        };
    }
    /**
     * 生成自适应调整建议
     */
    generateAdaptiveAdjustments(metrics) {
        const adjustments = [];
        // 基于CPU使用率调整并发数
        if (metrics.system.cpuUsage > 70) {
            adjustments.push({
                type: 'concurrency',
                parameter: 'maxConcurrentWorkers',
                currentValue: 5,
                suggestedValue: 3,
                reason: 'CPU使用率过高，建议降低并发数',
                confidence: 0.8,
                expectedImprovement: 15
            });
        }
        else if (metrics.system.cpuUsage < 30 && metrics.application.successRate > 0.9) {
            adjustments.push({
                type: 'concurrency',
                parameter: 'maxConcurrentWorkers',
                currentValue: 5,
                suggestedValue: 7,
                reason: 'CPU使用率较低且成功率高，可以增加并发数',
                confidence: 0.7,
                expectedImprovement: 20
            });
        }
        // 基于成功率调整超时时间
        if (metrics.application.successRate < 0.8) {
            adjustments.push({
                type: 'timeout',
                parameter: 'requestTimeout',
                currentValue: 30000,
                suggestedValue: 45000,
                reason: '成功率较低，建议增加超时时间',
                confidence: 0.6,
                expectedImprovement: 10
            });
        }
        // 基于缓存命中率调整缓存策略
        if (metrics.ai.cacheHitRate < 0.3) {
            adjustments.push({
                type: 'cache',
                parameter: 'cacheSize',
                currentValue: 1000,
                suggestedValue: 1500,
                reason: '缓存命中率较低，建议增加缓存大小',
                confidence: 0.7,
                expectedImprovement: 25
            });
        }
        // 基于AI决策时间调整参数
        if (metrics.ai.averageDecisionTime > 5000) {
            adjustments.push({
                type: 'ai_params',
                parameter: 'temperature',
                currentValue: 0.3,
                suggestedValue: 0.1,
                reason: 'AI决策时间过长，建议降低temperature以减少随机性',
                confidence: 0.5,
                expectedImprovement: 30
            });
        }
        // 更新调整建议
        this.adaptiveAdjustments = adjustments;
        if (adjustments.length > 0) {
            console.log(`[PerformanceMonitor] 🔧 Generated ${adjustments.length} adaptive adjustments`);
        }
    }
    /**
     * 获取当前性能指标
     */
    getCurrentMetrics() {
        return this.metrics.length > 0 ? this.metrics[this.metrics.length - 1] : null;
    }
    /**
     * 获取性能历史
     */
    getMetricsHistory(limit) {
        const history = [...this.metrics];
        return limit ? history.slice(-limit) : history;
    }
    /**
     * 获取活跃警报
     */
    getActiveAlerts() {
        const oneHourAgo = Date.now() - 60 * 60 * 1000;
        return this.alerts.filter(alert => new Date(alert.timestamp).getTime() > oneHourAgo);
    }
    /**
     * 获取自适应调整建议
     */
    getAdaptiveAdjustments() {
        return [...this.adaptiveAdjustments];
    }
    /**
     * 应用自适应调整
     */
    applyAdjustment(adjustmentId) {
        // 这里需要实际应用调整到系统中
        console.log(`[PerformanceMonitor] 🔧 Applied adjustment: ${adjustmentId}`);
        return true;
    }
    /**
     * 生成性能报告
     */
    generatePerformanceReport() {
        if (this.metrics.length === 0) {
            return {
                summary: {},
                trends: {},
                recommendations: ['开始监控以获取性能数据']
            };
        }
        const recent = this.metrics.slice(-10);
        const summary = {
            avgCpuUsage: recent.reduce((sum, m) => sum + m.system.cpuUsage, 0) / recent.length,
            avgMemoryUsage: recent.reduce((sum, m) => sum + m.system.memoryUsage, 0) / recent.length,
            avgSuccessRate: recent.reduce((sum, m) => sum + m.application.successRate, 0) / recent.length,
            avgResponseTime: recent.reduce((sum, m) => sum + m.application.averageResponseTime, 0) / recent.length,
            totalAlerts: this.getActiveAlerts().length,
            totalAdjustments: this.adaptiveAdjustments.length
        };
        const trends = {
            cpuTrend: this.calculateTrend(recent.map(m => m.system.cpuUsage)),
            memoryTrend: this.calculateTrend(recent.map(m => m.system.memoryUsage)),
            successRateTrend: this.calculateTrend(recent.map(m => m.application.successRate))
        };
        const recommendations = this.generateRecommendations(summary, trends);
        return { summary, trends, recommendations };
    }
    /**
     * 计算趋势
     */
    calculateTrend(values) {
        if (values.length < 2)
            return 'stable';
        const first = values.slice(0, Math.floor(values.length / 2));
        const second = values.slice(Math.floor(values.length / 2));
        const firstAvg = first.reduce((sum, v) => sum + v, 0) / first.length;
        const secondAvg = second.reduce((sum, v) => sum + v, 0) / second.length;
        const diff = (secondAvg - firstAvg) / firstAvg;
        if (diff > 0.1)
            return 'increasing';
        if (diff < -0.1)
            return 'decreasing';
        return 'stable';
    }
    /**
     * 生成建议
     */
    generateRecommendations(summary, trends) {
        const recommendations = [];
        if (summary.avgCpuUsage > 70) {
            recommendations.push('CPU使用率较高，考虑优化算法或增加服务器资源');
        }
        if (summary.avgSuccessRate < 0.8) {
            recommendations.push('任务成功率偏低，建议检查网络连接和目标网站状态');
        }
        if (trends.cpuTrend === 'increasing') {
            recommendations.push('CPU使用率呈上升趋势，需要关注系统负载');
        }
        if (this.adaptiveAdjustments.length > 0) {
            recommendations.push(`有${this.adaptiveAdjustments.length}个自适应调整建议待应用`);
        }
        return recommendations;
    }
    /**
     * 清理历史数据
     */
    cleanup() {
        const oneWeekAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;
        this.metrics = this.metrics.filter(metric => new Date(metric.timestamp).getTime() > oneWeekAgo);
        this.alerts = this.alerts.filter(alert => new Date(alert.timestamp).getTime() > oneWeekAgo);
        console.log('[PerformanceMonitor] 🧹 Cleaned up old performance data');
    }
}
exports.PerformanceMonitor = PerformanceMonitor;
