"use strict";
/**
 * V3.0 AI+MCP 爬虫系统 - 统一错误处理系统
 *
 * 这个文件提供了完整的错误处理机制，
 * 包括错误分类、处理策略和恢复机制。
 *
 * 核心功能：
 * 1. 错误分类和标准化
 * 2. 错误处理策略
 * 3. 错误恢复机制
 * 4. 错误统计和分析
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnifiedErrorHandler = exports.ErrorSeverity = exports.ErrorType = void 0;
exports.HandleErrors = HandleErrors;
/**
 * 错误类型枚举
 */
var ErrorType;
(function (ErrorType) {
    // 系统错误
    ErrorType["SYSTEM_ERROR"] = "SYSTEM_ERROR";
    ErrorType["INITIALIZATION_ERROR"] = "INITIALIZATION_ERROR";
    ErrorType["CONFIGURATION_ERROR"] = "CONFIGURATION_ERROR";
    // 网络错误
    ErrorType["NETWORK_ERROR"] = "NETWORK_ERROR";
    ErrorType["TIMEOUT_ERROR"] = "TIMEOUT_ERROR";
    ErrorType["CONNECTION_ERROR"] = "CONNECTION_ERROR";
    // AI相关错误
    ErrorType["AI_API_ERROR"] = "AI_API_ERROR";
    ErrorType["AI_QUOTA_EXCEEDED"] = "AI_QUOTA_EXCEEDED";
    ErrorType["AI_MODEL_ERROR"] = "AI_MODEL_ERROR";
    // 爬虫错误
    ErrorType["SCRAPING_ERROR"] = "SCRAPING_ERROR";
    ErrorType["CAPTCHA_ERROR"] = "CAPTCHA_ERROR";
    ErrorType["LOGIN_REQUIRED"] = "LOGIN_REQUIRED";
    ErrorType["RATE_LIMITED"] = "RATE_LIMITED";
    // 数据错误
    ErrorType["DATA_VALIDATION_ERROR"] = "DATA_VALIDATION_ERROR";
    ErrorType["DATA_PARSING_ERROR"] = "DATA_PARSING_ERROR";
    ErrorType["DATA_QUALITY_ERROR"] = "DATA_QUALITY_ERROR";
    // 业务错误
    ErrorType["TASK_ERROR"] = "TASK_ERROR";
    ErrorType["USER_ERROR"] = "USER_ERROR";
    ErrorType["PERMISSION_ERROR"] = "PERMISSION_ERROR";
    // 未知错误
    ErrorType["UNKNOWN_ERROR"] = "UNKNOWN_ERROR";
})(ErrorType || (exports.ErrorType = ErrorType = {}));
/**
 * 错误严重程度
 */
var ErrorSeverity;
(function (ErrorSeverity) {
    ErrorSeverity["LOW"] = "low";
    ErrorSeverity["MEDIUM"] = "medium";
    ErrorSeverity["HIGH"] = "high";
    ErrorSeverity["CRITICAL"] = "critical";
})(ErrorSeverity || (exports.ErrorSeverity = ErrorSeverity = {}));
/**
 * 统一错误处理器
 */
class UnifiedErrorHandler {
    constructor() {
        this.errorHistory = [];
        this.errorStrategies = new Map();
        this.maxHistorySize = 1000;
        this.errorCallbacks = new Map();
        this.initializeErrorStrategies();
    }
    /**
     * 获取单例实例
     */
    static getInstance() {
        if (!UnifiedErrorHandler.instance) {
            UnifiedErrorHandler.instance = new UnifiedErrorHandler();
        }
        return UnifiedErrorHandler.instance;
    }
    /**
     * 初始化错误处理策略
     */
    initializeErrorStrategies() {
        // 系统错误策略
        this.errorStrategies.set(ErrorType.SYSTEM_ERROR, {
            type: ErrorType.SYSTEM_ERROR,
            maxRetries: 0,
            retryDelay: 0,
            escalationThreshold: 1,
            notificationRequired: true,
            autoRecover: false
        });
        // 网络错误策略
        this.errorStrategies.set(ErrorType.NETWORK_ERROR, {
            type: ErrorType.NETWORK_ERROR,
            maxRetries: 3,
            retryDelay: 2000,
            escalationThreshold: 5,
            notificationRequired: false,
            autoRecover: true
        });
        // AI API错误策略
        this.errorStrategies.set(ErrorType.AI_API_ERROR, {
            type: ErrorType.AI_API_ERROR,
            maxRetries: 2,
            retryDelay: 5000,
            escalationThreshold: 3,
            notificationRequired: true,
            autoRecover: true
        });
        // 爬虫错误策略
        this.errorStrategies.set(ErrorType.SCRAPING_ERROR, {
            type: ErrorType.SCRAPING_ERROR,
            maxRetries: 2,
            retryDelay: 3000,
            escalationThreshold: 10,
            notificationRequired: false,
            autoRecover: true
        });
        // 验证码错误策略
        this.errorStrategies.set(ErrorType.CAPTCHA_ERROR, {
            type: ErrorType.CAPTCHA_ERROR,
            maxRetries: 1,
            retryDelay: 30000,
            escalationThreshold: 1,
            notificationRequired: true,
            autoRecover: false
        });
        // 限流错误策略
        this.errorStrategies.set(ErrorType.RATE_LIMITED, {
            type: ErrorType.RATE_LIMITED,
            maxRetries: 3,
            retryDelay: 60000,
            escalationThreshold: 5,
            notificationRequired: false,
            autoRecover: true
        });
    }
    /**
     * 处理错误
     */
    handleError(error, context) {
        return __awaiter(this, void 0, void 0, function* () {
            // 标准化错误
            const standardError = this.standardizeError(error, context);
            // 记录错误
            this.recordError(standardError);
            // 执行错误处理策略
            yield this.executeErrorStrategy(standardError);
            // 触发错误回调
            this.triggerErrorCallbacks(standardError);
            return standardError;
        });
    }
    /**
     * 标准化错误
     */
    standardizeError(error, context) {
        const errorType = this.classifyError(error);
        const severity = this.determineSeverity(errorType, error);
        return {
            id: this.generateErrorId(),
            type: errorType,
            severity,
            message: error.message || String(error),
            details: this.extractErrorDetails(error),
            context: Object.assign({ timestamp: new Date().toISOString() }, context),
            stack: error.stack,
            originalError: error instanceof Error ? error : undefined,
            retryable: this.isRetryable(errorType),
            recoveryActions: this.getRecoveryActions(errorType)
        };
    }
    /**
     * 错误分类
     */
    classifyError(error) {
        var _a, _b;
        const message = ((_a = error.message) === null || _a === void 0 ? void 0 : _a.toLowerCase()) || '';
        const code = ((_b = error.code) === null || _b === void 0 ? void 0 : _b.toLowerCase()) || '';
        // 网络相关错误
        if (message.includes('network') || message.includes('timeout') ||
            message.includes('connection') || code.includes('econnrefused')) {
            return ErrorType.NETWORK_ERROR;
        }
        // AI API错误
        if (message.includes('api') && (message.includes('quota') || message.includes('limit'))) {
            return ErrorType.AI_QUOTA_EXCEEDED;
        }
        if (message.includes('model') || message.includes('openai') || message.includes('ai')) {
            return ErrorType.AI_API_ERROR;
        }
        // 爬虫相关错误
        if (message.includes('captcha') || message.includes('verification')) {
            return ErrorType.CAPTCHA_ERROR;
        }
        if (message.includes('login') || message.includes('auth')) {
            return ErrorType.LOGIN_REQUIRED;
        }
        if (message.includes('rate') || message.includes('limit') || message.includes('throttle')) {
            return ErrorType.RATE_LIMITED;
        }
        if (message.includes('scraping') || message.includes('selector') || message.includes('element')) {
            return ErrorType.SCRAPING_ERROR;
        }
        // 数据相关错误
        if (message.includes('validation') || message.includes('invalid')) {
            return ErrorType.DATA_VALIDATION_ERROR;
        }
        if (message.includes('parse') || message.includes('json') || message.includes('format')) {
            return ErrorType.DATA_PARSING_ERROR;
        }
        // 系统错误
        if (message.includes('system') || message.includes('initialization')) {
            return ErrorType.SYSTEM_ERROR;
        }
        return ErrorType.UNKNOWN_ERROR;
    }
    /**
     * 确定错误严重程度
     */
    determineSeverity(errorType, error) {
        switch (errorType) {
            case ErrorType.SYSTEM_ERROR:
            case ErrorType.INITIALIZATION_ERROR:
            case ErrorType.AI_QUOTA_EXCEEDED:
                return ErrorSeverity.CRITICAL;
            case ErrorType.AI_API_ERROR:
            case ErrorType.CAPTCHA_ERROR:
            case ErrorType.LOGIN_REQUIRED:
                return ErrorSeverity.HIGH;
            case ErrorType.NETWORK_ERROR:
            case ErrorType.SCRAPING_ERROR:
            case ErrorType.RATE_LIMITED:
                return ErrorSeverity.MEDIUM;
            default:
                return ErrorSeverity.LOW;
        }
    }
    /**
     * 判断是否可重试
     */
    isRetryable(errorType) {
        const nonRetryableTypes = [
            ErrorType.SYSTEM_ERROR,
            ErrorType.CONFIGURATION_ERROR,
            ErrorType.DATA_VALIDATION_ERROR,
            ErrorType.PERMISSION_ERROR
        ];
        return !nonRetryableTypes.includes(errorType);
    }
    /**
     * 获取恢复建议
     */
    getRecoveryActions(errorType) {
        const recoveryMap = {
            // 系统错误
            [ErrorType.SYSTEM_ERROR]: ['重启系统', '检查配置', '联系技术支持'],
            [ErrorType.INITIALIZATION_ERROR]: ['重新初始化', '检查依赖', '重启应用'],
            [ErrorType.CONFIGURATION_ERROR]: ['检查配置文件', '修正配置参数', '重置默认配置'],
            // 网络错误
            [ErrorType.NETWORK_ERROR]: ['检查网络连接', '重试请求', '切换网络'],
            [ErrorType.TIMEOUT_ERROR]: ['增加超时时间', '重试请求', '检查网络状况'],
            [ErrorType.CONNECTION_ERROR]: ['检查连接状态', '重新连接', '切换服务器'],
            // AI相关错误
            [ErrorType.AI_API_ERROR]: ['检查API密钥', '重试请求', '切换模型'],
            [ErrorType.AI_QUOTA_EXCEEDED]: ['等待配额重置', '升级API计划', '优化请求频率'],
            [ErrorType.AI_MODEL_ERROR]: ['切换AI模型', '检查模型配置', '联系API提供商'],
            // 爬虫错误
            [ErrorType.SCRAPING_ERROR]: ['检查页面结构', '更新选择器', '调整爬虫策略'],
            [ErrorType.CAPTCHA_ERROR]: ['人工处理验证码', '切换账号', '降低访问频率'],
            [ErrorType.LOGIN_REQUIRED]: ['重新登录', '检查账号状态', '更新认证信息'],
            [ErrorType.RATE_LIMITED]: ['降低请求频率', '等待限制解除', '使用多个账号'],
            // 数据错误
            [ErrorType.DATA_VALIDATION_ERROR]: ['检查数据格式', '修正输入参数', '更新验证规则'],
            [ErrorType.DATA_PARSING_ERROR]: ['检查数据结构', '更新解析逻辑', '验证数据源'],
            [ErrorType.DATA_QUALITY_ERROR]: ['提高数据质量', '增加过滤条件', '手动验证数据'],
            // 业务错误
            [ErrorType.TASK_ERROR]: ['重新执行任务', '检查任务参数', '分解任务步骤'],
            [ErrorType.USER_ERROR]: ['检查用户输入', '提供使用指导', '修正操作步骤'],
            [ErrorType.PERMISSION_ERROR]: ['检查权限设置', '更新访问凭证', '联系管理员'],
            // 未知错误
            [ErrorType.UNKNOWN_ERROR]: ['查看错误详情', '重试操作', '联系技术支持']
        };
        return recoveryMap[errorType] || ['查看错误详情', '联系技术支持'];
    }
    /**
     * 提取错误详情
     */
    extractErrorDetails(error) {
        const details = {};
        if (error.code)
            details.code = error.code;
        if (error.status)
            details.status = error.status;
        if (error.response)
            details.response = error.response;
        if (error.config)
            details.config = error.config;
        if (error.cause)
            details.cause = error.cause;
        return Object.keys(details).length > 0 ? details : undefined;
    }
    /**
     * 记录错误
     */
    recordError(error) {
        this.errorHistory.push(error);
        // 限制历史记录大小
        if (this.errorHistory.length > this.maxHistorySize) {
            this.errorHistory = this.errorHistory.slice(-this.maxHistorySize);
        }
        console.error(`[ErrorHandler] ${error.severity.toUpperCase()} ${error.type}:`, {
            id: error.id,
            message: error.message,
            component: error.context.component,
            operation: error.context.operation,
            timestamp: error.context.timestamp
        });
    }
    /**
     * 执行错误处理策略
     */
    executeErrorStrategy(error) {
        return __awaiter(this, void 0, void 0, function* () {
            const strategy = this.errorStrategies.get(error.type);
            if (!strategy)
                return;
            // 检查是否需要通知
            if (strategy.notificationRequired) {
                yield this.sendErrorNotification(error);
            }
            // 执行自动恢复
            if (strategy.autoRecover && strategy.fallbackAction) {
                try {
                    yield strategy.fallbackAction();
                    console.log(`[ErrorHandler] Auto-recovery executed for error ${error.id}`);
                }
                catch (recoveryError) {
                    console.error(`[ErrorHandler] Auto-recovery failed for error ${error.id}:`, recoveryError);
                }
            }
        });
    }
    /**
     * 发送错误通知
     */
    sendErrorNotification(error) {
        return __awaiter(this, void 0, void 0, function* () {
            // 这里可以实现各种通知方式：邮件、短信、Webhook等
            console.warn(`[ErrorHandler] 🚨 Critical error notification:`, {
                id: error.id,
                type: error.type,
                severity: error.severity,
                message: error.message,
                component: error.context.component
            });
        });
    }
    /**
     * 触发错误回调
     */
    triggerErrorCallbacks(error) {
        this.errorCallbacks.forEach((callback, id) => {
            try {
                callback(error);
            }
            catch (callbackError) {
                console.error(`[ErrorHandler] Error in callback ${id}:`, callbackError);
            }
        });
    }
    /**
     * 注册错误回调
     */
    registerErrorCallback(id, callback) {
        this.errorCallbacks.set(id, callback);
    }
    /**
     * 注销错误回调
     */
    unregisterErrorCallback(id) {
        this.errorCallbacks.delete(id);
    }
    /**
     * 获取错误统计
     */
    getErrorStatistics() {
        const now = Date.now();
        const oneHour = 60 * 60 * 1000;
        const recentErrors = this.errorHistory.filter(error => now - new Date(error.context.timestamp).getTime() < oneHour);
        const errorsByType = this.errorHistory.reduce((acc, error) => {
            acc[error.type] = (acc[error.type] || 0) + 1;
            return acc;
        }, {});
        const errorsBySeverity = this.errorHistory.reduce((acc, error) => {
            acc[error.severity] = (acc[error.severity] || 0) + 1;
            return acc;
        }, {});
        const errorsByComponent = this.errorHistory.reduce((acc, error) => {
            acc[error.context.component] = (acc[error.context.component] || 0) + 1;
            return acc;
        }, {});
        return {
            totalErrors: this.errorHistory.length,
            errorsByType,
            errorsBySeverity,
            errorsByComponent,
            recentErrors: recentErrors.slice(-10),
            errorRate: recentErrors.length / Math.max(1, this.errorHistory.length),
            mttr: this.calculateMTTR()
        };
    }
    /**
     * 计算平均恢复时间
     */
    calculateMTTR() {
        // 简化实现，实际中需要跟踪错误恢复时间
        return 300; // 5分钟
    }
    /**
     * 清理错误历史
     */
    clearErrorHistory() {
        this.errorHistory = [];
        console.log('[ErrorHandler] Error history cleared');
    }
    /**
     * 生成错误ID
     */
    generateErrorId() {
        return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
exports.UnifiedErrorHandler = UnifiedErrorHandler;
/**
 * 错误处理装饰器
 */
function HandleErrors(component) {
    return function (target, propertyName, descriptor) {
        const method = descriptor.value;
        descriptor.value = function (...args) {
            return __awaiter(this, void 0, void 0, function* () {
                var _a;
                try {
                    return yield method.apply(this, args);
                }
                catch (error) {
                    const errorHandler = UnifiedErrorHandler.getInstance();
                    const standardError = yield errorHandler.handleError(error, {
                        component,
                        operation: propertyName,
                        requestId: ((_a = args[0]) === null || _a === void 0 ? void 0 : _a.requestId) || 'unknown'
                    });
                    throw standardError;
                }
            });
        };
        return descriptor;
    };
}
