"use strict";
/**
 * V3.0 AI+MCP 爬虫系统 - 结构化日志系统
 *
 * 这个文件提供了完整的结构化日志记录系统，
 * 支持多级别日志、结构化输出和日志分析。
 *
 * 核心功能：
 * 1. 多级别日志记录
 * 2. 结构化日志格式
 * 3. 日志过滤和搜索
 * 4. 性能监控集成
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.StructuredLogger = exports.FileOutput = exports.ConsoleOutput = exports.LogLevel = void 0;
exports.LogExecution = LogExecution;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
/**
 * 日志级别
 */
var LogLevel;
(function (LogLevel) {
    LogLevel[LogLevel["DEBUG"] = 0] = "DEBUG";
    LogLevel[LogLevel["INFO"] = 1] = "INFO";
    LogLevel[LogLevel["WARN"] = 2] = "WARN";
    LogLevel[LogLevel["ERROR"] = 3] = "ERROR";
    LogLevel[LogLevel["FATAL"] = 4] = "FATAL";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
/**
 * 控制台输出器
 */
class ConsoleOutput {
    constructor(enableColors = true) {
        this.enableColors = enableColors;
    }
    write(entry) {
        return __awaiter(this, void 0, void 0, function* () {
            const formatted = this.formatForConsole(entry);
            switch (entry.level) {
                case LogLevel.DEBUG:
                    console.debug(formatted);
                    break;
                case LogLevel.INFO:
                    console.info(formatted);
                    break;
                case LogLevel.WARN:
                    console.warn(formatted);
                    break;
                case LogLevel.ERROR:
                case LogLevel.FATAL:
                    console.error(formatted);
                    break;
            }
        });
    }
    formatForConsole(entry) {
        const timestamp = new Date(entry.timestamp).toISOString();
        const level = entry.levelName.padEnd(5);
        const component = entry.component.padEnd(20);
        let message = `[${timestamp}] ${level} [${component}] ${entry.message}`;
        if (entry.context.taskId) {
            message += ` (Task: ${entry.context.taskId})`;
        }
        if (entry.metadata.duration) {
            message += ` (${entry.metadata.duration}ms)`;
        }
        if (entry.error) {
            message += `\n  Error: ${entry.error.message}`;
            if (entry.error.stack) {
                message += `\n  Stack: ${entry.error.stack}`;
            }
        }
        return this.enableColors ? this.colorize(message, entry.level) : message;
    }
    colorize(message, level) {
        const colors = {
            [LogLevel.DEBUG]: '\x1b[36m', // Cyan
            [LogLevel.INFO]: '\x1b[32m', // Green
            [LogLevel.WARN]: '\x1b[33m', // Yellow
            [LogLevel.ERROR]: '\x1b[31m', // Red
            [LogLevel.FATAL]: '\x1b[35m' // Magenta
        };
        const reset = '\x1b[0m';
        return `${colors[level]}${message}${reset}`;
    }
}
exports.ConsoleOutput = ConsoleOutput;
/**
 * 文件输出器
 */
class FileOutput {
    constructor(filePath, maxFileSize = 10 * 1024 * 1024, maxFiles = 5) {
        this.currentSize = 0;
        this.filePath = filePath;
        this.maxFileSize = maxFileSize;
        this.maxFiles = maxFiles;
        // 确保目录存在
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        // 获取当前文件大小
        if (fs.existsSync(filePath)) {
            this.currentSize = fs.statSync(filePath).size;
        }
    }
    write(entry) {
        return __awaiter(this, void 0, void 0, function* () {
            const formatted = JSON.stringify(entry) + '\n';
            // 检查是否需要轮转日志
            if (this.currentSize + formatted.length > this.maxFileSize) {
                yield this.rotateLog();
            }
            fs.appendFileSync(this.filePath, formatted);
            this.currentSize += formatted.length;
        });
    }
    rotateLog() {
        return __awaiter(this, void 0, void 0, function* () {
            // 删除最旧的日志文件
            const oldestFile = `${this.filePath}.${this.maxFiles}`;
            if (fs.existsSync(oldestFile)) {
                fs.unlinkSync(oldestFile);
            }
            // 轮转现有文件
            for (let i = this.maxFiles - 1; i >= 1; i--) {
                const currentFile = i === 1 ? this.filePath : `${this.filePath}.${i}`;
                const nextFile = `${this.filePath}.${i + 1}`;
                if (fs.existsSync(currentFile)) {
                    fs.renameSync(currentFile, nextFile);
                }
            }
            this.currentSize = 0;
        });
    }
    flush() {
        return __awaiter(this, void 0, void 0, function* () {
            // 文件系统会自动刷新，这里不需要特殊处理
        });
    }
    close() {
        return __awaiter(this, void 0, void 0, function* () {
            // 文件输出器不需要特殊的关闭操作
        });
    }
}
exports.FileOutput = FileOutput;
/**
 * 结构化日志记录器
 */
class StructuredLogger {
    constructor(config) {
        this.outputs = [];
        this.logBuffer = [];
        this.flushInterval = null;
        this.config = config;
        this.setupOutputs();
        this.startFlushTimer();
    }
    /**
     * 获取单例实例
     */
    static getInstance(config) {
        if (!StructuredLogger.instance) {
            const defaultConfig = {
                level: LogLevel.INFO,
                enableConsole: true,
                enableFile: true,
                filePath: './logs/system.log',
                maxFileSize: 10 * 1024 * 1024,
                maxFiles: 5,
                enableStructured: true,
                enablePerformanceLogging: true,
                filters: {}
            };
            StructuredLogger.instance = new StructuredLogger(config || defaultConfig);
        }
        return StructuredLogger.instance;
    }
    /**
     * 设置输出器
     */
    setupOutputs() {
        if (this.config.enableConsole) {
            this.outputs.push(new ConsoleOutput());
        }
        if (this.config.enableFile && this.config.filePath) {
            this.outputs.push(new FileOutput(this.config.filePath, this.config.maxFileSize, this.config.maxFiles));
        }
    }
    /**
     * 启动刷新定时器
     */
    startFlushTimer() {
        this.flushInterval = setInterval(() => __awaiter(this, void 0, void 0, function* () {
            yield this.flush();
        }), 5000); // 每5秒刷新一次
    }
    /**
     * 记录调试日志
     */
    debug(message, component, context, metadata) {
        this.log(LogLevel.DEBUG, message, component, context, metadata);
    }
    /**
     * 记录信息日志
     */
    info(message, component, context, metadata) {
        this.log(LogLevel.INFO, message, component, context, metadata);
    }
    /**
     * 记录警告日志
     */
    warn(message, component, context, metadata) {
        this.log(LogLevel.WARN, message, component, context, metadata);
    }
    /**
     * 记录错误日志
     */
    error(message, component, error, context, metadata) {
        const errorInfo = error ? {
            name: error.name,
            message: error.message,
            stack: error.stack
        } : undefined;
        this.log(LogLevel.ERROR, message, component, context, metadata, errorInfo);
    }
    /**
     * 记录致命错误日志
     */
    fatal(message, component, error, context, metadata) {
        const errorInfo = error ? {
            name: error.name,
            message: error.message,
            stack: error.stack
        } : undefined;
        this.log(LogLevel.FATAL, message, component, context, metadata, errorInfo);
    }
    /**
     * 记录性能日志
     */
    performance(operation, component, duration, context, additionalMetadata) {
        if (!this.config.enablePerformanceLogging)
            return;
        const metadata = Object.assign({ duration, memoryUsage: process.memoryUsage().heapUsed }, additionalMetadata);
        this.log(LogLevel.INFO, `Performance: ${operation}`, component, context, metadata, undefined, ['performance']);
    }
    /**
     * 核心日志记录方法
     */
    log(level, message, component, context = {}, metadata = {}, error, tags = []) {
        // 检查日志级别
        if (level < this.config.level)
            return;
        // 检查过滤器
        if (!this.shouldLog(component))
            return;
        const entry = {
            id: this.generateLogId(),
            timestamp: new Date().toISOString(),
            level,
            levelName: LogLevel[level],
            message,
            component,
            operation: context.operation,
            context: Object.assign({}, context),
            metadata: Object.assign({}, metadata),
            tags,
            error
        };
        // 添加到缓冲区
        this.logBuffer.push(entry);
        // 如果是错误或致命错误，立即刷新
        if (level >= LogLevel.ERROR) {
            this.flush();
        }
    }
    /**
     * 检查是否应该记录日志
     */
    shouldLog(component) {
        var _a;
        const { filters } = this.config;
        // 检查排除组件
        if ((_a = filters.excludeComponents) === null || _a === void 0 ? void 0 : _a.includes(component)) {
            return false;
        }
        // 检查包含组件
        if (filters.components && filters.components.length > 0) {
            return filters.components.includes(component);
        }
        return true;
    }
    /**
     * 刷新日志缓冲区
     */
    flush() {
        return __awaiter(this, void 0, void 0, function* () {
            if (this.logBuffer.length === 0)
                return;
            const entries = [...this.logBuffer];
            this.logBuffer = [];
            for (const output of this.outputs) {
                for (const entry of entries) {
                    try {
                        yield output.write(entry);
                    }
                    catch (error) {
                        console.error('Failed to write log entry:', error);
                    }
                }
                if (output.flush) {
                    yield output.flush();
                }
            }
        });
    }
    /**
     * 搜索日志
     */
    searchLogs(criteria) {
        // 这里需要实现从文件读取和搜索的逻辑
        // 目前返回空数组作为占位符
        return [];
    }
    /**
     * 获取日志统计
     */
    getLogStatistics() {
        // 这里需要实现统计逻辑
        return {
            totalLogs: 0,
            logsByLevel: {},
            logsByComponent: {},
            recentErrors: []
        };
    }
    /**
     * 更新配置
     */
    updateConfig(newConfig) {
        this.config = Object.assign(Object.assign({}, this.config), newConfig);
        // 重新设置输出器
        this.outputs = [];
        this.setupOutputs();
    }
    /**
     * 关闭日志记录器
     */
    close() {
        return __awaiter(this, void 0, void 0, function* () {
            if (this.flushInterval) {
                clearInterval(this.flushInterval);
            }
            yield this.flush();
            for (const output of this.outputs) {
                if (output.close) {
                    yield output.close();
                }
            }
        });
    }
    /**
     * 生成日志ID
     */
    generateLogId() {
        return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
exports.StructuredLogger = StructuredLogger;
/**
 * 日志装饰器
 */
function LogExecution(component, operation) {
    return function (target, propertyName, descriptor) {
        const method = descriptor.value;
        const operationName = operation || propertyName;
        descriptor.value = function (...args) {
            return __awaiter(this, void 0, void 0, function* () {
                const logger = StructuredLogger.getInstance();
                const startTime = Date.now();
                logger.debug(`Starting ${operationName}`, component, {
                    operation: operationName,
                    args: args.length
                });
                try {
                    const result = yield method.apply(this, args);
                    const duration = Date.now() - startTime;
                    logger.performance(operationName, component, duration, {
                        operation: operationName,
                        success: true
                    });
                    return result;
                }
                catch (error) {
                    const duration = Date.now() - startTime;
                    logger.error(`Failed ${operationName}`, component, error, {
                        operation: operationName,
                        duration
                    });
                    throw error;
                }
            });
        };
        return descriptor;
    };
}
// 导出默认日志实例
exports.logger = StructuredLogger.getInstance();
