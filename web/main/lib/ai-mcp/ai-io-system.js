"use strict";
/**
 * AI I/O系统 - 处理AI输出的呈现和交互
 *
 * 核心功能：
 * 1. AI思考过程的实时展示
 * 2. 决策输出的结构化呈现
 * 3. 用户交互和反馈收集
 * 4. 输出内容的持久化存储
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.aiIOSystem = exports.AIIOSystem = exports.FileRenderer = exports.ElectronRenderer = exports.WebSocketRenderer = exports.ConsoleRenderer = void 0;
const events_1 = require("events");
/**
 * 控制台输出渲染器
 */
class ConsoleRenderer {
    constructor(enableColors = true) {
        this.enableColors = enableColors;
    }
    render(output) {
        return __awaiter(this, void 0, void 0, function* () {
            const timestamp = new Date(output.metadata.timestamp).toLocaleTimeString();
            const prefix = this.getTypePrefix(output.type);
            const colorCode = this.enableColors ? this.getColorCode(output.type) : '';
            const resetCode = this.enableColors ? '\x1b[0m' : '';
            console.log(`${colorCode}[${timestamp}] ${prefix} ${output.content}${resetCode}`);
            if (output.structured) {
                console.log(`${colorCode}📊 Structured Data:${resetCode}`, JSON.stringify(output.structured, null, 2));
            }
        });
    }
    clear() {
        return __awaiter(this, void 0, void 0, function* () {
            console.clear();
        });
    }
    setVisible(visible) {
        return __awaiter(this, void 0, void 0, function* () {
            // 控制台渲染器无需实现可见性控制
        });
    }
    getTypePrefix(type) {
        switch (type) {
            case 'thinking': return '🤔 AI思考';
            case 'decision': return '🎯 AI决策';
            case 'action': return '⚡ 执行动作';
            case 'result': return '✅ 执行结果';
            case 'error': return '❌ 错误';
            default: return '📝 输出';
        }
    }
    getColorCode(type) {
        switch (type) {
            case 'thinking': return '\x1b[36m'; // 青色
            case 'decision': return '\x1b[33m'; // 黄色
            case 'action': return '\x1b[34m'; // 蓝色
            case 'result': return '\x1b[32m'; // 绿色
            case 'error': return '\x1b[31m'; // 红色
            default: return '\x1b[37m'; // 白色
        }
    }
}
exports.ConsoleRenderer = ConsoleRenderer;
/**
 * WebSocket输出渲染器
 */
class WebSocketRenderer {
    constructor(ws) {
        this.isVisible = true;
        this.ws = ws;
    }
    render(output) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!this.isVisible || !this.ws)
                return;
            const message = {
                type: 'ai_output',
                data: output
            };
            try {
                this.ws.emit('ai_output', message);
            }
            catch (error) {
                console.error('[WebSocketRenderer] Failed to send output:', error);
            }
        });
    }
    clear() {
        return __awaiter(this, void 0, void 0, function* () {
            if (!this.ws)
                return;
            try {
                this.ws.emit('ai_output_clear', {});
            }
            catch (error) {
                console.error('[WebSocketRenderer] Failed to clear output:', error);
            }
        });
    }
    setVisible(visible) {
        return __awaiter(this, void 0, void 0, function* () {
            this.isVisible = visible;
        });
    }
}
exports.WebSocketRenderer = WebSocketRenderer;
/**
 * Electron主进程输出渲染器
 */
class ElectronRenderer {
    constructor(mainWindow) {
        this.isVisible = true;
        this.mainWindow = mainWindow;
    }
    render(output) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!this.isVisible || !this.mainWindow)
                return;
            try {
                this.mainWindow.webContents.send('ai-output', output);
            }
            catch (error) {
                console.error('[ElectronRenderer] Failed to send output:', error);
            }
        });
    }
    clear() {
        return __awaiter(this, void 0, void 0, function* () {
            if (!this.mainWindow)
                return;
            try {
                this.mainWindow.webContents.send('ai-output-clear', {});
            }
            catch (error) {
                console.error('[ElectronRenderer] Failed to clear output:', error);
            }
        });
    }
    setVisible(visible) {
        return __awaiter(this, void 0, void 0, function* () {
            this.isVisible = visible;
        });
    }
}
exports.ElectronRenderer = ElectronRenderer;
/**
 * 文件输出渲染器
 */
class FileRenderer {
    constructor(filePath) {
        this.isVisible = true;
        this.filePath = filePath;
    }
    render(output) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!this.isVisible)
                return;
            const fs = require('fs').promises;
            const path = require('path');
            try {
                // 确保目录存在
                const dir = path.dirname(this.filePath);
                yield fs.mkdir(dir, { recursive: true });
                // 格式化输出
                const timestamp = new Date(output.metadata.timestamp).toISOString();
                const logLine = `[${timestamp}] ${output.type.toUpperCase()}: ${output.content}\n`;
                if (output.structured) {
                    const structuredLine = `[${timestamp}] STRUCTURED: ${JSON.stringify(output.structured)}\n`;
                    yield fs.appendFile(this.filePath, logLine + structuredLine);
                }
                else {
                    yield fs.appendFile(this.filePath, logLine);
                }
            }
            catch (error) {
                console.error('[FileRenderer] Failed to write output:', error);
            }
        });
    }
    clear() {
        return __awaiter(this, void 0, void 0, function* () {
            const fs = require('fs').promises;
            try {
                yield fs.writeFile(this.filePath, '');
            }
            catch (error) {
                console.error('[FileRenderer] Failed to clear file:', error);
            }
        });
    }
    setVisible(visible) {
        return __awaiter(this, void 0, void 0, function* () {
            this.isVisible = visible;
        });
    }
}
exports.FileRenderer = FileRenderer;
/**
 * AI I/O系统管理器
 */
class AIIOSystem extends events_1.EventEmitter {
    constructor() {
        super();
        this.renderers = [];
        this.outputHistory = [];
        this.maxHistorySize = 1000;
        this.isEnabled = true;
    }
    /**
     * 添加输出渲染器
     */
    addRenderer(renderer) {
        this.renderers.push(renderer);
    }
    /**
     * 移除输出渲染器
     */
    removeRenderer(renderer) {
        const index = this.renderers.indexOf(renderer);
        if (index > -1) {
            this.renderers.splice(index, 1);
        }
    }
    /**
     * 输出AI内容
     */
    output(type_1, content_1) {
        return __awaiter(this, arguments, void 0, function* (type, content, metadata = {}, structured) {
            if (!this.isEnabled)
                return;
            const output = {
                id: this.generateOutputId(),
                type,
                content,
                metadata: Object.assign({ timestamp: new Date().toISOString() }, metadata),
                structured
            };
            // 添加到历史记录
            this.outputHistory.push(output);
            if (this.outputHistory.length > this.maxHistorySize) {
                this.outputHistory.shift();
            }
            // 渲染输出
            yield Promise.all(this.renderers.map(renderer => renderer.render(output).catch(error => console.error('[AIIOSystem] Renderer error:', error))));
            // 发出事件
            this.emit('output', output);
        });
    }
    /**
     * AI思考过程输出
     */
    thinking(content, taskId) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.output('thinking', content, { taskId });
        });
    }
    /**
     * AI决策输出
     */
    decision(content, confidence, taskId) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.output('decision', content, { taskId, confidence });
        });
    }
    /**
     * 执行动作输出
     */
    action(content, toolName, taskId) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.output('action', content, { taskId, toolName });
        });
    }
    /**
     * 执行结果输出
     */
    result(content, structured, duration, taskId) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.output('result', content, { taskId, duration }, structured);
        });
    }
    /**
     * 错误输出
     */
    error(content, taskId) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.output('error', content, { taskId });
        });
    }
    /**
     * 清空所有输出
     */
    clear() {
        return __awaiter(this, void 0, void 0, function* () {
            this.outputHistory = [];
            yield Promise.all(this.renderers.map(renderer => renderer.clear().catch(error => console.error('[AIIOSystem] Clear error:', error))));
        });
    }
    /**
     * 设置系统启用状态
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;
    }
    /**
     * 设置渲染器可见性
     */
    setVisible(visible) {
        return __awaiter(this, void 0, void 0, function* () {
            yield Promise.all(this.renderers.map(renderer => renderer.setVisible(visible).catch(error => console.error('[AIIOSystem] Visibility error:', error))));
        });
    }
    /**
     * 获取输出历史
     */
    getHistory(limit) {
        if (limit) {
            return this.outputHistory.slice(-limit);
        }
        return [...this.outputHistory];
    }
    /**
     * 根据任务ID获取输出
     */
    getOutputsByTaskId(taskId) {
        return this.outputHistory.filter(output => output.metadata.taskId === taskId);
    }
    /**
     * 生成输出ID
     */
    generateOutputId() {
        return `output_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
exports.AIIOSystem = AIIOSystem;
/**
 * 全局AI I/O系统实例
 */
exports.aiIOSystem = new AIIOSystem();
// 默认添加控制台渲染器
exports.aiIOSystem.addRenderer(new ConsoleRenderer());
