"use strict";
/**
 * 🔥 数据转换器实现
 *
 * 实现各种数据格式之间的转换：
 * 1. 爬虫原始数据 → 标准化存储格式
 * 2. 标准化数据 → AI输入格式
 * 3. 标准化数据 → 用户导出格式
 *
 * 作者: 毒舌界祖师爷的小贱狗 🐕
 * 创建时间: 2025-07-23
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.exportConverter = exports.storageConverter = exports.aiInputConverter = exports.DataValidator = exports.ExportConverter = exports.StorageConverter = exports.AIInputConverter = void 0;
const data_format_standards_1 = require("./data-format-standards");
// ==================== AI输入格式转换器 ====================
/**
 * 🎯 将标准化数据转换为AI输入格式
 */
class AIInputConverter {
    /**
     * 转换关键词分析数据
     */
    convertKeywordAnalysis(keywords, context) {
        return {
            type: 'keyword_analysis',
            textDescription: `请分析以下${keywords.length}个关键词的市场潜力和用户需求：${keywords.join('、')}。${context || ''}`,
            structuredData: {
                keywords,
                metadata: {
                    platform: 'taobao', // 默认平台
                    timestamp: new Date().toISOString(),
                    dataSource: 'user_input',
                    processingHints: [
                        '分析关键词的商业价值',
                        '识别用户需求痛点',
                        '评估市场竞争程度'
                    ]
                }
            }
        };
    }
    /**
     * 转换商品发现数据
     */
    convertProductDiscovery(products, keywords) {
        const productSummary = products.map(p => `${p.title} (${p.platform}, 价格:${p.price?.current || '未知'}, 销量:${p.sales?.count || '未知'})`).join('\n');
        return {
            type: 'product_discovery',
            textDescription: `基于关键词"${keywords.join('、')}"发现了${products.length}个相关商品，请分析这些商品的特点和市场表现：\n${productSummary}`,
            structuredData: {
                keywords,
                products,
                metadata: {
                    platform: products[0]?.platform || 'taobao',
                    timestamp: new Date().toISOString(),
                    dataSource: 'web_scraping',
                    processingHints: [
                        '分析商品价格分布',
                        '识别热门商品特征',
                        '评估市场需求趋势'
                    ]
                }
            }
        };
    }
    /**
     * 转换评论分析数据
     */
    convertCommentAnalysis(comments, productTitle) {
        const commentTexts = comments.map(c => c.content).slice(0, 50); // 限制评论数量
        const avgRating = comments.reduce((sum, c) => sum + (c.rating?.score || 0), 0) / comments.length;
        return {
            type: 'comment_analysis',
            textDescription: `请分析${productTitle ? `"${productTitle}"` : '商品'}的${comments.length}条用户评论，平均评分${avgRating.toFixed(1)}分。请识别用户满意的优点、不满意的缺点，以及未被满足的需求。`,
            structuredData: {
                comments,
                metadata: {
                    platform: comments[0]?.platform || 'taobao',
                    timestamp: new Date().toISOString(),
                    dataSource: 'user_reviews',
                    processingHints: [
                        '提取用户满意的产品优点',
                        '识别用户抱怨的产品缺点',
                        '发现未被满足的用户需求',
                        '分析评论情感倾向'
                    ]
                }
            }
        };
    }
    convert(input) {
        throw new Error('请使用具体的转换方法：convertKeywordAnalysis, convertProductDiscovery, convertCommentAnalysis');
    }
    validate(data) {
        return !!(data.type &&
            data.textDescription &&
            data.structuredData &&
            data.structuredData.metadata &&
            data.structuredData.metadata.timestamp);
    }
    getSchema() {
        return {
            type: 'object',
            required: ['type', 'textDescription', 'structuredData'],
            properties: {
                type: { enum: ['keyword_analysis', 'product_discovery', 'comment_analysis', 'market_insights'] },
                textDescription: { type: 'string', minLength: 10 },
                structuredData: {
                    type: 'object',
                    required: ['metadata'],
                    properties: {
                        keywords: { type: 'array', items: { type: 'string' } },
                        products: { type: 'array' },
                        comments: { type: 'array' },
                        metadata: {
                            type: 'object',
                            required: ['platform', 'timestamp', 'dataSource'],
                            properties: {
                                platform: { enum: ['taobao', 'xiaohongshu'] },
                                timestamp: { type: 'string', format: 'date-time' },
                                dataSource: { type: 'string' }
                            }
                        }
                    }
                }
            }
        };
    }
}
exports.AIInputConverter = AIInputConverter;
// ==================== 存储格式转换器 ====================
/**
 * 🎯 将爬虫原始数据转换为标准存储格式
 */
class StorageConverter {
    convert(rawTaskData) {
        const now = new Date().toISOString();
        // 转换商品数据
        const products = this.convertProducts(rawTaskData.discoveredLinks || []);
        // 转换评论数据
        const comments = this.convertComments(rawTaskData.scrapedComments || []);
        // 转换分析结果
        const analysisResults = this.convertAnalysisResults(rawTaskData.analysisResults || []);
        return {
            taskId: rawTaskData.id || rawTaskData.taskId,
            version: data_format_standards_1.DATA_FORMAT_VERSION,
            basicInfo: {
                keywords: rawTaskData.keywords || [],
                platforms: rawTaskData.platforms || ['taobao'],
                createdAt: rawTaskData.createdAt || now,
                updatedAt: rawTaskData.updatedAt || now,
                status: rawTaskData.status || 'PENDING'
            },
            discoveredProducts: products,
            scrapedComments: comments,
            analysisResults,
            statistics: {
                totalProducts: products.length,
                totalComments: comments.length,
                successRate: this.calculateSuccessRate(rawTaskData),
                processingTime: rawTaskData.processingTime || 0,
                dataQualityScore: this.calculateDataQuality(products, comments)
            },
            metadata: {
                dataIntegrity: true,
                lastValidation: now,
                exportHistory: []
            }
        };
    }
    convertProducts(rawProducts) {
        return rawProducts.map((product, index) => ({
            id: product.id || `product_${index}`,
            platform: product.platform || 'taobao',
            title: product.title || product.name || '未知商品',
            url: product.url || product.link || '',
            price: product.price ? {
                current: parseFloat(product.price.toString().replace(/[^\d.]/g, '')) || 0,
                currency: 'CNY'
            } : undefined,
            sales: product.sales ? {
                count: parseInt(product.sales.toString().replace(/[^\d]/g, '')) || 0,
                period: '月销量'
            } : undefined,
            rating: product.rating ? {
                score: parseFloat(product.rating.toString()) || 0,
                maxScore: 5,
                reviewCount: product.reviewCount || 0
            } : undefined,
            images: product.images || [],
            category: product.category || '',
            brand: product.brand || '',
            discoveredAt: product.discoveredAt || new Date().toISOString(),
            discoveryKeyword: product.keyword || ''
        }));
    }
    convertComments(rawComments) {
        return rawComments.map((comment, index) => ({
            id: comment.id || `comment_${index}`,
            platform: comment.platform || 'taobao',
            productId: comment.productId || '',
            productTitle: comment.productTitle || '',
            content: comment.content || comment.text || '',
            rating: comment.rating ? {
                score: parseFloat(comment.rating.toString()) || 0,
                maxScore: 5
            } : undefined,
            author: comment.author ? {
                name: comment.author.name || '匿名用户',
                level: comment.author.level || '',
                verified: comment.author.verified || false
            } : undefined,
            engagement: {
                likes: comment.likes || 0,
                replies: comment.replies || 0,
                helpful: comment.helpful || 0
            },
            metadata: {
                date: comment.date || new Date().toISOString(),
                isVerifiedPurchase: comment.isVerifiedPurchase || false,
                hasImages: comment.hasImages || false,
                hasVideo: comment.hasVideo || false
            },
            scrapedAt: comment.scrapedAt || new Date().toISOString()
        }));
    }
    convertAnalysisResults(rawResults) {
        return rawResults.map(result => ({
            type: result.type || 'comment_analysis',
            input: result.input || {},
            output: result.output || {},
            confidence: result.confidence || 0.8,
            processingTime: result.processingTime || 0,
            timestamp: result.timestamp || new Date().toISOString()
        }));
    }
    calculateSuccessRate(rawData) {
        const total = (rawData.discoveredLinks?.length || 0) + (rawData.scrapedComments?.length || 0);
        const successful = total; // 简化计算，实际应该基于错误率
        return total > 0 ? successful / total : 1.0;
    }
    calculateDataQuality(products, comments) {
        let score = 0.5; // 基础分
        // 商品数据质量
        if (products.length > 0) {
            const hasPrice = products.filter(p => p.price).length / products.length;
            const hasRating = products.filter(p => p.rating).length / products.length;
            score += (hasPrice + hasRating) * 0.2;
        }
        // 评论数据质量
        if (comments.length > 0) {
            const hasRating = comments.filter(c => c.rating).length / comments.length;
            const hasAuthor = comments.filter(c => c.author).length / comments.length;
            score += (hasRating + hasAuthor) * 0.15;
        }
        return Math.min(score, 1.0);
    }
    validate(data) {
        return !!(data.taskId &&
            data.version &&
            data.basicInfo &&
            data.statistics &&
            data.metadata);
    }
    getSchema() {
        return {
            type: 'object',
            required: ['taskId', 'version', 'basicInfo', 'discoveredProducts', 'scrapedComments', 'analysisResults', 'statistics', 'metadata']
        };
    }
}
exports.StorageConverter = StorageConverter;
// ==================== 导出格式转换器 ====================
/**
 * 🎯 将标准存储数据转换为用户导出格式
 */
class ExportConverter {
    convert(taskData) {
        return {
            products: this.convertProductsForExport(taskData.discoveredProducts),
            comments: this.convertCommentsForExport(taskData.scrapedComments),
            insights: this.convertInsightsForExport(taskData.analysisResults),
            summary: this.generateSummaryForExport(taskData)
        };
    }
    convertProductsForExport(products) {
        return products.map(product => ({
            platform: product.platform,
            keyword: product.discoveryKeyword,
            title: product.title,
            url: product.url,
            currentPrice: product.price?.current || 0,
            originalPrice: product.price?.original || 0,
            currency: product.price?.currency || 'CNY',
            salesCount: product.sales?.count || 0,
            salesPeriod: product.sales?.period || '',
            rating: product.rating?.score || 0,
            reviewCount: product.rating?.reviewCount || 0,
            category: product.category || '',
            brand: product.brand || '',
            discoveredAt: product.discoveredAt
        }));
    }
    convertCommentsForExport(comments) {
        return comments.map(comment => ({
            platform: comment.platform,
            productTitle: comment.productTitle,
            commentContent: comment.content,
            rating: comment.rating?.score || 0,
            authorName: comment.author?.name || '匿名用户',
            authorLevel: comment.author?.level || '',
            likes: comment.engagement?.likes || 0,
            replies: comment.engagement?.replies || 0,
            commentDate: comment.metadata.date,
            isVerifiedPurchase: comment.metadata.isVerifiedPurchase || false,
            hasMedia: Boolean(comment.metadata.hasImages || comment.metadata.hasVideo),
            scrapedAt: comment.scrapedAt
        }));
    }
    convertInsightsForExport(analysisResults) {
        const insights = [];
        analysisResults.forEach(result => {
            if (result.type === 'comment_analysis' && result.output) {
                const advantages = result.output.advantages || [];
                const disadvantages = result.output.disadvantages || [];
                const unmetNeeds = result.output.unmetNeeds || [];
                insights.push({
                    analysisType: result.type,
                    keyword: result.input.keywords?.join(', ') || '',
                    advantages: advantages.join('; '),
                    disadvantages: disadvantages.join('; '),
                    unmetNeeds: unmetNeeds.join('; '),
                    marketOpportunity: result.output.marketOpportunities?.join('; ') || '',
                    confidence: result.confidence,
                    analysisDate: result.timestamp
                });
            }
        });
        return insights;
    }
    generateSummaryForExport(taskData) {
        const products = taskData.discoveredProducts;
        const comments = taskData.scrapedComments;
        // 计算平均评分
        const ratingsWithScore = comments.filter(c => c.rating?.score);
        const averageRating = ratingsWithScore.length > 0
            ? ratingsWithScore.reduce((sum, c) => sum + (c.rating?.score || 0), 0) / ratingsWithScore.length
            : 0;
        // 统计热门类别
        const categories = products.map(p => p.category).filter((cat) => Boolean(cat));
        const topCategories = this.getTopItems(categories, 3).join(', ');
        // 统计热门品牌
        const brands = products.map(p => p.brand).filter((brand) => Boolean(brand));
        const topBrands = this.getTopItems(brands, 3).join(', ');
        return [{
                totalKeywords: taskData.basicInfo.keywords.length,
                totalProducts: taskData.statistics.totalProducts,
                totalComments: taskData.statistics.totalComments,
                averageRating: parseFloat(averageRating.toFixed(2)),
                topCategories,
                topBrands,
                dataQualityScore: parseFloat(taskData.statistics.dataQualityScore.toFixed(2)),
                reportGeneratedAt: new Date().toISOString()
            }];
    }
    getTopItems(items, limit) {
        const counts = items.reduce((acc, item) => {
            acc[item] = (acc[item] || 0) + 1;
            return acc;
        }, {});
        return Object.entries(counts)
            .sort(([, a], [, b]) => b - a)
            .slice(0, limit)
            .map(([item]) => item);
    }
    validate(data) {
        return !!(Array.isArray(data.products) &&
            Array.isArray(data.comments) &&
            Array.isArray(data.insights) &&
            Array.isArray(data.summary));
    }
    getSchema() {
        return {
            type: 'object',
            required: ['products', 'comments', 'insights', 'summary'],
            properties: {
                products: { type: 'array' },
                comments: { type: 'array' },
                insights: { type: 'array' },
                summary: { type: 'array' }
            }
        };
    }
}
exports.ExportConverter = ExportConverter;
// ==================== 数据验证器 ====================
/**
 * 🎯 数据格式验证器
 */
class DataValidator {
    static validateAIInput(data) {
        const errors = [];
        const warnings = [];
        if (!data.type)
            errors.push('缺少数据类型标识');
        if (!data.textDescription || data.textDescription.length < 10) {
            errors.push('文本描述过短或缺失');
        }
        if (!data.structuredData)
            errors.push('缺少结构化数据');
        if (!data.structuredData?.metadata?.timestamp) {
            errors.push('缺少时间戳');
        }
        if (data.structuredData?.keywords?.length === 0) {
            warnings.push('关键词列表为空');
        }
        const dataIntegrityScore = errors.length === 0 ?
            (warnings.length === 0 ? 1.0 : 0.8) : 0.3;
        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            dataIntegrityScore
        };
    }
    static validateTaskStorage(data) {
        const errors = [];
        const warnings = [];
        if (!data.taskId)
            errors.push('缺少任务ID');
        if (!data.version)
            errors.push('缺少数据版本');
        if (!data.basicInfo?.keywords?.length)
            errors.push('缺少关键词');
        if (data.discoveredProducts.length === 0) {
            warnings.push('未发现任何商品');
        }
        if (data.scrapedComments.length === 0) {
            warnings.push('未抓取任何评论');
        }
        const dataIntegrityScore = errors.length === 0 ?
            Math.max(0.5, 1.0 - warnings.length * 0.1) : 0.2;
        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            dataIntegrityScore
        };
    }
}
exports.DataValidator = DataValidator;
// ==================== 导出实例 ====================
exports.aiInputConverter = new AIInputConverter();
exports.storageConverter = new StorageConverter();
exports.exportConverter = new ExportConverter();
