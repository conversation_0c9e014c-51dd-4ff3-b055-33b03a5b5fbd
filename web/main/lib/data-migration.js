"use strict";
/**
 * 🔥 数据迁移工具
 *
 * 将旧格式的任务数据迁移到新的标准化格式：
 * 1. 旧Task格式 → 新TaskDataStorage格式
 * 2. 向后兼容性处理
 * 3. 数据完整性验证
 *
 * 作者: 毒舌界祖师爷的小贱狗 🐕
 * 创建时间: 2025-07-23
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getStandardData = exports.migrateTask = exports.DataCompatibilityHelper = exports.DataMigrator = void 0;
const data_format_standards_1 = require("./data-format-standards");
const data_converters_1 = require("./data-converters");
/**
 * 🎯 数据迁移器
 */
class DataMigrator {
    /**
     * 将旧格式Task转换为标准化TaskDataStorage
     */
    static migrateTaskToStandardFormat(task) {
        console.log(`[DataMigrator] 🔄 Migrating task ${task.id} to standard format`);
        // 如果已经有标准化数据，直接返回
        if (task.standardData) {
            console.log(`[DataMigrator] ✅ Task ${task.id} already has standard data`);
            return task.standardData;
        }
        // 转换商品数据
        const discoveredProducts = this.convertLegacyLinksToProducts(task.discoveredLinks || [], task.keywords);
        // 转换评论数据
        const scrapedComments = this.convertLegacyCommentsToStandard(task.scrapedComments || []);
        // 转换分析结果
        const analysisResults = this.convertLegacyReportToAnalysis(task.reportData);
        const standardData = {
            taskId: task.id,
            version: data_format_standards_1.DATA_FORMAT_VERSION,
            basicInfo: {
                keywords: task.keywords || task.initialKeywords || [],
                platforms: this.extractPlatformsFromTask(task),
                createdAt: task.createdAt,
                updatedAt: task.updatedAt,
                status: task.status
            },
            discoveredProducts,
            scrapedComments,
            analysisResults,
            statistics: {
                totalProducts: discoveredProducts.length,
                totalComments: scrapedComments.length,
                successRate: this.calculateSuccessRate(task),
                processingTime: this.estimateProcessingTime(task),
                dataQualityScore: this.calculateDataQuality(discoveredProducts, scrapedComments)
            },
            metadata: {
                dataIntegrity: true,
                lastValidation: new Date().toISOString(),
                exportHistory: []
            }
        };
        // 验证迁移后的数据
        const validation = data_converters_1.DataValidator.validateTaskStorage(standardData);
        if (!validation.isValid) {
            console.warn(`[DataMigrator] ⚠️ Migration validation failed for task ${task.id}:`, validation.errors);
        }
        console.log(`[DataMigrator] ✅ Task ${task.id} migrated successfully. Quality score: ${standardData.statistics.dataQualityScore}`);
        return standardData;
    }
    /**
     * 转换旧格式的链接数据为标准商品数据
     */
    static convertLegacyLinksToProducts(legacyLinks, keywords) {
        return legacyLinks.map((linkData, index) => ({
            id: `migrated_product_${Date.now()}_${index}`,
            platform: linkData.platform,
            title: `商品_${index + 1}`, // 旧数据没有标题，使用占位符
            url: linkData.link,
            discoveredAt: new Date().toISOString(),
            discoveryKeyword: keywords.join(', ')
        }));
    }
    /**
     * 转换旧格式的评论数据为标准评论数据
     */
    static convertLegacyCommentsToStandard(legacyComments) {
        return legacyComments.map((comment, index) => ({
            id: `migrated_comment_${Date.now()}_${index}`,
            platform: this.extractPlatformFromSource(comment.source),
            productId: 'unknown',
            productTitle: '未知商品',
            content: comment.content,
            engagement: {
                likes: 0,
                replies: 0,
                helpful: 0
            },
            metadata: {
                date: new Date().toISOString(),
                isVerifiedPurchase: false,
                hasImages: false,
                hasVideo: false
            },
            scrapedAt: new Date().toISOString()
        }));
    }
    /**
     * 转换旧格式的报告数据为标准分析结果
     */
    static convertLegacyReportToAnalysis(reportData) {
        if (!reportData)
            return [];
        const analysisResult = {
            type: 'market_insights',
            input: {
                keywords: [],
                comments: []
            },
            output: {
                insights: reportData.insights?.map((insight) => insight.description) || [],
                marketOpportunities: [reportData.summary || '']
            },
            confidence: 0.8, // 默认置信度
            processingTime: 0,
            timestamp: new Date().toISOString()
        };
        return [analysisResult];
    }
    /**
     * 从任务中提取平台信息
     */
    static extractPlatformsFromTask(task) {
        const platforms = new Set();
        // 从链接数据中提取平台
        task.discoveredLinks?.forEach(link => {
            platforms.add(link.platform);
        });
        // 从评论数据中提取平台
        task.scrapedComments?.forEach(comment => {
            const platform = this.extractPlatformFromSource(comment.source);
            platforms.add(platform);
        });
        // 如果没有找到平台信息，默认使用淘宝
        if (platforms.size === 0) {
            platforms.add('taobao');
        }
        return Array.from(platforms);
    }
    /**
     * 从source字符串中提取平台信息
     */
    static extractPlatformFromSource(source) {
        if (source.includes('xiaohongshu') || source.includes('小红书')) {
            return 'xiaohongshu';
        }
        return 'taobao'; // 默认淘宝
    }
    /**
     * 计算任务成功率
     */
    static calculateSuccessRate(task) {
        const totalExpected = (task.discoveredLinks?.length || 0) + (task.scrapedComments?.length || 0);
        if (totalExpected === 0)
            return 1.0;
        // 简化计算：如果任务完成，认为成功率为1.0
        if (task.status === 'COMPLETED')
            return 1.0;
        if (task.status === 'FAILED')
            return 0.0;
        return 0.8; // 默认成功率
    }
    /**
     * 估算处理时间
     */
    static estimateProcessingTime(task) {
        const createdAt = new Date(task.createdAt);
        const updatedAt = new Date(task.updatedAt);
        return updatedAt.getTime() - createdAt.getTime();
    }
    /**
     * 计算数据质量分数
     */
    static calculateDataQuality(products, comments) {
        let score = 0.5; // 基础分
        // 数据完整性评分
        if (products.length > 0)
            score += 0.2;
        if (comments.length > 0)
            score += 0.2;
        // 数据丰富度评分
        if (products.length > 10)
            score += 0.1;
        if (comments.length > 50)
            score += 0.1;
        return Math.min(score, 1.0);
    }
    /**
     * 批量迁移任务数据
     */
    static async batchMigrateTasks(tasks) {
        console.log(`[DataMigrator] 🔄 Starting batch migration for ${tasks.length} tasks`);
        const migratedTasks = [];
        let successCount = 0;
        let errorCount = 0;
        for (const task of tasks) {
            try {
                const standardData = this.migrateTaskToStandardFormat(task);
                const migratedTask = {
                    ...task,
                    standardData
                };
                migratedTasks.push(migratedTask);
                successCount++;
            }
            catch (error) {
                console.error(`[DataMigrator] ❌ Failed to migrate task ${task.id}:`, error);
                migratedTasks.push(task); // 保留原始数据
                errorCount++;
            }
        }
        console.log(`[DataMigrator] ✅ Batch migration completed: ${successCount} success, ${errorCount} errors`);
        return migratedTasks;
    }
    /**
     * 检查任务是否需要迁移
     */
    static needsMigration(task) {
        return !task.standardData || task.standardData.version !== data_format_standards_1.DATA_FORMAT_VERSION;
    }
    /**
     * 获取迁移统计信息
     */
    static getMigrationStats(tasks) {
        const total = tasks.length;
        const needsMigration = tasks.filter(task => this.needsMigration(task)).length;
        const alreadyMigrated = total - needsMigration;
        const migrationRate = total > 0 ? (alreadyMigrated / total) * 100 : 0;
        return {
            total,
            needsMigration,
            alreadyMigrated,
            migrationRate
        };
    }
}
exports.DataMigrator = DataMigrator;
/**
 * 🎯 数据兼容性助手
 * 提供新旧数据格式之间的兼容性方法
 */
class DataCompatibilityHelper {
    /**
     * 从Task中获取标准化数据，如果没有则自动迁移
     */
    static getStandardData(task) {
        if (task.standardData) {
            return task.standardData;
        }
        // 自动迁移
        return DataMigrator.migrateTaskToStandardFormat(task);
    }
    /**
     * 获取商品数据（兼容新旧格式）
     */
    static getProducts(task) {
        const standardData = this.getStandardData(task);
        return standardData.discoveredProducts;
    }
    /**
     * 获取评论数据（兼容新旧格式）
     */
    static getComments(task) {
        const standardData = this.getStandardData(task);
        return standardData.scrapedComments;
    }
    /**
     * 获取分析结果（兼容新旧格式）
     */
    static getAnalysisResults(task) {
        const standardData = this.getStandardData(task);
        return standardData.analysisResults;
    }
    /**
     * 获取统计信息（兼容新旧格式）
     */
    static getStatistics(task) {
        const standardData = this.getStandardData(task);
        return standardData.statistics;
    }
}
exports.DataCompatibilityHelper = DataCompatibilityHelper;
// 导出便捷函数
exports.migrateTask = DataMigrator.migrateTaskToStandardFormat;
exports.getStandardData = DataCompatibilityHelper.getStandardData;
