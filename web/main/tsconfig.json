{"extends": "../tsconfig.json", "compilerOptions": {"module": "commonjs", "moduleResolution": "node", "outDir": "./dist", "noEmit": false, "baseUrl": "..", "paths": {"@/*": ["*"]}, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true}, "include": ["./**/*.ts", "../lib/**/*.ts", "../types/**/*.ts", "../config.ts"], "exclude": ["../node_modules", "../components/**/*", "../app/**/*", "../hooks/**/*"]}