import { TaskDataStorage } from '../lib/data-format-standards';

export interface Task {
  id: string
  jobId: string
  status: "PENDING" | "EXPENTING" | "DISCOVERING" | "SCRAPING" | "ANALYZING" | "COMPLETED" | "FAILED" | "WAITING_CONFIRMATION" | "WAITING_USER_LOGIN" | "WAITING_CAPTCHA" | "PAUSED" | "CANCELLED"
  scanMode: "quick" | "deep"
  source: "manual" | "file"
  keywords: string[] // 兼容性属性，指向 initialKeywords
  initialKeywords: string[]
  finalKeywords?: string[]
  processedKeywords?: string[] | KeywordConfirmation[]

  // 🔥 向后兼容的旧格式字段（逐步废弃）
  pausedLinks?: { platform: 'taobao' | 'xiaohongshu'; link: string; }[]
  discoveredLinks?: { platform: 'taobao' | 'xiaohongshu'; link: string; }[]
  scrapedComments?: { content: string; source: string; }[]
  reportData?: InsightReport

  // 🔥 新的标准化数据存储字段
  standardData?: TaskDataStorage

  progress?: {
    discovered?: number
    discovered_total?: number
    scraped?: number
    scraped_total?: number
    total?: number
    message?: string
  }
  errorMessage?: string
  createdAt: string
  updatedAt: string
}

export interface KeywordConfirmation {
  theme: string
  keywords: string[]
  selected?: boolean
}

export interface InsightReport {
  summary: string
  demandKeywords: Array<{
    word: string
    frequency: number
  }>
  insights: Array<{
    id: string
    title: string
    description: string
    evidence: string[]
  }>
}
