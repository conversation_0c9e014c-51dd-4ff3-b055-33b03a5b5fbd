import { Task } from './task';

// EnhancedTaskMonitor 数据类型定义
export interface WorkerStatus {
  id: string;
  platform: string;
  isHealthy: boolean;
  isAvailable: boolean;
  successCount: number;
  failureCount: number;
  successRate: string;
  uptime: number;
  lastActivity: number;
}

export interface SystemStatus {
  isOnline: boolean;
  lastHeartbeat: Date;
  activeConnections: number;
  memoryUsage: number;
  cpuUsage: number;
  diskUsage: number;
}

export interface RealTimeStats {
  tasksProcessed: number;
  successRate: number;
  avgResponseTime: number;
  errorsInLastHour: number;
  activeWorkers: number;
  queueLength: number;
}

// AI输出相关类型定义
export interface AIOutput {
  id: string;
  type: 'thinking' | 'decision' | 'action' | 'result' | 'error';
  content: string;
  metadata: {
    timestamp: string;
    taskId?: string;
    toolName?: string;
    duration?: number;
    confidence?: number;
  };
  structured?: any;
}

export interface IElectronAPI {
  getTasks: () => Promise<Task[]>;
  getTaskById: (taskId: string) => Promise<Task | null>;
  startTask: (taskData: Partial<Task>) => Promise<Task>;
  deleteTask: (taskId: string, options?: { force?: boolean }) => Promise<void>;
  confirmKeywords: (data: { taskId: string; keywords: string[] }) => Promise<Task | null>;
  resumeTask: (taskId: string) => Promise<void>;
  retryTask: (taskId: string) => Promise<void>;
  onTasksUpdated: (callback: (tasks: Task[]) => void) => () => void;
  onTaskUpdated: (taskId: string, callback: (task: Task) => void) => () => void;
  removeAllListeners?: (channel: string) => void;

  // V2.0 登录管理相关方法
  startLoginSession?: (platform: string) => Promise<{ success: boolean; message?: string; error?: string }>;
  checkLoginStatus?: () => Promise<Array<{ platform: string; isLoggedIn: boolean; lastLoginTime?: string; sessionValid?: boolean; fileExists?: boolean; error?: string }>>;
  clearLoginSession?: (platform: string) => Promise<{ success: boolean; message?: string; error?: string }>;

  // 🔥 智能文件解析 API
  parseFileForKeywords?: (filePath: string) => Promise<string[]>;

  // EnhancedTaskMonitor 真实数据 API
  getWorkerStatuses?: (taskId?: string) => Promise<WorkerStatus[]>;
  getSystemStatus?: () => Promise<SystemStatus>;
  getRealTimeStats?: () => Promise<RealTimeStats>;
  controlTask?: (taskId: string, action: 'stop') => Promise<void>;
  restartWorker?: (workerId: string) => Promise<void>;
  exportTaskData?: (taskId: string, dataType: 'links' | 'comments' | 'insights' | 'all', format: 'excel' | 'csv' | 'json') => Promise<string>;

  // AI输出相关 API
  onAIOutput?: (callback: (event: any, data: AIOutput) => void) => () => void;
  onTaskProgress?: (callback: (event: any, progress: any) => void) => () => void;

  // 浏览器可视化控制 API
  toggleBrowserVisualization?: (headless: boolean) => Promise<{
    success: boolean;
    message: string;
    taskInterrupted: boolean;
    error?: string;
  }>;

  // 🔧 新增：通用事件监听 API
  on?: (channel: string, callback: (event: any, ...args: any[]) => void) => () => void;
  off?: (channel: string, callback: (event: any, ...args: any[]) => void) => void;
}

declare global {
  interface Window {
    electron: IElectronAPI;
    electronAPI: IElectronAPI; // V2.0 兼容性别名
  }

  // Extend the File interface to include the `path` property for Electron
  interface File {
    path: string;
  }
} 