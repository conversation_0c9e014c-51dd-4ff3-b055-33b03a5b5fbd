"use client"

import React from 'react';
import { useParams } from 'next/navigation';
import { Sidebar } from '@/components/Sidebar';
import { TaskMonitor } from '@/components/TaskMonitor';

export default function DashboardPage() {
  const params = useParams();
  const jobId = params.id as string;

  return (
    <div className="flex h-screen bg-white">
      <Sidebar />
      <main className="flex-1 flex flex-col">
        <div className="flex-1 p-6 bg-[#FDFBF4] overflow-auto">
          <TaskMonitor jobId={jobId} />
        </div>
      </main>
    </div>
  );
}
