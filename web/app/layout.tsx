import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { JetBrains_Mono } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { ChatHistoryProvider } from "@/contexts/ChatHistoryContext"
import { Toaster } from "@/components/ui/toaster"

const jetbrainsMono = JetBrains_Mono({
  subsets: ["latin"],
  weight: ["400", "700"]
})

export const metadata: Metadata = {
  title: "聚需求",
  description: "电商需求洞察，发现潜在商机",
  generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={jetbrainsMono.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          forcedTheme="light"
          disableTransitionOnChange
        >
          <ChatHistoryProvider>
            {children}
            <Toaster />
          </ChatHistoryProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
