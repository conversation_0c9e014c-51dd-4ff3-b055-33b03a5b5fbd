'use client'

import { AppLayout } from "@/components/AppLayout"
import { PromptInputForm } from "@/components/PromptInputForm"
import { LoginSuggestionCard } from "@/components/LoginSuggestionCard"

export default function HomePage() {
  return (
    <AppLayout hideHeader>
      <div className="flex-1 flex flex-col items-center justify-center h-full">
        <div className="max-w-4xl w-full">
          <div className="text-center mb-8">
            <h1 className="text-2xl md:text-3xl font-bold text-[#333333] leading-tight">
              电商需求洞察，发现潜在商机
            </h1>
          </div>

          {/* 登录建议卡片 - 与输入框宽度保持一致 */}
          <div className="max-w-4xl w-full">
            <LoginSuggestionCard />
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6 md:p-8 border">
            <PromptInputForm />
          </div>
        </div>
      </div>
    </AppLayout>
  )
}
