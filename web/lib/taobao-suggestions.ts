/**
 * 淘宝下拉词获取服务
 * 通过模拟在淘宝搜索框输入关键词来获取下拉提示词
 */

import { Page } from 'playwright';

export interface TaobaoSuggestion {
  keyword: string;
  fullText: string;
}

export class TaobaoSuggestionsService {
  private page: Page | null = null;

  constructor(page?: Page) {
    this.page = page || null;
  }

  /**
   * 获取多个关键词的淘宝下拉提示
   * @param keywords 关键词数组
   * @returns 去重后的下拉提示词数组
   */
  async getSuggestions(keywords: string[]): Promise<string[]> {
    const allSuggestions: string[] = [];
    
    for (const keyword of keywords) {
      try {
        console.log(`正在获取"${keyword}"的淘宝下拉词...`);
        const suggestions = await this.fetchSuggestionsForKeyword(keyword);
        allSuggestions.push(...suggestions);
        
        // 添加延迟避免请求过快
        await this.delay(1000);
      } catch (error) {
        console.error(`获取"${keyword}"的下拉词失败:`, error);
      }
    }
    
    // 去重并返回
    const uniqueSuggestions = [...new Set(allSuggestions)];
    console.log(`总共获取到 ${uniqueSuggestions.length} 个去重后的下拉词`);
    return uniqueSuggestions;
  }

  /**
   * 获取单个关键词的淘宝下拉提示
   * @param keyword 关键词
   * @returns 下拉提示词数组
   */
  private async fetchSuggestionsForKeyword(keyword: string): Promise<string[]> {
    if (!this.page) {
      throw new Error('Playwright page not initialized');
    }

    try {
      // 导航到淘宝首页
      await this.page.goto('https://www.taobao.com', { 
        waitUntil: 'domcontentloaded',
        timeout: 30000 
      });

      // 等待搜索框加载
      await this.page.waitForSelector('input[name="q"], .search-combobox-input, [role="combobox"]', { 
        timeout: 10000 
      });

      // 清空搜索框并输入关键词
      const searchBox = this.page.locator('input[name="q"], .search-combobox-input, [role="combobox"]').first();
      await searchBox.click();
      await searchBox.fill('');
      await searchBox.fill(keyword);

      // 等待下拉提示出现
      await this.delay(2000);

      // 提取下拉提示词
      const suggestions = await this.extractSuggestions();
      
      console.log(`"${keyword}" 获取到 ${suggestions.length} 个下拉词`);
      return suggestions;

    } catch (error) {
      console.error(`获取关键词 "${keyword}" 的下拉词时出错:`, error);
      return [];
    }
  }

  /**
   * 从页面中提取下拉提示词
   * @returns 提示词数组
   */
  private async extractSuggestions(): Promise<string[]> {
    if (!this.page) return [];

    try {
      // 等待下拉提示容器出现 - 使用实际的淘宝选择器
      await this.page.waitForSelector('.search-suggest-popup, .search-suggest-menu, [class*="search-suggest"]', {
        timeout: 5000
      });

      // 提取所有下拉提示文本
      const suggestions = await this.page.evaluate(() => {
        const results: string[] = [];

        // 🔥 使用实际测试发现的淘宝下拉建议选择器
        const suggestionItems = document.querySelectorAll([
          '.search-suggest-menu-item .item-text',  // 最精确的选择器
          '.search-suggest-menu-item',             // 备用选择器
          '.search-suggest-popup .item-text',      // 另一个备用选择器
          '[class*="search-suggest"] [class*="item-text"]'  // 通用选择器
        ].join(', '));

        suggestionItems.forEach(item => {
          const text = item.textContent?.trim();
          if (text && text.length > 0 && text.length < 100) {
            // 清理文本，移除多余空格和换行符
            const cleanText = text.replace(/\s+/g, ' ').trim();
            if (cleanText && !results.includes(cleanText)) {
              results.push(cleanText);
            }
          }
        });

        // 🔥 如果上面的选择器没找到，尝试更通用的方法
        if (results.length === 0) {
          const searchContainer = document.querySelector('.search-bd.search-suggest');
          if (searchContainer) {
            const allElements = searchContainer.querySelectorAll('*');
            const targetTexts = new Set<string>();

            allElements.forEach(element => {
              const text = element.textContent?.trim();
              if (text && text.length > 1 && text.length < 50 &&
                  !text.includes('\n') && !text.includes('  ')) {
                // 检查是否是单个关键词建议
                if (/^[\u4e00-\u9fa5a-zA-Z0-9\s]{2,20}$/.test(text)) {
                  targetTexts.add(text);
                }
              }
            });

            results.push(...Array.from(targetTexts));
          }
        }

        return results;
      });

      // 过滤和清理建议词
      const filteredSuggestions = suggestions
        .filter(suggestion =>
          suggestion &&
          suggestion.length > 1 &&
          suggestion.length < 50 &&
          !suggestion.includes('搜索') &&
          !suggestion.includes('热门') &&
          !suggestion.includes('推荐') &&
          !suggestion.includes('历史') &&
          !suggestion.includes('清除')
        )
        .slice(0, 10); // 限制最多10个

      console.log(`🔍 提取到 ${filteredSuggestions.length} 个下拉建议:`, filteredSuggestions);
      return filteredSuggestions;

    } catch (error) {
      console.error('提取下拉提示词时出错:', error);
      return [];
    }
  }

  /**
   * 延迟函数
   * @param ms 毫秒数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 设置Playwright页面实例
   * @param page Playwright页面实例
   */
  setPage(page: Page): void {
    this.page = page;
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    // 这里不关闭page，因为可能被其他地方使用
    this.page = null;
  }
}

/**
 * 创建淘宝下拉词获取服务实例
 * @param page 可选的Playwright页面实例
 * @returns TaobaoSuggestionsService实例
 */
export function createTaobaoSuggestionsService(page?: Page): TaobaoSuggestionsService {
  return new TaobaoSuggestionsService(page);
}
