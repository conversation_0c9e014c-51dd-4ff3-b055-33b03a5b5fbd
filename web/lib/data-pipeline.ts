/**
 * 🔥 数据转换管道
 * 
 * 构建完整的数据流转换链：
 * 爬虫原始数据 → 标准化存储 → AI分析 → 用户导出
 * 
 * 这是整个数据格式规范化的核心管道，确保数据在各个环节
 * 都使用正确的格式，消除数据格式混乱问题。
 * 
 * 作者: 毒舌界祖师爷的小贱狗 🐕
 * 创建时间: 2025-07-23
 */

import { Task } from '../types/task';
import { Platform } from './browser-manager';
import {
  StandardProductData,
  StandardCommentData,
  StandardAnalysisResult,
  TaskDataStorage,
  AIInputFormat,
  ExportFormat
} from './data-format-standards';
import {
  aiInputConverter,
  storageConverter,
  exportConverter,
  DataValidator
} from './data-converters';
import { DataMigrator } from './data-migration';
import { ExportManager } from './export-manager';

/**
 * 🎯 管道阶段枚举
 */
export enum PipelineStage {
  RAW_DATA = 'raw_data',
  STANDARDIZED = 'standardized',
  AI_READY = 'ai_ready',
  ANALYZED = 'analyzed',
  EXPORT_READY = 'export_ready'
}

/**
 * 🎯 管道处理结果
 */
export interface PipelineResult<T = any> {
  success: boolean;
  stage: PipelineStage;
  data: T;
  metadata: {
    processingTime: number;
    dataQuality: number;
    recordCount: number;
    timestamp: string;
  };
  errors?: string[];
  warnings?: string[];
}

/**
 * 🎯 爬虫原始数据格式
 */
export interface RawScrapingData {
  taskId: string;
  keywords: string[];
  platforms: Platform[];
  discoveredLinks: any[]; // 原始链接数据
  scrapedComments: any[]; // 原始评论数据
  metadata: {
    startTime: string;
    endTime: string;
    successRate: number;
  };
}

/**
 * 🔥 数据转换管道核心类
 */
export class DataPipeline {
  
  /**
   * 🚀 完整管道处理：从原始数据到导出就绪
   */
  static async processComplete(rawData: RawScrapingData): Promise<PipelineResult<TaskDataStorage>> {
    console.log(`[DataPipeline] 🚀 Starting complete pipeline for task ${rawData.taskId}`);
    const startTime = Date.now();
    
    try {
      // 阶段1: 原始数据 → 标准化存储
      const standardizedResult = await this.standardizeRawData(rawData);
      if (!standardizedResult.success) {
        throw new Error(`Standardization failed: ${standardizedResult.errors?.join(', ')}`);
      }
      
      // 阶段2: 数据验证
      const validation = DataValidator.validateTaskStorage(standardizedResult.data);
      if (!validation.isValid) {
        console.warn(`[DataPipeline] ⚠️ Data validation warnings:`, validation.warnings);
      }
      
      const processingTime = Date.now() - startTime;
      console.log(`[DataPipeline] ✅ Complete pipeline finished in ${processingTime}ms`);
      
      return {
        success: true,
        stage: PipelineStage.EXPORT_READY,
        data: standardizedResult.data,
        metadata: {
          processingTime,
          dataQuality: validation.dataIntegrityScore,
          recordCount: standardizedResult.data.discoveredProducts.length + standardizedResult.data.scrapedComments.length,
          timestamp: new Date().toISOString()
        },
        warnings: validation.warnings
      };
      
    } catch (error) {
      console.error(`[DataPipeline] ❌ Complete pipeline failed:`, error);
      return {
        success: false,
        stage: PipelineStage.RAW_DATA,
        data: {} as TaskDataStorage,
        metadata: {
          processingTime: Date.now() - startTime,
          dataQuality: 0,
          recordCount: 0,
          timestamp: new Date().toISOString()
        },
        errors: [error instanceof Error ? error.message : String(error)]
      };
    }
  }
  
  /**
   * 🔄 阶段1: 原始数据标准化
   */
  static async standardizeRawData(rawData: RawScrapingData): Promise<PipelineResult<TaskDataStorage>> {
    console.log(`[DataPipeline] 🔄 Stage 1: Standardizing raw data`);
    const startTime = Date.now();
    
    try {
      // 转换商品数据
      const standardProducts = this.convertRawLinksToProducts(rawData.discoveredLinks, rawData.keywords);
      
      // 转换评论数据
      const standardComments = this.convertRawCommentsToStandard(rawData.scrapedComments);
      
      // 创建标准化存储结构
      const standardData: TaskDataStorage = {
        taskId: rawData.taskId,
        version: '1.0.0',
        basicInfo: {
          keywords: rawData.keywords,
          platforms: rawData.platforms,
          createdAt: rawData.metadata.startTime,
          updatedAt: rawData.metadata.endTime,
          status: 'PROCESSING'
        },
        discoveredProducts: standardProducts,
        scrapedComments: standardComments,
        analysisResults: [], // 初始为空，等待AI分析
        statistics: {
          totalProducts: standardProducts.length,
          totalComments: standardComments.length,
          successRate: rawData.metadata.successRate,
          processingTime: Date.now() - new Date(rawData.metadata.startTime).getTime(),
          dataQualityScore: this.calculateDataQuality(standardProducts, standardComments)
        },
        metadata: {
          dataIntegrity: true,
          lastValidation: new Date().toISOString(),
          exportHistory: []
        }
      };
      
      const processingTime = Date.now() - startTime;
      console.log(`[DataPipeline] ✅ Stage 1 completed: ${standardProducts.length} products, ${standardComments.length} comments`);
      
      return {
        success: true,
        stage: PipelineStage.STANDARDIZED,
        data: standardData,
        metadata: {
          processingTime,
          dataQuality: standardData.statistics.dataQualityScore,
          recordCount: standardProducts.length + standardComments.length,
          timestamp: new Date().toISOString()
        }
      };
      
    } catch (error) {
      console.error(`[DataPipeline] ❌ Stage 1 failed:`, error);
      return {
        success: false,
        stage: PipelineStage.RAW_DATA,
        data: null as any,
        metadata: {
          processingTime: Date.now() - startTime,
          dataQuality: 0,
          recordCount: 0,
          timestamp: new Date().toISOString()
        },
        errors: [error instanceof Error ? error.message : String(error)]
      };
    }
  }
  
  /**
   * 🔄 阶段2: 准备AI分析输入
   */
  static async prepareAIInput(standardData: TaskDataStorage, analysisType: 'keyword_analysis' | 'product_discovery' | 'comment_analysis'): Promise<PipelineResult<AIInputFormat>> {
    console.log(`[DataPipeline] 🔄 Stage 2: Preparing AI input for ${analysisType}`);
    const startTime = Date.now();
    
    try {
      let aiInput: AIInputFormat;
      
      switch (analysisType) {
        case 'keyword_analysis':
          aiInput = aiInputConverter.convertKeywordAnalysis(standardData.basicInfo.keywords);
          break;
        case 'product_discovery':
          aiInput = aiInputConverter.convertProductDiscovery(standardData.discoveredProducts, standardData.basicInfo.keywords);
          break;
        case 'comment_analysis':
          aiInput = aiInputConverter.convertCommentAnalysis(standardData.scrapedComments);
          break;
        default:
          throw new Error(`Unsupported analysis type: ${analysisType}`);
      }
      
      // 验证AI输入格式
      const validation = DataValidator.validateAIInput(aiInput);
      if (!validation.isValid) {
        throw new Error(`AI input validation failed: ${validation.errors.join(', ')}`);
      }
      
      const processingTime = Date.now() - startTime;
      console.log(`[DataPipeline] ✅ Stage 2 completed: AI input prepared for ${analysisType}`);
      
      return {
        success: true,
        stage: PipelineStage.AI_READY,
        data: aiInput,
        metadata: {
          processingTime,
          dataQuality: validation.dataIntegrityScore,
          recordCount: 1, // AI输入是单个对象
          timestamp: new Date().toISOString()
        },
        warnings: validation.warnings
      };
      
    } catch (error) {
      console.error(`[DataPipeline] ❌ Stage 2 failed:`, error);
      return {
        success: false,
        stage: PipelineStage.STANDARDIZED,
        data: null as any,
        metadata: {
          processingTime: Date.now() - startTime,
          dataQuality: 0,
          recordCount: 0,
          timestamp: new Date().toISOString()
        },
        errors: [error instanceof Error ? error.message : String(error)]
      };
    }
  }
  
  /**
   * 🔄 阶段3: 处理AI分析结果
   */
  static async processAIResult(standardData: TaskDataStorage, aiResult: any, analysisType: string): Promise<PipelineResult<TaskDataStorage>> {
    console.log(`[DataPipeline] 🔄 Stage 3: Processing AI result for ${analysisType}`);
    const startTime = Date.now();
    
    try {
      // 创建标准化分析结果
      const analysisResult: StandardAnalysisResult = {
        type: analysisType as any,
        input: {
          keywords: standardData.basicInfo.keywords,
          products: standardData.discoveredProducts.map(p => p.title),
          comments: standardData.scrapedComments.map(c => c.content)
        },
        output: aiResult,
        confidence: aiResult.confidence || 0.8,
        processingTime: Date.now() - startTime,
        timestamp: new Date().toISOString()
      };
      
      // 更新标准数据
      const updatedData: TaskDataStorage = {
        ...standardData,
        analysisResults: [...standardData.analysisResults, analysisResult],
        metadata: {
          ...standardData.metadata,
          lastValidation: new Date().toISOString()
        }
      };
      
      const processingTime = Date.now() - startTime;
      console.log(`[DataPipeline] ✅ Stage 3 completed: AI result processed`);
      
      return {
        success: true,
        stage: PipelineStage.ANALYZED,
        data: updatedData,
        metadata: {
          processingTime,
          dataQuality: standardData.statistics.dataQualityScore,
          recordCount: updatedData.analysisResults.length,
          timestamp: new Date().toISOString()
        }
      };
      
    } catch (error) {
      console.error(`[DataPipeline] ❌ Stage 3 failed:`, error);
      return {
        success: false,
        stage: PipelineStage.AI_READY,
        data: standardData,
        metadata: {
          processingTime: Date.now() - startTime,
          dataQuality: 0,
          recordCount: 0,
          timestamp: new Date().toISOString()
        },
        errors: [error instanceof Error ? error.message : String(error)]
      };
    }
  }
  
  /**
   * 🔄 阶段4: 准备导出数据
   */
  static async prepareExportData(standardData: TaskDataStorage): Promise<PipelineResult<ExportFormat>> {
    console.log(`[DataPipeline] 🔄 Stage 4: Preparing export data`);
    const startTime = Date.now();
    
    try {
      const exportData = exportConverter.convert(standardData);
      
      // 验证导出数据
      const validation = exportConverter.validate(exportData);
      if (!validation) {
        throw new Error('Export data validation failed');
      }
      
      const totalRecords = exportData.products.length + exportData.comments.length + exportData.insights.length;
      const processingTime = Date.now() - startTime;
      
      console.log(`[DataPipeline] ✅ Stage 4 completed: Export data prepared (${totalRecords} records)`);
      
      return {
        success: true,
        stage: PipelineStage.EXPORT_READY,
        data: exportData,
        metadata: {
          processingTime,
          dataQuality: standardData.statistics.dataQualityScore,
          recordCount: totalRecords,
          timestamp: new Date().toISOString()
        }
      };
      
    } catch (error) {
      console.error(`[DataPipeline] ❌ Stage 4 failed:`, error);
      return {
        success: false,
        stage: PipelineStage.ANALYZED,
        data: null as any,
        metadata: {
          processingTime: Date.now() - startTime,
          dataQuality: 0,
          recordCount: 0,
          timestamp: new Date().toISOString()
        },
        errors: [error instanceof Error ? error.message : String(error)]
      };
    }
  }
  
  /**
   * 转换原始链接为标准商品数据
   */
  private static convertRawLinksToProducts(rawLinks: any[], keywords: string[]): StandardProductData[] {
    return rawLinks.map((link, index) => ({
      id: `product_${Date.now()}_${index}`,
      platform: link.platform || 'taobao',
      title: link.title || link.name || `商品_${index + 1}`,
      url: link.url || link.link || '',
      price: link.price ? {
        current: parseFloat(String(link.price).replace(/[^\d.]/g, '')) || 0,
        currency: 'CNY'
      } : undefined,
      sales: link.sales ? {
        count: parseInt(String(link.sales).replace(/[^\d]/g, '')) || 0,
        period: '月销量'
      } : undefined,
      rating: link.rating ? {
        score: parseFloat(String(link.rating)) || 0,
        maxScore: 5,
        reviewCount: link.reviewCount || 0
      } : undefined,
      images: link.images || [],
      category: link.category || '',
      brand: link.brand || '',
      discoveredAt: new Date().toISOString(),
      discoveryKeyword: keywords.join(', ')
    }));
  }
  
  /**
   * 转换原始评论为标准评论数据
   */
  private static convertRawCommentsToStandard(rawComments: any[]): StandardCommentData[] {
    return rawComments.map((comment, index) => ({
      id: `comment_${Date.now()}_${index}`,
      platform: comment.platform || 'taobao',
      productId: comment.productId || 'unknown',
      productTitle: comment.productTitle || '未知商品',
      content: comment.content || comment.text || '',
      rating: comment.rating ? {
        score: parseFloat(String(comment.rating)) || 0,
        maxScore: 5
      } : undefined,
      author: comment.author ? {
        name: comment.author.name || '匿名用户',
        level: comment.author.level || '',
        verified: comment.author.verified || false
      } : undefined,
      engagement: {
        likes: comment.likes || 0,
        replies: comment.replies || 0,
        helpful: comment.helpful || 0
      },
      metadata: {
        date: comment.date || new Date().toISOString(),
        isVerifiedPurchase: comment.isVerifiedPurchase || false,
        hasImages: comment.hasImages || false,
        hasVideo: comment.hasVideo || false
      },
      scrapedAt: new Date().toISOString()
    }));
  }
  
  /**
   * 计算数据质量分数
   */
  private static calculateDataQuality(products: StandardProductData[], comments: StandardCommentData[]): number {
    let score = 0.5; // 基础分
    
    // 商品数据质量
    if (products.length > 0) {
      const hasPrice = products.filter(p => p.price).length / products.length;
      const hasRating = products.filter(p => p.rating).length / products.length;
      score += (hasPrice + hasRating) * 0.2;
    }
    
    // 评论数据质量
    if (comments.length > 0) {
      const hasRating = comments.filter(c => c.rating).length / comments.length;
      const hasAuthor = comments.filter(c => c.author).length / comments.length;
      score += (hasRating + hasAuthor) * 0.15;
    }
    
    return Math.min(score, 1.0);
  }
  
  /**
   * 获取管道状态摘要
   */
  static getPipelineStatus(results: PipelineResult[]): {
    currentStage: PipelineStage;
    completedStages: PipelineStage[];
    totalProcessingTime: number;
    overallQuality: number;
    totalRecords: number;
  } {
    const completedStages = results.filter(r => r.success).map(r => r.stage);
    const currentStage = completedStages[completedStages.length - 1] || PipelineStage.RAW_DATA;
    const totalProcessingTime = results.reduce((sum, r) => sum + r.metadata.processingTime, 0);
    const overallQuality = results.length > 0 
      ? results.reduce((sum, r) => sum + r.metadata.dataQuality, 0) / results.length 
      : 0;
    const totalRecords = results.reduce((sum, r) => sum + r.metadata.recordCount, 0);
    
    return {
      currentStage,
      completedStages,
      totalProcessingTime,
      overallQuality,
      totalRecords
    };
  }
}

// 导出便捷函数
export const processRawData = DataPipeline.standardizeRawData;
export const prepareAIInput = DataPipeline.prepareAIInput;
export const processAIResult = DataPipeline.processAIResult;
export const prepareExportData = DataPipeline.prepareExportData;
