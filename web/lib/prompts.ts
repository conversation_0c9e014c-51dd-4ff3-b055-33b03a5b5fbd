/**
 * Prompt Engineering & Management for Demand Insight Assistant
 * 按照 PRD 第9节要求，管理所有 AI 分析流程的核心 Prompt
 */

/**
 * 少量关键词扩展 Prompt
 * 用于深度扫描模式下，将1-3个核心词扩展为10个相关搜索词
 */
export const getExpansionPrompt = (keyword: string): string => {
  const systemPrompt = `你是一位顶级的电商行业专家，精通用户搜索行为和市场趋势。`;

  const userPrompt = `基于核心词 \`${keyword}\`，完成以下任务:（1）分析核心词: 首先，简要分析这个核心词可能对应的主要产品类别和用户画像。（2）识别扩展维度: 识别出与该核心词最相关的3-5个关键扩展维度。例如：材质、风格、功能、适用人群、使用场景等。如果我提供的示例维度（如通勤、度假）不适用，请你根据核心词自行判断并替换为更合适的维度。（3）生成关键词: 基于以上分析，为我扩展出10-15个最具搜索潜力的长尾关键词。这些关键词应该：清晰地指向一个细分需求；避免过于宽泛或与核心词无关；组合不同的维度以发掘交叉机会点。

输出格式要求: 请将最终的10个关键词以一个严格的JSON格式字符串数组返回，不要包含任何解释性文字、序号或标记。

输出结构示例:
["关键词1", "关键词2", "关键词3", ...]`;

  return `${systemPrompt}\n\n${userPrompt}`;
}

/**
 * 大量关键词聚类 Prompt
 * 用于深度扫描模式下，将数百个关键词归纳为5-7个核心主题
 */
export const getClusteringPrompt = (keywords: string[]): string => {
  const systemPrompt = `你是一位顶级的市场分析师，擅长从海量关键词中归纳总结。`;

  const userPrompt = `这里有一份包含数百个搜索关键词的列表。请完成以下任务:（1）全面分析: 系统性地阅读并理解下方提供的所有关键词。（2）主题聚类: 将这些关键词聚类成若干个有商业意义的、清晰的"用户需求主题"或"细分市场"。（3）主题数量: 理想的主题数量在 8-12个 之间，但你应根据数据的实际分布来决定最终数量，确保每个主题都有足够的代表性和区分度。宁缺毋滥。（4）主题命名: 每个主题的名称应高度概括，直接反映用户的核心意图或产品分类（例如："便携办公场景"、"天然有机材质"、"复古设计风格"）。（5）样本选择: 对于你归纳出的每一个主题，从原始列表中挑选 3-5个 最具代表性的关键词作为样本。这些样本应该能共同展示该主题的广度和深度。

输出格式要求: 以严格的JSON格式返回结果，不要包含任何额外解释。

输出结构示例:
[{"theme": "主题名1", "keywords": ["代表词A", "代表词B"]}, {"theme": "主题名2", "keywords": ["代表词C", "代表词D", "代表词E"]}, ...]

原始关键词列表如下：
${keywords.join(', ')}。`;

  return `${systemPrompt}\n\n${userPrompt}`;
}

/**
 * Map 阶段：单品评论分析 Prompt
 * 对单个商品的评论进行微型分析，提取优缺点和未满足需求
 */
export const getMapAnalysisPrompt = (comments: string[], productTitle?: string): string => {
  const systemPrompt = `你是一个冷靜、客观、精准的商品评论分析引擎。你的唯一任务是根据用户真实评论，结构化地提炼关于产品本身的核心信息。`;

  const userPrompt = `从下方提供的用户评论中，提取关于产品本身的洞察。执行指令:（1）过滤噪声: 在分析前，请务必忽略所有与产品功能、设计、质量、性能无关的评论，例如关于物流速度、快递员态度、包装好坏、客服响应、价格优惠、赠品等。（2）提炼优点: 总结出该商品最多 3 个被用户提及最多的核心优点。（3）提炼缺点: 总结出该商品最多 3 个被用户抱怨最多的核心缺点。（4）挖掘机会点: 识别出最多 2 个潜在的、未被满足的需求或明确的改进建议。定义: "未满足的需求"不是对现有功能的抱怨（那是缺点），而是用户渴望拥有但当前产品未提供的功能、特性或服务。例如："要是这个书包能有个专门放水杯的侧袋就好了"。

输出格式要求:以JSON格式返回结果，如果某个类别（如"优点"）下没有任何有效信息，请返回一个空数组 []，返回的每个点都应是精炼的短语。

输出JSON结构示例:
{
  "产品主要优点": ["优点1", "优点2", ...],
  "产品主要缺点": ["缺点1", "缺点2", ...],
  "潜在机会点": ["机会点1", "机会点2", ...]
}


原始评论如下：
${comments.join('\n')}。`;

  return `${systemPrompt}\n\n${userPrompt}`;
}

/**
 * Reduce 阶段：宏观洞察综合分析 Prompt
 * 将所有单品分析报告汇总为最终的市场洞察报告
 */
export const getReduceAnalysisPrompt = (summaries: Array<{productTitle?: string, analysis: any}>): string => {
  const systemPrompt = `你是一位顶级的、富有远见的市场战略分析师。你的任务是从大量单品分析报告中进行趋同和发散分析，识别出能够定义下一代产品的"蓝海机会"。`;

  const userPrompt = `我已为你提供了N份针对同一品类下不同商品的分析报告（JSON格式），每份报告都包含了该商品的优缺点和潜在机会点。综合以下N份单品分析报告，请忽略常见的、已被满足的需求，深度挖掘并总结出整个品类当前市场**最核心的5个未被满足的需求**或**新兴的机会点**。

分析方法论:（1）识别普遍痛点: 找出在多个不同商品的"缺点(cons)"和"机会点(opportunities)"中反复出现的主题。这些是行业级的普遍痛点，是重要的机会来源。（2）发现新兴信号: 关注那些虽然只在少数报告中出现，但用户情绪非常强烈（如"强烈希望"、"要是...就完美了"）或指向全新应用场景的"机会点"。这些是新兴趋势的信号。（3）忽略已解决的"优点": 在多份报告的"优点(pros)"中都普遍提及的功能，视为已被市场满足的"红海"特性，在最终报告中应忽略。（归纳与升维: 将识别出的多个具体痛点或需求，归纳、提炼成更高维度的机会点。例如，将"希望有放水杯的侧袋"和"想要一个单独的电脑隔层"归纳为"结构化收纳系统缺失"。）

报告格式要求:以最终的JSON报告格式返回；为每个机会点提供详细描述，说明其商业价值；为每个机会点附上 2-3条 来自原始报告、最能支撑你论点的原始评论佐证。

输出JSON结构示例:
{
  "market_opportunities": [
    {
      "opportunity_title": "机会点标题（高度概括）",
      "description": "对该机会点的详细描述，解释它为什么重要，能解决什么核心问题。",
      "evidence": [
        "直接相关的原始评论1",
        "直接相关的原始评论2"
      ]
    }
  ]
}

单品分析报告如下：
${summaries.map((s, i) => `
=== 报告 ${i + 1} ${s.productTitle ? `(${s.productTitle})` : ''} ===
${JSON.stringify(s.analysis, null, 2)}
`).join('\n\n')}。`;

  return `${systemPrompt}\n\n${userPrompt}`;
}