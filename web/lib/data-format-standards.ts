/**
 * 🔥 数据格式标准化规范
 * 
 * 解决系统中数据格式混乱问题，建立统一的数据格式标准：
 * 1. API通信格式 - 与AI交互的标准化数据格式
 * 2. 本地存储格式 - 任务数据的标准化存储结构
 * 3. 用户导出格式 - CSV/Excel/JSON的标准化导出格式
 * 
 * 作者: 毒舌界祖师爷的小贱狗 🐕
 * 创建时间: 2025-07-23
 */

import { Platform } from './browser-manager';

// ==================== API通信格式 ====================

/**
 * 🎯 AI输入数据标准格式
 * 确保发送给AI的数据为纯文本+结构化JSON的组合
 */
export interface AIInputFormat {
  /** 数据类型标识 */
  type: 'keyword_analysis' | 'product_discovery' | 'comment_analysis' | 'market_insights';
  
  /** 纯文本描述 - AI容易理解的自然语言 */
  textDescription: string;
  
  /** 结构化数据 - 标准JSON格式 */
  structuredData: {
    keywords?: string[];
    products?: StandardProductData[];
    comments?: StandardCommentData[];
    metadata: {
      platform: Platform;
      timestamp: string;
      dataSource: string;
      processingHints?: string[];
    };
  };
}

/**
 * 🎯 AI输出数据标准格式
 * 规范AI返回的数据结构
 */
export interface AIOutputFormat<T = any> {
  success: boolean;
  data: T;
  confidence: number; // 0-1之间的置信度
  reasoning?: string; // AI的推理过程
  metadata: {
    model: string;
    tokensUsed: number;
    processingTime: number;
    timestamp: string;
  };
  error?: string;
}

// ==================== 标准化数据结构 ====================

/**
 * 🎯 标准化商品数据结构
 */
export interface StandardProductData {
  id: string;
  platform: Platform;
  title: string;
  url: string;
  price?: {
    current: number;
    original?: number;
    currency: string;
  };
  sales?: {
    count: number;
    period: string; // '月销量', '总销量'
  };
  rating?: {
    score: number;
    maxScore: number;
    reviewCount: number;
  };
  images?: string[];
  category?: string;
  brand?: string;
  discoveredAt: string;
  discoveryKeyword: string;
}

/**
 * 🎯 标准化评论数据结构
 */
export interface StandardCommentData {
  id: string;
  platform: Platform;
  productId: string;
  productTitle: string;
  content: string;
  rating?: {
    score: number;
    maxScore: number;
  };
  author?: {
    name: string;
    level?: string;
    verified?: boolean;
  };
  engagement?: {
    likes: number;
    replies: number;
    helpful: number;
  };
  metadata: {
    date: string;
    isVerifiedPurchase?: boolean;
    hasImages?: boolean;
    hasVideo?: boolean;
  };
  scrapedAt: string;
}

/**
 * 🎯 标准化分析结果结构
 */
export interface StandardAnalysisResult {
  type: 'keyword_expansion' | 'keyword_clustering' | 'comment_analysis' | 'market_insights';
  input: {
    keywords?: string[];
    products?: string[];
    comments?: string[];
  };
  output: {
    expandedKeywords?: string[];
    clusteredKeywords?: { [cluster: string]: string[] };
    advantages?: string[];
    disadvantages?: string[];
    unmetNeeds?: string[];
    marketOpportunities?: string[];
    insights?: string[];
  };
  confidence: number;
  processingTime: number;
  timestamp: string;
}

// ==================== 本地存储格式 ====================

/**
 * 🎯 任务数据标准存储格式
 * 替换当前混乱的Task.reportData结构
 */
export interface TaskDataStorage {
  taskId: string;
  version: string; // 数据格式版本，便于后续升级
  
  // 基础信息
  basicInfo: {
    keywords: string[];
    platforms: Platform[];
    createdAt: string;
    updatedAt: string;
    status: string;
  };
  
  // 发现的商品数据
  discoveredProducts: StandardProductData[];
  
  // 抓取的评论数据
  scrapedComments: StandardCommentData[];
  
  // AI分析结果
  analysisResults: StandardAnalysisResult[];
  
  // 统计信息
  statistics: {
    totalProducts: number;
    totalComments: number;
    successRate: number;
    processingTime: number;
    dataQualityScore: number;
  };
  
  // 元数据
  metadata: {
    dataIntegrity: boolean;
    lastValidation: string;
    exportHistory: ExportRecord[];
  };
}

/**
 * 🎯 导出记录
 */
export interface ExportRecord {
  exportId: string;
  format: 'csv' | 'excel' | 'json';
  dataType: 'products' | 'comments' | 'insights' | 'all';
  filePath: string;
  exportedAt: string;
  fileSize: number;
}

// ==================== 用户导出格式 ====================

/**
 * 🎯 CSV/Excel导出的标准化格式
 */
export interface ExportFormat {
  // 商品数据表
  products: {
    platform: string;
    keyword: string;
    title: string;
    url: string;
    currentPrice: number;
    originalPrice: number;
    currency: string;
    salesCount: number;
    salesPeriod: string;
    rating: number;
    reviewCount: number;
    category: string;
    brand: string;
    discoveredAt: string;
  }[];
  
  // 评论数据表
  comments: {
    platform: string;
    productTitle: string;
    commentContent: string;
    rating: number;
    authorName: string;
    authorLevel: string;
    likes: number;
    replies: number;
    commentDate: string;
    isVerifiedPurchase: boolean;
    hasMedia: boolean;
    scrapedAt: string;
  }[];
  
  // 洞察分析表
  insights: {
    analysisType: string;
    keyword: string;
    advantages: string;
    disadvantages: string;
    unmetNeeds: string;
    marketOpportunity: string;
    confidence: number;
    analysisDate: string;
  }[];
  
  // 统计汇总表
  summary: {
    totalKeywords: number;
    totalProducts: number;
    totalComments: number;
    averageRating: number;
    topCategories: string;
    topBrands: string;
    dataQualityScore: number;
    reportGeneratedAt: string;
  }[];
}

// ==================== 数据转换工具类型 ====================

/**
 * 🎯 数据转换器接口
 */
export interface DataConverter<TInput, TOutput> {
  convert(input: TInput): TOutput;
  validate(data: TOutput): boolean;
  getSchema(): any;
}

/**
 * 🎯 数据验证结果
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  dataIntegrityScore: number;
}

// ==================== 常量定义 ====================

/**
 * 🎯 数据格式版本
 */
export const DATA_FORMAT_VERSION = '1.0.0';

/**
 * 🎯 支持的导出格式
 */
export const SUPPORTED_EXPORT_FORMATS = ['csv', 'excel', 'json'] as const;

/**
 * 🎯 支持的数据类型
 */
export const SUPPORTED_DATA_TYPES = ['products', 'comments', 'insights', 'all'] as const;

/**
 * 🎯 AI输入数据类型
 */
export const AI_INPUT_TYPES = [
  'keyword_analysis',
  'product_discovery', 
  'comment_analysis',
  'market_insights'
] as const;
