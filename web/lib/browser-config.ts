import * as path from 'path';
import * as os from 'os';

/**
 * 🎯 本地浏览器配置管理
 * 支持开发环境使用项目内置浏览器，生产环境使用系统浏览器
 */
export class BrowserConfig {
  /**
   * 获取Chromium可执行文件路径 - 支持开发和生产环境
   */
  static getChromiumExecutablePath(): string {
    const platform = os.platform();
    const isProduction = process.env.NODE_ENV === 'production' || process.resourcesPath;

    if (platform === 'darwin') {
      if (isProduction) {
        // 生产环境：从app.asar.unpacked或Resources目录
        const resourcesPath = process.resourcesPath || path.join(process.cwd(), 'resources');
        const localPath = path.join(resourcesPath, 'browsers/mac/Chromium.app/Contents/MacOS/Chromium');
        console.log(`[BrowserConfig] macOS 生产环境 Chromium路径: ${localPath}`);
        return localPath;
      } else {
        // 开发环境：项目根目录
        const localPath = path.join(process.cwd(), 'browsers/mac/Chromium.app/Contents/MacOS/Chromium');
        console.log(`[BrowserConfig] macOS 开发环境 Chromium路径: ${localPath}`);
        return localPath;
      }
    } else if (platform === 'win32') {
      if (isProduction) {
        // Windows生产环境
        const appPath = path.dirname(process.execPath);
        const localPath = path.join(appPath, 'browsers/win/chrome-win/chrome.exe');
        console.log(`[BrowserConfig] Windows 生产环境 Chromium路径: ${localPath}`);
        return localPath;
      } else {
        // Windows开发环境
        const localPath = path.join(process.cwd(), 'browsers/win/chrome-win/chrome.exe');
        console.log(`[BrowserConfig] Windows 开发环境 Chromium路径: ${localPath}`);
        return localPath;
      }
    } else {
      // Linux环境
      if (isProduction) {
        const appPath = path.dirname(process.execPath);
        const localPath = path.join(appPath, 'browsers/linux/chrome-linux/chrome');
        console.log(`[BrowserConfig] Linux 生产环境 Chromium路径: ${localPath}`);
        return localPath;
      } else {
        const localPath = path.join(process.cwd(), 'browsers/linux/chrome-linux/chrome');
        console.log(`[BrowserConfig] Linux 开发环境 Chromium路径: ${localPath}`);
        return localPath;
      }
    }
  }

  /**
   * 检查本地浏览器是否存在
   */
  static hasLocalBrowser(): boolean {
    const fs = require('fs');
    const executablePath = this.getChromiumExecutablePath();
    const exists = fs.existsSync(executablePath);
    console.log(`[BrowserConfig] 本地浏览器存在检查: ${exists ? '✅' : '❌'} (${executablePath})`);
    return exists;
  }

  /**
   * 智能浏览器路径检测 - 多重回退策略
   */
  static getAvailableBrowserPath(): { path?: string; source: string } {
    // 策略1: 检查本地预置浏览器
    if (this.hasLocalBrowser()) {
      return {
        path: this.getChromiumExecutablePath(),
        source: 'local-preset'
      };
    }

    // 策略2: 检查系统Playwright浏览器
    const fs = require('fs');
    const os = require('os');
    const platform = os.platform();

    let playwrightPath: string;
    if (platform === 'darwin') {
      playwrightPath = path.join(os.homedir(), 'Library/Caches/ms-playwright/chromium-1181/chrome-mac/Chromium.app/Contents/MacOS/Chromium');
    } else if (platform === 'win32') {
      playwrightPath = path.join(os.homedir(), 'AppData/Local/ms-playwright/chromium-1181/chrome-win/chrome.exe');
    } else {
      playwrightPath = path.join(os.homedir(), '.cache/ms-playwright/chromium-1181/chrome-linux/chrome');
    }

    if (fs.existsSync(playwrightPath)) {
      console.log(`[BrowserConfig] 找到系统Playwright浏览器: ${playwrightPath}`);
      return {
        path: playwrightPath,
        source: 'system-playwright'
      };
    }

    // 策略3: 使用Playwright默认路径（让Playwright自己处理）
    console.log(`[BrowserConfig] 使用Playwright默认浏览器路径`);
    return {
      source: 'playwright-default'
    };
  }

  /**
   * 获取浏览器启动配置 - 增强反爬虫策略 + 智能路径选择
   */
  static getBrowserLaunchOptions(headless: boolean = true) {
    const baseOptions = {
      headless, // 默认headless模式，避免启动时弹窗
      args: [
        // 基础安全设置
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        
        // 🚀 增强反爬虫检测绕过
        '--disable-blink-features=AutomationControlled',
        '--disable-features=VizDisplayCompositor',
        '--disable-web-security',
        '--disable-features=TranslateUI',
        '--disable-ipc-flooding-protection',
        '--disable-renderer-backgrounding',
        '--disable-backgrounding-occluded-windows',
        '--disable-client-side-phishing-detection',
        '--disable-sync',
        '--disable-default-apps',
        '--disable-extensions',
        '--disable-plugins',
        '--disable-preconnect',
        '--disable-hang-monitor',
        '--disable-prompt-on-repost',
        '--disable-domain-reliability',
        '--disable-component-extensions-with-background-pages',
        
        // 🎭 伪装为真实用户浏览器
        '--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        '--accept-lang=zh-CN,zh;q=0.9,en;q=0.8',
        
        // 🖥️ 真实屏幕尺寸
        '--window-size=1366,768',
        '--start-maximized',
        
        // 🔧 性能优化
        '--no-first-run',
        '--no-default-browser-check',
        '--disable-gpu-sandbox',
        '--disable-software-rasterizer'
      ]
    };

    // 使用智能路径检测
    const browserInfo = this.getAvailableBrowserPath();
    console.log(`[BrowserConfig] 浏览器来源: ${browserInfo.source}`);

    if (browserInfo.path) {
      return {
        ...baseOptions,
        executablePath: browserInfo.path
      };
    }

    // 使用Playwright默认路径
    return baseOptions;
  }
} 