/**
 * 🎯 TaskMonitor与后台进程状态映射表
 * 确保前端中文显示与后台进程状态完全一一对应
 */

// ====== 后台进度阶段到UI状态的映射表 ======
export const BACKEND_PHASE_TO_UI_STATUS = {
  // 初始化阶段
  'initialization': 'PENDING',
  'init': 'PENDING',
  'pending': 'PENDING',
  
  // AI拓词阶段
  'ai_expansion': 'EXPENTING',
  'ai_clustering': 'EXPENTING', 
  'keyword_processing': 'EXPENTING',
  'preprocessing': 'EXPENTING',
  'preprocessing_complete': 'WAITING_CONFIRMATION',
  
  // 链接发现阶段
  'link_discovery': 'DISCOVERING',
  'discovering': 'DISCOVERING',
  'scraping_start': 'DISCOVERING', // 开始爬虫时仍在发现阶段
  
  // 登录/验证码干预阶段
  'login_required': 'WAITING_USER_LOGIN',
  'waiting_login': 'WAITING_USER_LOGIN',
  'captcha_required': 'WAITING_CAPTCHA',
  'waiting_captcha': 'WAITING_CAPTCHA',
  
  // 数据采集阶段
  'scraping': 'SCRAPING',
  'scraping_progress': 'SCRAPING',
  'data_collection': 'SCRAPING',
  
  // AI分析阶段
  'analysis_start': 'ANALYZING',
  'analysis': 'ANALYZING',
  'analysis_progress': 'ANALYZING',
  'ai_analysis': 'ANALYZING',
  
  // 完成阶段
  'completed': 'COMPLETED',
  'execution_complete': 'COMPLETED',
  'quick-scan': 'COMPLETED',
  'finished': 'COMPLETED',
  
  // 失败阶段
  'failed': 'FAILED',
  'error': 'FAILED',
  'cancelled': 'CANCELLED',
} as const;

// ====== UI状态到中文显示的映射表 ======
export const UI_STATUS_TO_CHINESE = {
  'PENDING': '任务排队中',
  'EXPENTING': '智能拓词中', 
  'DISCOVERING': '链接发现中',
  'WAITING_CONFIRMATION': '等待用户确认',
  'WAITING_USER_LOGIN': '等待用户登录',
  'WAITING_CAPTCHA': '等待验证码',
  'SCRAPING': '评论采集中',
  'ANALYZING': 'AI洞察分析',
  'COMPLETED': '分析完成',
  'FAILED': '任务失败',
  'CANCELLED': '任务已取消',
  'PAUSED': '任务已暂停',
} as const;

// ====== 后台进度阶段到中文消息的映射表 ======
export const BACKEND_PHASE_TO_CHINESE_MESSAGE = {
  // 初始化阶段
  'initialization': '正在初始化任务...',
  'init': '任务初始化中...',
  'pending': '任务正在队列中等待处理',
  
  // AI拓词阶段
  'ai_expansion': 'AI正在基于核心词扩展相关搜索关键词',
  'ai_clustering': 'AI正在智能聚类关键词',
  'keyword_processing': '正在处理关键词...',
  'preprocessing': '正在进行AI预处理...',
  'preprocessing_complete': '关键词预处理完成，等待您的确认',
  
  // 链接发现阶段
  'link_discovery': '正在各大平台发现高价值商品/内容',
  'discovering': '正在发现相关链接...',
  'scraping_start': '开始数据采集，正在发现目标链接',
  
  // 登录/验证码干预阶段
  'login_required': '需要您登录平台账号以继续采集',
  'waiting_login': '等待您完成登录操作',
  'captcha_required': '需要您完成验证码验证',
  'waiting_captcha': '等待您完成验证码验证',
  
  // 数据采集阶段
  'scraping': '正在采集相关页面的用户评论数据',
  'scraping_progress': '数据采集进行中...',
  'data_collection': '正在收集用户评论数据',
  
  // AI分析阶段
  'analysis_start': 'AI正在深度分析采集到的数据',
  'analysis': 'AI分析进行中...',
  'analysis_progress': 'AI正在生成洞察报告',
  'ai_analysis': 'AI正在深度分析用户需求',
  
  // 完成阶段
  'completed': '任务执行完成，报告已生成',
  'execution_complete': '深度扫描完成，报告已生成',
  'quick-scan': '快速扫描完成',
  'finished': '所有任务已完成',
  
  // 失败阶段
  'failed': '任务执行失败',
  'error': '任务执行过程中发生错误',
  'cancelled': '任务已被用户取消',
} as const;

// ====== 扫描模式到中文的映射表 ======
export const SCAN_MODE_TO_CHINESE = {
  'quick': '快速扫描',
  'deep': '深度扫描',
  'ai+mcp': 'AI+MCP智能扫描',
} as const;

// ====== 工具函数 ======

/**
 * 将后台进度阶段转换为UI状态
 */
export function mapBackendPhaseToUIStatus(phase: string): string {
  return BACKEND_PHASE_TO_UI_STATUS[phase as keyof typeof BACKEND_PHASE_TO_UI_STATUS] || phase;
}

/**
 * 将UI状态转换为中文显示
 */
export function mapUIStatusToChinese(status: string): string {
  return UI_STATUS_TO_CHINESE[status as keyof typeof UI_STATUS_TO_CHINESE] || status;
}

/**
 * 将后台进度阶段转换为中文消息
 */
export function mapBackendPhaseToChinese(phase: string): string {
  return BACKEND_PHASE_TO_CHINESE_MESSAGE[phase as keyof typeof BACKEND_PHASE_TO_CHINESE_MESSAGE] || phase;
}

/**
 * 将扫描模式转换为中文
 */
export function mapScanModeToChinese(mode: string): string {
  return SCAN_MODE_TO_CHINESE[mode as keyof typeof SCAN_MODE_TO_CHINESE] || mode;
}

/**
 * 综合转换函数：从后台进度直接转换为中文显示
 */
export function convertBackendProgressToChinese(phase: string, message?: string): {
  status: string;
  chineseStatus: string;
  chineseMessage: string;
} {
  const uiStatus = mapBackendPhaseToUIStatus(phase);
  const chineseStatus = mapUIStatusToChinese(uiStatus);
  const chineseMessage = message || mapBackendPhaseToChinese(phase);
  
  return {
    status: uiStatus,
    chineseStatus,
    chineseMessage
  };
}

// ====== 类型定义 ======
export type BackendPhase = keyof typeof BACKEND_PHASE_TO_UI_STATUS;
export type UIStatus = keyof typeof UI_STATUS_TO_CHINESE;
export type ScanMode = keyof typeof SCAN_MODE_TO_CHINESE;

/**
 * 标准化的任务进度接口
 */
export interface StandardTaskProgress {
  phase: string;
  status: UIStatus;
  chineseStatus: string;
  chineseMessage: string;
  current: number;
  total: number;
  mode?: ScanMode;
  timestamp: string;
}

/**
 * 将原始进度转换为标准化进度
 */
export function standardizeTaskProgress(rawProgress: {
  phase: string;
  message?: string;
  current: number;
  total: number;
  mode?: string;
}): StandardTaskProgress {
  const converted = convertBackendProgressToChinese(rawProgress.phase, rawProgress.message);
  
  return {
    phase: rawProgress.phase,
    status: converted.status as UIStatus,
    chineseStatus: converted.chineseStatus,
    chineseMessage: converted.chineseMessage,
    current: rawProgress.current,
    total: rawProgress.total,
    mode: rawProgress.mode as ScanMode,
    timestamp: new Date().toISOString()
  };
}
