/**
 * 🔥 SimpleTaskExecutor - 简化的任务执行器
 * 
 * 替代复杂的IntelligentTaskOrchestrator，使用传统爬虫+AI分析架构
 * 
 * 核心设计原则：
 * 1. 保持与IntelligentTaskOrchestrator相同的接口，确保前端零修改
 * 2. 内部使用简单的if-else逻辑替代AI决策
 * 3. 直接调用BrowserManager和PlatformScraper
 * 4. 保留现有的进度回调机制
 * 5. 保持所有用户功能和体验不变
 */

import { BrowserManager, Platform } from './browser-manager';
import { PlatformScraper, LoginRequiredError } from './platform-scraper';
import { SimpleAIService } from './simple-ai-service';
import * as localStorageModule from './local-storage';

/**
 * 任务选项接口 - 与IntelligentTaskOrchestrator兼容
 */
export interface TaskOptions {
  maxResults?: number;
  platforms?: Platform[];
  analysisDepth?: 'basic' | 'detailed' | 'comprehensive';
  includeComments?: boolean;
  skipAIPreprocessing?: boolean; // 快速扫描模式标志
  customInstructions?: string;
  useAIDecision?: boolean;
}

/**
 * 任务进度接口 - 与现有系统兼容
 */
export interface TaskProgress {
  phase: string;
  message: string;
  current: number;
  total: number;
  mode?: 'quick' | 'deep' | 'ai+mcp';
  metadata?: any;
}

/**
 * 任务结果接口 - 与现有系统兼容
 */
export interface TaskResult {
  success: boolean;
  data?: {
    discoveredLinks: string[];
    scrapedComments: Record<string, string[]>;
    analysis?: any;
    // 深度扫描特殊字段
    needsUserConfirmation?: boolean;
    processedKeywords?: Array<{theme: string, keywords: string[]}>;
  };
  metadata?: {
    timestamp: string;
    duration: number;
    tokensUsed?: number;
    phase?: string;
  };
  error?: string;
}

/**
 * 进度回调函数类型
 */
export type ProgressCallback = (progress: TaskProgress) => void;

/**
 * SimpleTaskExecutor - 简化的任务执行器
 * 
 * 这个类替代复杂的IntelligentTaskOrchestrator，提供简化但功能完整的任务执行能力
 */
export class SimpleTaskExecutor {
  private browserManager: BrowserManager;
  private aiService: SimpleAIService;
  private localStorage: typeof localStorageModule;
  private progressCallback?: ProgressCallback;
  private isInitialized: boolean = false;

  constructor(
    browserManager: BrowserManager,
    aiService: SimpleAIService,
    localStorage: typeof localStorageModule
  ) {
    this.browserManager = browserManager;
    this.aiService = aiService;
    this.localStorage = localStorage;
  }

  /**
   * 初始化任务执行器
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('[SimpleTaskExecutor] 🚀 Initializing simple task executor...');
      
      // 确保AI服务已初始化
      await this.aiService.initialize();
      
      this.isInitialized = true;
      console.log('[SimpleTaskExecutor] ✅ Simple task executor initialized successfully');
    } catch (error) {
      console.error('[SimpleTaskExecutor] ❌ Failed to initialize:', error);
      throw new Error(`SimpleTaskExecutor initialization failed: ${error}`);
    }
  }

  /**
   * 设置进度回调函数 - 与IntelligentTaskOrchestrator兼容
   */
  public setProgressCallback(callback: ProgressCallback): void {
    this.progressCallback = callback;
  }

  /**
   * 报告任务进度 - 内部方法
   */
  private reportProgress(phase: string, message: string, current: number, total: number, mode?: string): void {
    if (this.progressCallback) {
      this.progressCallback({
        phase,
        message,
        current,
        total,
        mode: mode as any,
        metadata: {
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  /**
   * 主要任务执行方法 - 与IntelligentTaskOrchestrator兼容
   */
  public async executeTask(
    keywords: string[],
    options: TaskOptions = {}
  ): Promise<TaskResult> {
    if (!this.isInitialized) {
      throw new Error('SimpleTaskExecutor not initialized. Call initialize() first.');
    }

    const startTime = Date.now();
    console.log(`[SimpleTaskExecutor] 🎯 Starting task execution with ${keywords.length} keywords`);

    // 设置默认选项
    const taskOptions = {
      maxResults: 50,
      platforms: ['taobao', 'xiaohongshu'] as Platform[],
      analysisDepth: 'basic' as const,
      includeComments: true,
      skipAIPreprocessing: false,
      ...options
    };

    try {
      // 根据skipAIPreprocessing标志选择执行模式
      if (taskOptions.skipAIPreprocessing) {
        console.log('[SimpleTaskExecutor] 🚀 Executing quick scan mode (skip AI preprocessing)');
        return await this.executeQuickScan(keywords, taskOptions);
      } else {
        console.log('[SimpleTaskExecutor] 🧠 Executing deep scan mode (with AI preprocessing)');
        return await this.executeDeepScan(keywords, taskOptions);
      }
    } catch (error) {
      console.error('[SimpleTaskExecutor] ❌ Task execution failed:', error);
      
      const duration = Date.now() - startTime;
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        metadata: {
          timestamp: new Date().toISOString(),
          duration
        }
      };
    }
  }

  /**
   * 快速扫描模式 - 跳过AI预处理，直接执行爬虫
   */
  public async executeQuickScan(
    keywords: string[],
    options: TaskOptions = {}
  ): Promise<TaskResult> {
    const startTime = Date.now();
    
    this.reportProgress('initialization', '初始化快速扫描任务', 0, keywords.length, 'quick');

    try {
      // 直接使用原始关键词进行爬虫，不进行AI预处理
      const finalKeywords = keywords;
      
      this.reportProgress('scraping_start', '开始数据采集', 0, finalKeywords.length, 'quick');

      // 执行爬虫任务
      const scrapingResult = await this.executeScraping(finalKeywords, options);

      this.reportProgress('analysis_start', '开始AI分析', finalKeywords.length * 0.8, finalKeywords.length, 'quick');

      // 执行AI分析（如果需要）
      let analysis = null;
      if (options.includeComments && scrapingResult.scrapedComments) {
        analysis = await this.executeAnalysis(scrapingResult.scrapedComments);
      }

      this.reportProgress('completed', '快速扫描完成', finalKeywords.length, finalKeywords.length, 'quick');

      const duration = Date.now() - startTime;
      return {
        success: true,
        data: {
          discoveredLinks: scrapingResult.discoveredLinks,
          scrapedComments: scrapingResult.scrapedComments,
          analysis: {
            // 转换为InsightReport兼容格式
            demandKeywords: this.extractDemandKeywords(analysis),
            insights: this.formatInsights(analysis),
            summary: analysis?.summary || '快速扫描完成',
            // 保留原始分析数据
            rawAnalysis: analysis
          }
        },
        metadata: {
          timestamp: new Date().toISOString(),
          duration,
          tokensUsed: analysis?.tokensUsed || 0,
          phase: 'quick-scan'
        }
      };

    } catch (error) {
      console.error('[SimpleTaskExecutor] ❌ Quick scan failed:', error);
      throw error;
    }
  }

  /**
   * 深度扫描模式 - 包含AI预处理和用户确认
   *
   * 注意：这个方法分为两个阶段：
   * 1. 预处理阶段：执行关键词扩展/聚类，然后暂停等待用户确认
   * 2. 执行阶段：用户确认后，使用确认的关键词执行爬虫和分析
   */
  public async executeDeepScan(
    keywords: string[],
    options: TaskOptions = {}
  ): Promise<TaskResult> {
    const startTime = Date.now();

    this.reportProgress('initialization', '初始化深度扫描任务', 0, keywords.length, 'deep');

    try {
      // 第一步：AI关键词预处理
      let processedKeywords: Array<{theme: string, keywords: string[]}>;

      if (keywords.length < 5) {
        // 关键词数量少于5个，进行扩展
        this.reportProgress('ai_expansion', '正在扩展关键词...', 0, keywords.length, 'deep');
        const expandedKeywords = await this.expandKeywords(keywords);

        // 将扩展的关键词包装成聚类格式
        processedKeywords = [{
          theme: '扩展关键词',
          keywords: expandedKeywords
        }];
      } else {
        // 关键词数量大于等于5个，进行聚类
        this.reportProgress('ai_clustering', '正在聚类关键词...', 0, keywords.length, 'deep');
        const clusters = await this.clusterKeywords(keywords);

        // 转换为processedKeywords格式（移除selected字段）
        processedKeywords = clusters.map(cluster => ({
          theme: cluster.theme,
          keywords: cluster.keywords
        }));
      }

      // 第二步：返回特殊结果，指示需要用户确认
      // main.ts会检测到这个特殊状态，将任务设置为WAITING_CONFIRMATION
      return {
        success: true,
        data: {
          discoveredLinks: [],
          scrapedComments: {},
          analysis: null,
          // 特殊标记：需要用户确认
          needsUserConfirmation: true,
          processedKeywords: processedKeywords
        },
        metadata: {
          timestamp: new Date().toISOString(),
          duration: Date.now() - startTime,
          phase: 'preprocessing_complete'
        }
      };

    } catch (error) {
      console.error('[SimpleTaskExecutor] ❌ Deep scan preprocessing failed:', error);
      throw error;
    }
  }

  /**
   * 深度扫描执行阶段 - 用户确认关键词后执行
   * 这个方法在用户确认关键词后被调用
   */
  public async executeDeepScanWithConfirmedKeywords(
    confirmedKeywords: string[],
    options: TaskOptions = {}
  ): Promise<TaskResult> {
    const startTime = Date.now();

    this.reportProgress('scraping_start', '开始数据采集', 0, confirmedKeywords.length, 'deep');

    try {
      // 执行爬虫任务
      const scrapingResult = await this.executeScraping(confirmedKeywords, options);

      this.reportProgress('analysis_start', '开始深度AI分析', confirmedKeywords.length * 0.8, confirmedKeywords.length, 'deep');

      // 执行深度AI分析
      let analysis = null;
      if (options.includeComments && scrapingResult.scrapedComments) {
        analysis = await this.executeAnalysis(scrapingResult.scrapedComments);
      }

      this.reportProgress('completed', '深度扫描完成', confirmedKeywords.length, confirmedKeywords.length, 'deep');

      const duration = Date.now() - startTime;
      return {
        success: true,
        data: {
          discoveredLinks: scrapingResult.discoveredLinks,
          scrapedComments: scrapingResult.scrapedComments,
          analysis: {
            // 转换为InsightReport兼容格式
            demandKeywords: this.extractDemandKeywords(analysis),
            insights: this.formatInsights(analysis),
            summary: analysis?.summary || '深度扫描完成',
            // 保留原始分析数据
            rawAnalysis: analysis
          }
        },
        metadata: {
          timestamp: new Date().toISOString(),
          duration,
          tokensUsed: analysis?.tokensUsed || 0,
          phase: 'execution_complete'
        }
      };

    } catch (error) {
      console.error('[SimpleTaskExecutor] ❌ Deep scan execution failed:', error);
      throw error;
    }
  }

  /**
   * 关键词扩展 - 调用SimpleAIService
   */
  private async expandKeywords(keywords: string[]): Promise<string[]> {
    try {
      const allExpandedKeywords: string[] = [];
      
      for (const keyword of keywords) {
        const expanded = await this.aiService.expandKeywords(keyword);
        allExpandedKeywords.push(...expanded);
      }
      
      // 去重并返回
      return [...new Set(allExpandedKeywords)];
    } catch (error) {
      console.error('[SimpleTaskExecutor] ❌ Keyword expansion failed:', error);
      // 失败时返回原始关键词
      return keywords;
    }
  }

  /**
   * 关键词聚类 - 调用SimpleAIService
   */
  private async clusterKeywords(keywords: string[]): Promise<Array<{theme: string, keywords: string[], selected: boolean}>> {
    try {
      return await this.aiService.clusterKeywords(keywords);
    } catch (error) {
      console.error('[SimpleTaskExecutor] ❌ Keyword clustering failed:', error);
      // 失败时返回简单分组
      return [{
        theme: '默认分组',
        keywords: keywords.slice(0, 10), // 取前10个关键词
        selected: true
      }];
    }
  }

  /**
   * 从聚类结果中提取关键词
   */
  private extractKeywordsFromClusters(clusters: Array<{theme: string, keywords: string[], selected: boolean}>): string[] {
    const selectedKeywords: string[] = [];

    for (const cluster of clusters) {
      if (cluster.selected) {
        selectedKeywords.push(...cluster.keywords);
      }
    }

    return selectedKeywords;
  }

  /**
   * 从分析结果中提取需求关键词 - 兼容InsightReport格式
   */
  private extractDemandKeywords(analysis: any): Array<{word: string, frequency: number}> {
    if (!analysis) return [];

    // 如果已经是正确格式
    if (analysis.demandKeywords && Array.isArray(analysis.demandKeywords)) {
      return analysis.demandKeywords;
    }

    // 从市场洞察中提取关键词
    if (analysis.marketInsights && Array.isArray(analysis.marketInsights)) {
      const keywords: Array<{word: string, frequency: number}> = [];
      analysis.marketInsights.forEach((insight: any, index: number) => {
        if (insight.title) {
          keywords.push({
            word: insight.title,
            frequency: 10 - index // 简单的频率计算
          });
        }
      });
      return keywords.slice(0, 10); // 最多返回10个
    }

    return [];
  }

  /**
   * 格式化洞察数据 - 兼容InsightReport格式
   */
  private formatInsights(analysis: any): Array<{id: string, title: string, description: string, evidence: string[]}> {
    if (!analysis) return [];

    // 如果已经是正确格式
    if (analysis.insights && Array.isArray(analysis.insights)) {
      return analysis.insights;
    }

    // 从市场洞察中转换
    if (analysis.marketInsights && Array.isArray(analysis.marketInsights)) {
      return analysis.marketInsights.map((insight: any, index: number) => ({
        id: `insight-${index + 1}`,
        title: insight.title || `洞察 ${index + 1}`,
        description: insight.description || insight.content || '',
        evidence: insight.evidence || []
      }));
    }

    return [];
  }

  /**
   * 🚀 重构：按照5步流程执行爬虫任务
   * 1. 链接采集：对关键词先按照淘宝销量排序、小红书最热排序进行链接抓取和存储
   * 2. 链接去重：对存储的链接进行去重
   * 3. 评论采集：对去重后的链接进行每条链接的评论采集和存储
   * 4. 单链接评论 AI 分析（在executeAnalysis中处理）
   * 5. 综合洞察生成（在executeAnalysis中处理）
   */
  private async executeScraping(
    keywords: string[],
    options: TaskOptions
  ): Promise<{discoveredLinks: string[], scrapedComments: Record<string, string[]>}> {
    const platforms = options.platforms || ['taobao', 'xiaohongshu'];
    const maxResults = options.maxResults || 50;
    const includeComments = options.includeComments !== false;

    console.log(`[SimpleTaskExecutor] 🕷️ Starting 5-step scraping process for ${keywords.length} keywords on ${platforms.length} platforms`);

    // 🚀 步骤1&2：链接采集和去重的临时存储
    const allDiscoveredLinks: string[] = [];
    const scrapedComments: Record<string, string[]> = {};

    try {
      // 🎯 为任务启动浏览器（强制visible模式，确保用户可以看到爬虫操作）
      if (!this.browserManager.isRunning()) {
        console.log('[SimpleTaskExecutor] 🚀 为任务启动浏览器管理器（visible模式）...');
        await this.browserManager.launchForTask();
      } else {
        // 如果浏览器已经在运行，但可能是headless模式，需要重启为visible模式
        console.log('[SimpleTaskExecutor] 🔄 检测到浏览器已运行，重启为任务模式（visible）...');
        await this.browserManager.launchForTask();
      }

      // 为每个平台创建PlatformScraper实例
      const scrapers: Record<Platform, PlatformScraper> = {} as Record<Platform, PlatformScraper>;
      for (const platform of platforms) {
        const page = await this.browserManager.getPage(platform);
        scrapers[platform] = new PlatformScraper(page, platform);
      }

      // 🚀 步骤1：链接采集 - 分别对每个关键词在每个平台进行链接抓取
      console.log(`[SimpleTaskExecutor] 📋 步骤1：开始链接采集`);
      const totalLinkTasks = keywords.length * platforms.length;
      let linkTasksCompleted = 0;

      for (const keyword of keywords) {
        for (const platform of platforms) {
          try {
            this.reportProgress(
              'discovering',
              `步骤1: 在${platform}平台搜索"${keyword}"`,
              linkTasksCompleted,
              totalLinkTasks
            );

            // 发现商品链接（已按销量/最热排序）
            const links = await scrapers[platform].discoverLinks([keyword]);
            allDiscoveredLinks.push(...links);

            console.log(`[SimpleTaskExecutor] 🔍 关键词"${keyword}"在${platform}平台找到 ${links.length} 个链接`);
            linkTasksCompleted++;

          } catch (error) {
            console.error(`[SimpleTaskExecutor] ❌ 链接采集失败 "${keyword}" on ${platform}:`, error);

            // 处理登录错误
            if (error instanceof LoginRequiredError) {
              await this.handleLoginRequired(platform);
            }

            linkTasksCompleted++;
          }
        }
      }

      // 🚀 步骤2：链接去重
      console.log(`[SimpleTaskExecutor] 🔄 步骤2：开始链接去重`);
      this.reportProgress('deduplicating', '步骤2: 正在去重链接', totalLinkTasks * 0.8, totalLinkTasks);

      const uniqueLinks = [...new Set(allDiscoveredLinks)];
      console.log(`[SimpleTaskExecutor] ✅ 去重完成：${allDiscoveredLinks.length} -> ${uniqueLinks.length} 个唯一链接`);

      // 限制最大链接数量
      const finalLinks = uniqueLinks.slice(0, maxResults);
      console.log(`[SimpleTaskExecutor] 📊 最终处理 ${finalLinks.length} 个链接（限制：${maxResults}）`);

      // 🚀 步骤3：评论采集（如果需要）
      if (includeComments && finalLinks.length > 0) {
        console.log(`[SimpleTaskExecutor] 💬 步骤3：开始评论采集`);

        for (let i = 0; i < finalLinks.length; i++) {
          const link = finalLinks[i];

          try {
            this.reportProgress(
              'scraping',
              `步骤3: 采集评论 (${i + 1}/${finalLinks.length})`,
              i,
              finalLinks.length
            );

            // 确定链接所属平台
            const platform = this.determinePlatformFromLink(link);
            if (!platform || !scrapers[platform]) {
              console.warn(`[SimpleTaskExecutor] ⚠️ 无法确定链接平台: ${link}`);
              continue;
            }

            // 导航到产品页面并抓取评论
            const page = await this.browserManager.getPage(platform);
            await page.goto(link, { waitUntil: 'domcontentloaded', timeout: 30000 });
            const comments = await scrapers[platform].scrapeComments();

            if (comments.length > 0) {
              scrapedComments[link] = comments;
              console.log(`[SimpleTaskExecutor] ✅ 链接 ${i + 1}/${finalLinks.length} 采集到 ${comments.length} 条评论`);
            } else {
              console.log(`[SimpleTaskExecutor] ⚠️ 链接 ${i + 1}/${finalLinks.length} 未找到评论`);
            }

          } catch (commentError) {
            console.warn(`[SimpleTaskExecutor] ❌ 评论采集失败 ${link}:`, commentError);

            // 处理登录错误
            if (commentError instanceof LoginRequiredError) {
              await this.handleLoginRequired(this.determinePlatformFromLink(link) as Platform);
              // 登录后重试一次
              try {
                const platform = this.determinePlatformFromLink(link) as Platform;
                const page = await this.browserManager.getPage(platform);
                await page.goto(link, { waitUntil: 'domcontentloaded', timeout: 30000 });
                const comments = await scrapers[platform].scrapeComments();
                if (comments.length > 0) {
                  scrapedComments[link] = comments;
                }
              } catch (retryError) {
                console.warn(`[SimpleTaskExecutor] ❌ 重试失败 ${link}:`, retryError);
              }
            }
          }
        }
      }

      console.log(`[SimpleTaskExecutor] ✅ 5步流程完成: ${finalLinks.length} 个链接, ${Object.keys(scrapedComments).length} 个产品有评论`);

      return {
        discoveredLinks: finalLinks,
        scrapedComments
      };

    } catch (error) {
      console.error('[SimpleTaskExecutor] ❌ 5步爬虫流程失败:', error);
      throw error;
    }
  }

  /**
   * 🔧 辅助方法：从链接确定所属平台
   */
  private determinePlatformFromLink(link: string): Platform | null {
    if (link.includes('taobao.com') || link.includes('tmall.com')) {
      return 'taobao';
    } else if (link.includes('xiaohongshu.com')) {
      return 'xiaohongshu';
    }
    return null;
  }

  /**
   * 处理登录要求 - 保持现有的登录处理逻辑
   */
  private async handleLoginRequired(platform: Platform): Promise<void> {
    console.log(`[SimpleTaskExecutor] 🔐 Login required for ${platform}, triggering login flow...`);

    this.reportProgress(
      'login_required',
      `需要${platform}平台登录`,
      0,
      1
    );

    try {
      // 触发登录流程（保持现有实现）
      // 启动浏览器进行登录
      if (!this.browserManager.isRunning()) {
        await this.browserManager.launch();
      }

      // 等待登录完成
      await this.waitForLoginCompletion(platform);

      console.log(`[SimpleTaskExecutor] ✅ Login completed for ${platform}`);

    } catch (error) {
      console.error(`[SimpleTaskExecutor] ❌ Login failed for ${platform}:`, error);
      throw error;
    }
  }

  /**
   * 等待登录完成
   */
  private async waitForLoginCompletion(platform: Platform): Promise<void> {
    const maxWaitTime = 300000; // 5分钟超时
    const checkInterval = 2000; // 每2秒检查一次
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      try {
        // 简化的登录状态检查 - 检查context是否可用
        const isContextReady = this.browserManager.isContextReady(platform);

        if (isContextReady) {
          console.log(`[SimpleTaskExecutor] ✅ Login confirmed for ${platform}`);
          return;
        }

        // 等待一段时间后再次检查
        await new Promise(resolve => setTimeout(resolve, checkInterval));

      } catch (error) {
        console.warn(`[SimpleTaskExecutor] ⚠️ Error checking login status for ${platform}:`, error);
        await new Promise(resolve => setTimeout(resolve, checkInterval));
      }
    }

    throw new Error(`Login timeout for ${platform} after ${maxWaitTime / 1000} seconds`);
  }

  /**
   * 执行AI分析 - 调用SimpleAIService
   */
  private async executeAnalysis(
    scrapedComments: Record<string, string[]>
  ): Promise<any> {
    try {
      console.log(`[SimpleTaskExecutor] 🧠 Starting AI analysis for ${Object.keys(scrapedComments).length} products`);

      const analysisSummaries: any[] = [];
      let totalTokensUsed = 0;

      // 第一阶段：Map - 对每个产品的评论进行分析
      for (const [productLink, comments] of Object.entries(scrapedComments)) {
        if (comments.length === 0) continue;

        try {
          // 从链接中提取产品标题（简化处理）
          const productTitle = this.extractProductTitleFromLink(productLink);

          console.log(`[SimpleTaskExecutor] 📊 Analyzing comments for product: ${productTitle}`);

          // 调用SimpleAIService进行评论分析
          const analysis = await this.aiService.analyzeComments(comments, productTitle);

          analysisSummaries.push({
            productLink,
            productTitle,
            analysis,
            commentCount: comments.length
          });

          console.log(`[SimpleTaskExecutor] ✅ Analysis completed for ${productTitle}`);

        } catch (error) {
          console.error(`[SimpleTaskExecutor] ❌ Failed to analyze comments for ${productLink}:`, error);
          // 继续处理其他产品，不因单个产品失败而中断
        }
      }

      // 第二阶段：Reduce - 生成市场洞察报告
      if (analysisSummaries.length > 0) {
        console.log(`[SimpleTaskExecutor] 🎯 Generating market insights from ${analysisSummaries.length} product analyses`);

        try {
          // 提取所有分析结果
          const commentAnalysisResults = analysisSummaries.map(summary => summary.analysis);

          // 调用SimpleAIService生成市场洞察
          const marketInsights = await this.aiService.generateMarketInsights(commentAnalysisResults);

          console.log(`[SimpleTaskExecutor] ✅ Market insights generated successfully`);

          // 返回完整的分析结果
          return {
            productAnalyses: analysisSummaries,
            marketInsights,
            summary: {
              totalProducts: analysisSummaries.length,
              totalComments: analysisSummaries.reduce((sum, s) => sum + s.commentCount, 0),
              tokensUsed: totalTokensUsed
            }
          };

        } catch (error) {
          console.error(`[SimpleTaskExecutor] ❌ Failed to generate market insights:`, error);

          // 即使市场洞察生成失败，也返回产品分析结果
          return {
            productAnalyses: analysisSummaries,
            marketInsights: null,
            summary: {
              totalProducts: analysisSummaries.length,
              totalComments: analysisSummaries.reduce((sum, s) => sum + s.commentCount, 0),
              tokensUsed: totalTokensUsed
            },
            error: 'Market insights generation failed'
          };
        }
      } else {
        console.log(`[SimpleTaskExecutor] ⚠️ No valid product analyses available for market insights`);
        return {
          productAnalyses: [],
          marketInsights: null,
          summary: {
            totalProducts: 0,
            totalComments: 0,
            tokensUsed: 0
          },
          error: 'No valid comments found for analysis'
        };
      }

    } catch (error) {
      console.error(`[SimpleTaskExecutor] ❌ AI analysis failed:`, error);
      throw error;
    }
  }

  /**
   * 从产品链接中提取产品标题 - 辅助方法
   */
  private extractProductTitleFromLink(productLink: string): string {
    try {
      // 简化的标题提取逻辑
      // 实际项目中可能需要更复杂的解析
      const url = new URL(productLink);

      if (url.hostname.includes('taobao')) {
        return `淘宝商品 - ${url.pathname.split('/').pop()}`;
      } else if (url.hostname.includes('xiaohongshu')) {
        return `小红书商品 - ${url.pathname.split('/').pop()}`;
      } else {
        return `商品 - ${url.pathname.split('/').pop()}`;
      }
    } catch (error) {
      return `商品 - ${productLink.substring(0, 50)}...`;
    }
  }

  /**
   * 清理资源
   */
  public async cleanup(): Promise<void> {
    console.log('[SimpleTaskExecutor] 🧹 Cleaning up resources...');
    // 清理逻辑将在后续实现
  }
}
