/**
 * V3.0 AI+MCP 爬虫系统 - 配置管理
 *
 * 🔥 重构说明：
 * 1. 集成统一API配置管理器，消除硬编码
 * 2. 所有API配置通过函数传参，不再直接访问配置对象
 * 3. 保持向后兼容性，逐步迁移到新的配置系统
 */

// import { AIAgentConfig } from './ai-agent'; // V3.0 AI Agent已移除
import { APIConfigManager, AIProviderConfig as UnifiedAIProviderConfig } from '../api-config-manager';

/**
 * AI代理配置接口（V3.0兼容性保留）
 */
export interface AIAgentConfig {
  apiKey: string;
  baseURL: string;
  model: string;
  maxTokens: number;
  temperature: number;
  maxRetries: number;
  retryDelay: number;
}
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

/**
 * MCP服务器配置接口
 */
export interface MCPServerConfig {
  name: string;
  version: string;
  capabilities: {
    tools: boolean;
    resources: boolean;
    prompts: boolean;
  };
  transport: {
    type: 'stdio' | 'sse' | 'websocket';
    options?: any;
  };
}

/**
 * AI+MCP系统完整配置接口
 */
export interface AIMCPConfig {
  // AI代理配置
  agent: AIAgentConfig;
  
  // MCP服务器配置
  mcpServer: MCPServerConfig;
  
  // 工具执行配置
  tools: {
    // 工具执行超时时间（毫秒）
    executionTimeout: number;
    // 工具重试次数
    maxRetries: number;
    // 重试间隔（毫秒）
    retryDelay: number;
    // 并发工具调用限制
    maxConcurrentCalls: number;
  };
  
  // 性能监控配置
  monitoring: {
    // 是否启用性能监控
    enabled: boolean;
    // 监控间隔（毫秒）
    interval: number;
    // 性能指标阈值
    thresholds: {
      responseTime: number; // 毫秒
      memoryUsage: number;  // MB
      errorRate: number;    // 百分比
    };
  };
  
  // 日志配置
  logging: {
    // 日志级别
    level: 'debug' | 'info' | 'warn' | 'error';
    // 是否启用AI决策日志
    enableAIDecisionLogs: boolean;
    // 是否启用工具调用日志
    enableToolCallLogs: boolean;
    // 日志文件路径
    logFilePath?: string;
  };
}

/**
 * AI模型提供商配置接口
 */
export interface AIProviderConfig {
  apiKey: string;
  baseURL: string;
  model: string;
  maxTokens?: number;
  temperature?: number;
  enabled?: boolean;
}

/**
 * 多模型配置接口
 */
export interface MultiModelConfig {
  // Playwright MCP专用模型（用于页面操作和决策）
  playwrightMCP: AIProviderConfig;
  // 数据分析专用模型（用于数据处理和洞察生成）
  dataAnalysis: AIProviderConfig;
  // 默认模型（通用任务）
  default: AIProviderConfig;
}

/**
 * 默认AI+MCP配置
 */
export const DEFAULT_AI_MCP_CONFIG: AIMCPConfig = {
  agent: {
    // 不再硬编码，完全依赖外部配置
    apiKey: '', // 将通过配置文件或环境变量提供
    baseURL: '', // 将通过配置文件或环境变量提供
    model: '', // 将通过配置文件或环境变量提供

    // AI行为配置
    maxTokens: 4000,
    temperature: 0.3, // 较低的温度确保更稳定的决策

    // 任务配置
    maxRetries: 3,
    retryDelay: 2000,
  },
  
  mcpServer: {
    name: 'demand-insight-scraping-server',
    version: '3.0.0',
    capabilities: {
      tools: true,
      resources: false,
      prompts: false,
    },
    transport: {
      type: 'stdio',
    },
  },
  
  tools: {
    executionTimeout: 60000,  // 60秒
    maxRetries: 3,
    retryDelay: 1000,
    maxConcurrentCalls: 5,
  },
  
  monitoring: {
    enabled: true,
    interval: 10000, // 10秒
    thresholds: {
      responseTime: 30000, // 30秒
      memoryUsage: 1024,   // 1GB
      errorRate: 10,       // 10%
    },
  },
  
  logging: {
    level: 'info',
    enableAIDecisionLogs: true,
    enableToolCallLogs: true,
    logFilePath: './logs/ai-mcp.log',
  },
};

/**
 * AI+MCP配置管理器
 */
export class AIMCPConfigManager {
  private static instance: AIMCPConfigManager;
  private config: AIMCPConfig;
  private multiModelConfig: MultiModelConfig | null = null;

  private constructor() {
    this.config = { ...DEFAULT_AI_MCP_CONFIG };
    this.loadConfiguration();
  }

  public static getInstance(): AIMCPConfigManager {
    if (!AIMCPConfigManager.instance) {
      AIMCPConfigManager.instance = new AIMCPConfigManager();
    }
    return AIMCPConfigManager.instance;
  }

  /**
   * 统一配置加载方法
   */
  private loadConfiguration(): void {
    // 1. 首先尝试从配置文件加载
    this.loadFromConfigFile();

    // 2. 然后从环境变量覆盖
    this.loadFromEnvironment();

    // 3. 最后验证配置完整性
    this.validateConfiguration();
  }

  /**
   * 从配置文件加载多模型配置
   */
  private loadFromConfigFile(): void {
    try {
      const os = require('os');
      const path = require('path');
      const fs = require('fs');

      // 支持多个配置文件路径
      const configPaths = [
        path.join(os.homedir(), 'Library/Application Support/demand-insight-assistant/.insight.config.json'),
        path.join(process.cwd(), '.insight.config.json'),
        path.join(process.cwd(), 'config/ai-config.json')
      ];

      for (const configPath of configPaths) {
        if (fs.existsSync(configPath)) {
          const configData = JSON.parse(fs.readFileSync(configPath, 'utf-8'));

          // 加载多模型配置
          if (configData.multiModel) {
            this.multiModelConfig = configData.multiModel;
            console.log(`[AIMCPConfig] ✅ Multi-model configuration loaded from: ${configPath}`);
          }

          // 加载单一配置（向后兼容）
          if (configData.apiKey && configData.modelId) {
            this.config.agent.apiKey = configData.apiKey;
            this.config.agent.model = configData.modelId;
            // 确保baseURL正确设置
            this.config.agent.baseURL = configData.endpoint || configData.baseURL || 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
            console.log(`[AIMCPConfig] ✅ Legacy configuration loaded from: ${configPath}`);
            console.log(`[AIMCPConfig] 🔧 API Config: baseURL=${this.config.agent.baseURL}, model=${this.config.agent.model}`);
          }

          break; // 使用第一个找到的配置文件
        }
      }
    } catch (error) {
      console.warn(`[AIMCPConfig] ⚠️ Failed to load configuration file: ${error}`);
    }
  }

  /**
   * 从环境变量加载配置
   */
  private loadFromEnvironment(): void {
    // 通用AI配置
    if (process.env.AI_API_KEY) {
      this.config.agent.apiKey = process.env.AI_API_KEY;
    }

    if (process.env.AI_BASE_URL) {
      this.config.agent.baseURL = process.env.AI_BASE_URL;
    }

    if (process.env.AI_MODEL) {
      this.config.agent.model = process.env.AI_MODEL;
    }

    // 火山引擎配置（向后兼容）
    if (process.env.VOLCENGINE_API_KEY) {
      this.config.agent.apiKey = process.env.VOLCENGINE_API_KEY;
    }

    if (process.env.VOLCENGINE_BASE_URL) {
      this.config.agent.baseURL = process.env.VOLCENGINE_BASE_URL;
    }

    if (process.env.VOLCENGINE_MODEL) {
      this.config.agent.model = process.env.VOLCENGINE_MODEL;
    }

    // AI参数配置
    if (process.env.AI_MAX_TOKENS) {
      this.config.agent.maxTokens = parseInt(process.env.AI_MAX_TOKENS);
    }

    if (process.env.AI_TEMPERATURE) {
      this.config.agent.temperature = parseFloat(process.env.AI_TEMPERATURE);
    }

    // 日志配置
    if (process.env.AI_LOG_LEVEL) {
      this.config.logging.level = process.env.AI_LOG_LEVEL as any;
    }

    console.log('[AIMCPConfig] ✅ Configuration loaded from environment');
  }

  /**
   * 验证配置完整性
   */
  private validateConfiguration(): void {
    const validation = this.validateConfig();
    if (!validation.isValid) {
      console.error('[AIMCPConfig] ❌ Configuration validation failed:', validation.errors);
      throw new Error(`Configuration validation failed: ${validation.errors.join(', ')}`);
    }
  }

  /**
   * 获取完整配置
   */
  public getConfig(): AIMCPConfig {
    return { ...this.config };
  }

  /**
   * 获取AI代理配置
   */
  public getAgentConfig(): AIAgentConfig {
    return { ...this.config.agent };
  }

  /**
   * 获取MCP服务器配置
   */
  public getMCPServerConfig(): MCPServerConfig {
    return { ...this.config.mcpServer };
  }

  /**
   * 更新AI代理配置
   */
  public updateAgentConfig(updates: Partial<AIAgentConfig>): void {
    this.config.agent = { ...this.config.agent, ...updates };
    console.log('[AIMCPConfig] 🔄 Agent configuration updated');
  }

  /**
   * 更新工具配置
   */
  public updateToolsConfig(updates: Partial<AIMCPConfig['tools']>): void {
    this.config.tools = { ...this.config.tools, ...updates };
    console.log('[AIMCPConfig] 🔄 Tools configuration updated');
  }

  /**
   * 获取多模型配置
   */
  public getMultiModelConfig(): MultiModelConfig | null {
    return this.multiModelConfig;
  }

  /**
   * 设置多模型配置
   */
  public setMultiModelConfig(config: MultiModelConfig): void {
    this.multiModelConfig = config;
    console.log('[AIMCPConfig] 🔄 Multi-model configuration updated');
  }

  /**
   * 根据用途获取AI配置
   */
  public getAIConfigForPurpose(purpose: 'playwrightMCP' | 'dataAnalysis' | 'default'): AIProviderConfig {
    if (this.multiModelConfig && this.multiModelConfig[purpose]) {
      return this.multiModelConfig[purpose];
    }

    // 回退到默认配置
    return {
      apiKey: this.config.agent.apiKey,
      baseURL: this.config.agent.baseURL,
      model: this.config.agent.model,
      maxTokens: this.config.agent.maxTokens,
      temperature: this.config.agent.temperature,
      enabled: true
    };
  }

  /**
   * 验证配置有效性
   */
  public validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 验证火山API配置
    if (!this.config.agent.apiKey) {
      errors.push('VOLCENGINE_API_KEY is required');
    }
    
    if (!this.config.agent.baseURL) {
      errors.push('VOLCENGINE_BASE_URL is required');
    }
    
    if (!this.config.agent.model) {
      errors.push('VOLCENGINE_MODEL is required');
    }

    // 验证数值配置
    if (this.config.agent.maxTokens <= 0) {
      errors.push('maxTokens must be greater than 0');
    }
    
    if (this.config.agent.temperature < 0 || this.config.agent.temperature > 2) {
      errors.push('temperature must be between 0 and 2');
    }
    
    if (this.config.tools.executionTimeout <= 0) {
      errors.push('executionTimeout must be greater than 0');
    }
    
    if (this.config.tools.maxRetries < 0) {
      errors.push('maxRetries must be non-negative');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 从JSON文件加载配置
   */
  public loadFromFile(configPath: string): void {
    try {
      const fs = require('fs');
      if (fs.existsSync(configPath)) {
        const fileConfig = JSON.parse(fs.readFileSync(configPath, 'utf-8'));
        this.config = { ...DEFAULT_AI_MCP_CONFIG, ...fileConfig };
        console.log(`[AIMCPConfig] ✅ Configuration loaded from file: ${configPath}`);
      }
    } catch (error) {
      console.error(`[AIMCPConfig] ❌ Failed to load configuration from file: ${error}`);
      console.log(`[AIMCPConfig] 🔄 Using default configuration`);
    }
  }

  /**
   * 保存配置到JSON文件
   */
  public saveToFile(configPath: string): void {
    try {
      const fs = require('fs');
      const path = require('path');
      
      // 确保目录存在
      const dir = path.dirname(configPath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      
      // 保存配置（排除敏感信息）
      const configToSave = { ...this.config };
      configToSave.agent.apiKey = '[REDACTED]'; // 不保存API密钥
      
      fs.writeFileSync(configPath, JSON.stringify(configToSave, null, 2));
      console.log(`[AIMCPConfig] ✅ Configuration saved to file: ${configPath}`);
    } catch (error) {
      console.error(`[AIMCPConfig] ❌ Failed to save configuration to file: ${error}`);
    }
  }

  /**
   * 重置为默认配置
   */
  public reset(): void {
    this.config = { ...DEFAULT_AI_MCP_CONFIG };
    this.loadFromEnvironment();
    console.log('[AIMCPConfig] 🔄 Configuration reset to defaults');
  }

  /**
   * 获取环境变量配置示例
   */
  public getEnvironmentExample(): string {
    return `
# 火山引擎API配置
VOLCENGINE_API_KEY=your_api_key_here
VOLCENGINE_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
VOLCENGINE_MODEL=ep-20241220161543-8xqzr

# AI参数配置
AI_MAX_TOKENS=4000
AI_TEMPERATURE=0.3

# 日志配置
AI_LOG_LEVEL=info
    `.trim();
  }
}

/**
 * 全局配置实例
 */
export const aiMCPConfig = AIMCPConfigManager.getInstance();

/**
 * 便捷的配置访问函数
 */
export const getAIMCPConfig = () => aiMCPConfig.getConfig();
export const getAgentConfig = () => aiMCPConfig.getAgentConfig();
export const getMCPServerConfig = () => aiMCPConfig.getMCPServerConfig();
