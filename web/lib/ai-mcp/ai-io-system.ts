/**
 * AI I/O系统 - 处理AI输出的呈现和交互
 * 
 * 核心功能：
 * 1. AI思考过程的实时展示
 * 2. 决策输出的结构化呈现
 * 3. 用户交互和反馈收集
 * 4. 输出内容的持久化存储
 */

import { EventEmitter } from 'events';

/**
 * AI输出类型定义
 */
export interface AIOutput {
  id: string;
  type: 'thinking' | 'decision' | 'action' | 'result' | 'error';
  content: string;
  metadata: {
    timestamp: string;
    taskId?: string;
    toolName?: string;
    duration?: number;
    confidence?: number;
  };
  structured?: any; // 结构化数据
}

/**
 * 输出渲染器接口
 */
export interface OutputRenderer {
  render(output: AIOutput): Promise<void>;
  clear(): Promise<void>;
  setVisible(visible: boolean): Promise<void>;
}

/**
 * 控制台输出渲染器
 */
export class ConsoleRenderer implements OutputRenderer {
  private enableColors: boolean;

  constructor(enableColors: boolean = true) {
    this.enableColors = enableColors;
  }

  public async render(output: AIOutput): Promise<void> {
    const timestamp = new Date(output.metadata.timestamp).toLocaleTimeString();
    const prefix = this.getTypePrefix(output.type);
    const colorCode = this.enableColors ? this.getColorCode(output.type) : '';
    const resetCode = this.enableColors ? '\x1b[0m' : '';
    
    console.log(`${colorCode}[${timestamp}] ${prefix} ${output.content}${resetCode}`);
    
    if (output.structured) {
      console.log(`${colorCode}📊 Structured Data:${resetCode}`, JSON.stringify(output.structured, null, 2));
    }
  }

  public async clear(): Promise<void> {
    console.clear();
  }

  public async setVisible(visible: boolean): Promise<void> {
    // 控制台渲染器无需实现可见性控制
  }

  private getTypePrefix(type: AIOutput['type']): string {
    switch (type) {
      case 'thinking': return '🤔 AI思考';
      case 'decision': return '🎯 AI决策';
      case 'action': return '⚡ 执行动作';
      case 'result': return '✅ 执行结果';
      case 'error': return '❌ 错误';
      default: return '📝 输出';
    }
  }

  private getColorCode(type: AIOutput['type']): string {
    switch (type) {
      case 'thinking': return '\x1b[36m'; // 青色
      case 'decision': return '\x1b[33m'; // 黄色
      case 'action': return '\x1b[34m'; // 蓝色
      case 'result': return '\x1b[32m'; // 绿色
      case 'error': return '\x1b[31m'; // 红色
      default: return '\x1b[37m'; // 白色
    }
  }
}

/**
 * WebSocket输出渲染器
 */
export class WebSocketRenderer implements OutputRenderer {
  private ws: any; // WebSocket连接
  private isVisible: boolean = true;

  constructor(ws: any) {
    this.ws = ws;
  }

  public async render(output: AIOutput): Promise<void> {
    if (!this.isVisible || !this.ws) return;

    const message = {
      type: 'ai_output',
      data: output
    };

    try {
      this.ws.emit('ai_output', message);
    } catch (error) {
      console.error('[WebSocketRenderer] Failed to send output:', error);
    }
  }

  public async clear(): Promise<void> {
    if (!this.ws) return;

    try {
      this.ws.emit('ai_output_clear', {});
    } catch (error) {
      console.error('[WebSocketRenderer] Failed to clear output:', error);
    }
  }

  public async setVisible(visible: boolean): Promise<void> {
    this.isVisible = visible;
  }
}

/**
 * Electron主进程输出渲染器
 */
export class ElectronRenderer implements OutputRenderer {
  private mainWindow: any; // Electron BrowserWindow
  private isVisible: boolean = true;

  constructor(mainWindow: any) {
    this.mainWindow = mainWindow;
  }

  public async render(output: AIOutput): Promise<void> {
    if (!this.isVisible || !this.mainWindow) return;

    try {
      this.mainWindow.webContents.send('ai-output', output);
    } catch (error) {
      console.error('[ElectronRenderer] Failed to send output:', error);
    }
  }

  public async clear(): Promise<void> {
    if (!this.mainWindow) return;

    try {
      this.mainWindow.webContents.send('ai-output-clear', {});
    } catch (error) {
      console.error('[ElectronRenderer] Failed to clear output:', error);
    }
  }

  public async setVisible(visible: boolean): Promise<void> {
    this.isVisible = visible;
  }
}

/**
 * 文件输出渲染器
 */
export class FileRenderer implements OutputRenderer {
  private filePath: string;
  private isVisible: boolean = true;

  constructor(filePath: string) {
    this.filePath = filePath;
  }

  public async render(output: AIOutput): Promise<void> {
    if (!this.isVisible) return;

    const fs = require('fs').promises;
    const path = require('path');
    
    try {
      // 确保目录存在
      const dir = path.dirname(this.filePath);
      await fs.mkdir(dir, { recursive: true });
      
      // 格式化输出
      const timestamp = new Date(output.metadata.timestamp).toISOString();
      const logLine = `[${timestamp}] ${output.type.toUpperCase()}: ${output.content}\n`;
      
      if (output.structured) {
        const structuredLine = `[${timestamp}] STRUCTURED: ${JSON.stringify(output.structured)}\n`;
        await fs.appendFile(this.filePath, logLine + structuredLine);
      } else {
        await fs.appendFile(this.filePath, logLine);
      }
    } catch (error) {
      console.error('[FileRenderer] Failed to write output:', error);
    }
  }

  public async clear(): Promise<void> {
    const fs = require('fs').promises;
    
    try {
      await fs.writeFile(this.filePath, '');
    } catch (error) {
      console.error('[FileRenderer] Failed to clear file:', error);
    }
  }

  public async setVisible(visible: boolean): Promise<void> {
    this.isVisible = visible;
  }
}

/**
 * AI I/O系统管理器
 */
export class AIIOSystem extends EventEmitter {
  private renderers: OutputRenderer[] = [];
  private outputHistory: AIOutput[] = [];
  private maxHistorySize: number = 1000;
  private isEnabled: boolean = true;

  constructor() {
    super();
  }

  /**
   * 添加输出渲染器
   */
  public addRenderer(renderer: OutputRenderer): void {
    this.renderers.push(renderer);
  }

  /**
   * 移除输出渲染器
   */
  public removeRenderer(renderer: OutputRenderer): void {
    const index = this.renderers.indexOf(renderer);
    if (index > -1) {
      this.renderers.splice(index, 1);
    }
  }

  /**
   * 输出AI内容
   */
  public async output(
    type: AIOutput['type'],
    content: string,
    metadata: Partial<AIOutput['metadata']> = {},
    structured?: any
  ): Promise<void> {
    if (!this.isEnabled) return;

    const output: AIOutput = {
      id: this.generateOutputId(),
      type,
      content,
      metadata: {
        timestamp: new Date().toISOString(),
        ...metadata
      },
      structured
    };

    // 添加到历史记录
    this.outputHistory.push(output);
    if (this.outputHistory.length > this.maxHistorySize) {
      this.outputHistory.shift();
    }

    // 渲染输出
    await Promise.all(
      this.renderers.map(renderer => 
        renderer.render(output).catch(error => 
          console.error('[AIIOSystem] Renderer error:', error)
        )
      )
    );

    // 发出事件
    this.emit('output', output);
  }

  /**
   * AI思考过程输出
   */
  public async thinking(content: string, taskId?: string): Promise<void> {
    await this.output('thinking', content, { taskId });
  }

  /**
   * AI决策输出
   */
  public async decision(content: string, confidence?: number, taskId?: string): Promise<void> {
    await this.output('decision', content, { taskId, confidence });
  }

  /**
   * 执行动作输出
   */
  public async action(content: string, toolName?: string, taskId?: string): Promise<void> {
    await this.output('action', content, { taskId, toolName });
  }

  /**
   * 执行结果输出
   */
  public async result(content: string, structured?: any, duration?: number, taskId?: string): Promise<void> {
    await this.output('result', content, { taskId, duration }, structured);
  }

  /**
   * 错误输出
   */
  public async error(content: string, taskId?: string): Promise<void> {
    await this.output('error', content, { taskId });
  }

  /**
   * 清空所有输出
   */
  public async clear(): Promise<void> {
    this.outputHistory = [];
    await Promise.all(
      this.renderers.map(renderer => 
        renderer.clear().catch(error => 
          console.error('[AIIOSystem] Clear error:', error)
        )
      )
    );
  }

  /**
   * 设置系统启用状态
   */
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * 设置渲染器可见性
   */
  public async setVisible(visible: boolean): Promise<void> {
    await Promise.all(
      this.renderers.map(renderer => 
        renderer.setVisible(visible).catch(error => 
          console.error('[AIIOSystem] Visibility error:', error)
        )
      )
    );
  }

  /**
   * 获取输出历史
   */
  public getHistory(limit?: number): AIOutput[] {
    if (limit) {
      return this.outputHistory.slice(-limit);
    }
    return [...this.outputHistory];
  }

  /**
   * 根据任务ID获取输出
   */
  public getOutputsByTaskId(taskId: string): AIOutput[] {
    return this.outputHistory.filter(output => output.metadata.taskId === taskId);
  }

  /**
   * 生成输出ID
   */
  private generateOutputId(): string {
    return `output_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 全局AI I/O系统实例
 */
export const aiIOSystem = new AIIOSystem();

// 默认添加控制台渲染器
aiIOSystem.addRenderer(new ConsoleRenderer());
