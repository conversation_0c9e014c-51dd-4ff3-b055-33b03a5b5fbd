/**
 * V3.0 AI+MCP 爬虫系统 - 统一错误处理系统
 * 
 * 这个文件提供了完整的错误处理机制，
 * 包括错误分类、处理策略和恢复机制。
 * 
 * 核心功能：
 * 1. 错误分类和标准化
 * 2. 错误处理策略
 * 3. 错误恢复机制
 * 4. 错误统计和分析
 */

/**
 * 错误类型枚举
 */
export enum ErrorType {
  // 系统错误
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  INITIALIZATION_ERROR = 'INITIALIZATION_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  
  // 网络错误
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  CONNECTION_ERROR = 'CONNECTION_ERROR',
  
  // AI相关错误
  AI_API_ERROR = 'AI_API_ERROR',
  AI_QUOTA_EXCEEDED = 'AI_QUOTA_EXCEEDED',
  AI_MODEL_ERROR = 'AI_MODEL_ERROR',
  
  // 爬虫错误
  SCRAPING_ERROR = 'SCRAPING_ERROR',
  CAPTCHA_ERROR = 'CAPTCHA_ERROR',
  LOGIN_REQUIRED = 'LOGIN_REQUIRED',
  RATE_LIMITED = 'RATE_LIMITED',
  
  // 数据错误
  DATA_VALIDATION_ERROR = 'DATA_VALIDATION_ERROR',
  DATA_PARSING_ERROR = 'DATA_PARSING_ERROR',
  DATA_QUALITY_ERROR = 'DATA_QUALITY_ERROR',
  
  // 业务错误
  TASK_ERROR = 'TASK_ERROR',
  USER_ERROR = 'USER_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  
  // 未知错误
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * 错误严重程度
 */
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * 标准化错误接口
 */
export interface StandardError {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  details?: any;
  context: {
    timestamp: string;
    component: string;
    operation: string;
    userId?: string;
    taskId?: string;
    requestId?: string;
  };
  stack?: string;
  originalError?: Error;
  retryable: boolean;
  recoveryActions: string[];
}

/**
 * 错误处理策略
 */
export interface ErrorHandlingStrategy {
  type: ErrorType;
  maxRetries: number;
  retryDelay: number;
  escalationThreshold: number;
  notificationRequired: boolean;
  autoRecover: boolean;
  fallbackAction?: () => Promise<any>;
}

/**
 * 错误统计
 */
export interface ErrorStatistics {
  totalErrors: number;
  errorsByType: Record<ErrorType, number>;
  errorsBySeverity: Record<ErrorSeverity, number>;
  errorsByComponent: Record<string, number>;
  recentErrors: StandardError[];
  errorRate: number;
  mttr: number; // Mean Time To Recovery
}

/**
 * 统一错误处理器
 */
export class UnifiedErrorHandler {
  private static instance: UnifiedErrorHandler;
  private errorHistory: StandardError[] = [];
  private errorStrategies: Map<ErrorType, ErrorHandlingStrategy> = new Map();
  private maxHistorySize: number = 1000;
  private errorCallbacks: Map<string, (error: StandardError) => void> = new Map();

  private constructor() {
    this.initializeErrorStrategies();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): UnifiedErrorHandler {
    if (!UnifiedErrorHandler.instance) {
      UnifiedErrorHandler.instance = new UnifiedErrorHandler();
    }
    return UnifiedErrorHandler.instance;
  }

  /**
   * 初始化错误处理策略
   */
  private initializeErrorStrategies(): void {
    // 系统错误策略
    this.errorStrategies.set(ErrorType.SYSTEM_ERROR, {
      type: ErrorType.SYSTEM_ERROR,
      maxRetries: 0,
      retryDelay: 0,
      escalationThreshold: 1,
      notificationRequired: true,
      autoRecover: false
    });

    // 网络错误策略
    this.errorStrategies.set(ErrorType.NETWORK_ERROR, {
      type: ErrorType.NETWORK_ERROR,
      maxRetries: 3,
      retryDelay: 2000,
      escalationThreshold: 5,
      notificationRequired: false,
      autoRecover: true
    });

    // AI API错误策略
    this.errorStrategies.set(ErrorType.AI_API_ERROR, {
      type: ErrorType.AI_API_ERROR,
      maxRetries: 2,
      retryDelay: 5000,
      escalationThreshold: 3,
      notificationRequired: true,
      autoRecover: true
    });

    // 爬虫错误策略
    this.errorStrategies.set(ErrorType.SCRAPING_ERROR, {
      type: ErrorType.SCRAPING_ERROR,
      maxRetries: 2,
      retryDelay: 3000,
      escalationThreshold: 10,
      notificationRequired: false,
      autoRecover: true
    });

    // 验证码错误策略
    this.errorStrategies.set(ErrorType.CAPTCHA_ERROR, {
      type: ErrorType.CAPTCHA_ERROR,
      maxRetries: 1,
      retryDelay: 30000,
      escalationThreshold: 1,
      notificationRequired: true,
      autoRecover: false
    });

    // 限流错误策略
    this.errorStrategies.set(ErrorType.RATE_LIMITED, {
      type: ErrorType.RATE_LIMITED,
      maxRetries: 3,
      retryDelay: 60000,
      escalationThreshold: 5,
      notificationRequired: false,
      autoRecover: true
    });
  }

  /**
   * 处理错误
   */
  public async handleError(
    error: Error | any,
    context: {
      component: string;
      operation: string;
      userId?: string;
      taskId?: string;
      requestId?: string;
    }
  ): Promise<StandardError> {
    // 标准化错误
    const standardError = this.standardizeError(error, context);
    
    // 记录错误
    this.recordError(standardError);
    
    // 执行错误处理策略
    await this.executeErrorStrategy(standardError);
    
    // 触发错误回调
    this.triggerErrorCallbacks(standardError);
    
    return standardError;
  }

  /**
   * 标准化错误
   */
  private standardizeError(
    error: Error | any,
    context: {
      component: string;
      operation: string;
      userId?: string;
      taskId?: string;
      requestId?: string;
    }
  ): StandardError {
    const errorType = this.classifyError(error);
    const severity = this.determineSeverity(errorType, error);
    
    return {
      id: this.generateErrorId(),
      type: errorType,
      severity,
      message: error.message || String(error),
      details: this.extractErrorDetails(error),
      context: {
        timestamp: new Date().toISOString(),
        ...context
      },
      stack: error.stack,
      originalError: error instanceof Error ? error : undefined,
      retryable: this.isRetryable(errorType),
      recoveryActions: this.getRecoveryActions(errorType)
    };
  }

  /**
   * 错误分类
   */
  private classifyError(error: any): ErrorType {
    const message = error.message?.toLowerCase() || '';
    const code = error.code?.toLowerCase() || '';
    
    // 网络相关错误
    if (message.includes('network') || message.includes('timeout') || 
        message.includes('connection') || code.includes('econnrefused')) {
      return ErrorType.NETWORK_ERROR;
    }
    
    // AI API错误
    if (message.includes('api') && (message.includes('quota') || message.includes('limit'))) {
      return ErrorType.AI_QUOTA_EXCEEDED;
    }
    if (message.includes('model') || message.includes('openai') || message.includes('ai')) {
      return ErrorType.AI_API_ERROR;
    }
    
    // 爬虫相关错误
    if (message.includes('captcha') || message.includes('verification')) {
      return ErrorType.CAPTCHA_ERROR;
    }
    if (message.includes('login') || message.includes('auth')) {
      return ErrorType.LOGIN_REQUIRED;
    }
    if (message.includes('rate') || message.includes('limit') || message.includes('throttle')) {
      return ErrorType.RATE_LIMITED;
    }
    if (message.includes('scraping') || message.includes('selector') || message.includes('element')) {
      return ErrorType.SCRAPING_ERROR;
    }
    
    // 数据相关错误
    if (message.includes('validation') || message.includes('invalid')) {
      return ErrorType.DATA_VALIDATION_ERROR;
    }
    if (message.includes('parse') || message.includes('json') || message.includes('format')) {
      return ErrorType.DATA_PARSING_ERROR;
    }
    
    // 系统错误
    if (message.includes('system') || message.includes('initialization')) {
      return ErrorType.SYSTEM_ERROR;
    }
    
    return ErrorType.UNKNOWN_ERROR;
  }

  /**
   * 确定错误严重程度
   */
  private determineSeverity(errorType: ErrorType, error: any): ErrorSeverity {
    switch (errorType) {
      case ErrorType.SYSTEM_ERROR:
      case ErrorType.INITIALIZATION_ERROR:
      case ErrorType.AI_QUOTA_EXCEEDED:
        return ErrorSeverity.CRITICAL;
        
      case ErrorType.AI_API_ERROR:
      case ErrorType.CAPTCHA_ERROR:
      case ErrorType.LOGIN_REQUIRED:
        return ErrorSeverity.HIGH;
        
      case ErrorType.NETWORK_ERROR:
      case ErrorType.SCRAPING_ERROR:
      case ErrorType.RATE_LIMITED:
        return ErrorSeverity.MEDIUM;
        
      default:
        return ErrorSeverity.LOW;
    }
  }

  /**
   * 判断是否可重试
   */
  private isRetryable(errorType: ErrorType): boolean {
    const nonRetryableTypes = [
      ErrorType.SYSTEM_ERROR,
      ErrorType.CONFIGURATION_ERROR,
      ErrorType.DATA_VALIDATION_ERROR,
      ErrorType.PERMISSION_ERROR
    ];
    
    return !nonRetryableTypes.includes(errorType);
  }

  /**
   * 获取恢复建议
   */
  private getRecoveryActions(errorType: ErrorType): string[] {
    const recoveryMap: Record<ErrorType, string[]> = {
      // 系统错误
      [ErrorType.SYSTEM_ERROR]: ['重启系统', '检查配置', '联系技术支持'],
      [ErrorType.INITIALIZATION_ERROR]: ['重新初始化', '检查依赖', '重启应用'],
      [ErrorType.CONFIGURATION_ERROR]: ['检查配置文件', '修正配置参数', '重置默认配置'],

      // 网络错误
      [ErrorType.NETWORK_ERROR]: ['检查网络连接', '重试请求', '切换网络'],
      [ErrorType.TIMEOUT_ERROR]: ['增加超时时间', '重试请求', '检查网络状况'],
      [ErrorType.CONNECTION_ERROR]: ['检查连接状态', '重新连接', '切换服务器'],

      // AI相关错误
      [ErrorType.AI_API_ERROR]: ['检查API密钥', '重试请求', '切换模型'],
      [ErrorType.AI_QUOTA_EXCEEDED]: ['等待配额重置', '升级API计划', '优化请求频率'],
      [ErrorType.AI_MODEL_ERROR]: ['切换AI模型', '检查模型配置', '联系API提供商'],

      // 爬虫错误
      [ErrorType.SCRAPING_ERROR]: ['检查页面结构', '更新选择器', '调整爬虫策略'],
      [ErrorType.CAPTCHA_ERROR]: ['人工处理验证码', '切换账号', '降低访问频率'],
      [ErrorType.LOGIN_REQUIRED]: ['重新登录', '检查账号状态', '更新认证信息'],
      [ErrorType.RATE_LIMITED]: ['降低请求频率', '等待限制解除', '使用多个账号'],

      // 数据错误
      [ErrorType.DATA_VALIDATION_ERROR]: ['检查数据格式', '修正输入参数', '更新验证规则'],
      [ErrorType.DATA_PARSING_ERROR]: ['检查数据结构', '更新解析逻辑', '验证数据源'],
      [ErrorType.DATA_QUALITY_ERROR]: ['提高数据质量', '增加过滤条件', '手动验证数据'],

      // 业务错误
      [ErrorType.TASK_ERROR]: ['重新执行任务', '检查任务参数', '分解任务步骤'],
      [ErrorType.USER_ERROR]: ['检查用户输入', '提供使用指导', '修正操作步骤'],
      [ErrorType.PERMISSION_ERROR]: ['检查权限设置', '更新访问凭证', '联系管理员'],

      // 未知错误
      [ErrorType.UNKNOWN_ERROR]: ['查看错误详情', '重试操作', '联系技术支持']
    };
    
    return recoveryMap[errorType] || ['查看错误详情', '联系技术支持'];
  }

  /**
   * 提取错误详情
   */
  private extractErrorDetails(error: any): any {
    const details: any = {};
    
    if (error.code) details.code = error.code;
    if (error.status) details.status = error.status;
    if (error.response) details.response = error.response;
    if (error.config) details.config = error.config;
    if (error.cause) details.cause = error.cause;
    
    return Object.keys(details).length > 0 ? details : undefined;
  }

  /**
   * 记录错误
   */
  private recordError(error: StandardError): void {
    this.errorHistory.push(error);
    
    // 限制历史记录大小
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(-this.maxHistorySize);
    }
    
    console.error(`[ErrorHandler] ${error.severity.toUpperCase()} ${error.type}:`, {
      id: error.id,
      message: error.message,
      component: error.context.component,
      operation: error.context.operation,
      timestamp: error.context.timestamp
    });
  }

  /**
   * 执行错误处理策略
   */
  private async executeErrorStrategy(error: StandardError): Promise<void> {
    const strategy = this.errorStrategies.get(error.type);
    if (!strategy) return;
    
    // 检查是否需要通知
    if (strategy.notificationRequired) {
      await this.sendErrorNotification(error);
    }
    
    // 执行自动恢复
    if (strategy.autoRecover && strategy.fallbackAction) {
      try {
        await strategy.fallbackAction();
        console.log(`[ErrorHandler] Auto-recovery executed for error ${error.id}`);
      } catch (recoveryError) {
        console.error(`[ErrorHandler] Auto-recovery failed for error ${error.id}:`, recoveryError);
      }
    }
  }

  /**
   * 发送错误通知
   */
  private async sendErrorNotification(error: StandardError): Promise<void> {
    // 这里可以实现各种通知方式：邮件、短信、Webhook等
    console.warn(`[ErrorHandler] 🚨 Critical error notification:`, {
      id: error.id,
      type: error.type,
      severity: error.severity,
      message: error.message,
      component: error.context.component
    });
  }

  /**
   * 触发错误回调
   */
  private triggerErrorCallbacks(error: StandardError): void {
    this.errorCallbacks.forEach((callback, id) => {
      try {
        callback(error);
      } catch (callbackError) {
        console.error(`[ErrorHandler] Error in callback ${id}:`, callbackError);
      }
    });
  }

  /**
   * 注册错误回调
   */
  public registerErrorCallback(id: string, callback: (error: StandardError) => void): void {
    this.errorCallbacks.set(id, callback);
  }

  /**
   * 注销错误回调
   */
  public unregisterErrorCallback(id: string): void {
    this.errorCallbacks.delete(id);
  }

  /**
   * 获取错误统计
   */
  public getErrorStatistics(): ErrorStatistics {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    const recentErrors = this.errorHistory.filter(
      error => now - new Date(error.context.timestamp).getTime() < oneHour
    );
    
    const errorsByType = this.errorHistory.reduce((acc, error) => {
      acc[error.type] = (acc[error.type] || 0) + 1;
      return acc;
    }, {} as Record<ErrorType, number>);
    
    const errorsBySeverity = this.errorHistory.reduce((acc, error) => {
      acc[error.severity] = (acc[error.severity] || 0) + 1;
      return acc;
    }, {} as Record<ErrorSeverity, number>);
    
    const errorsByComponent = this.errorHistory.reduce((acc, error) => {
      acc[error.context.component] = (acc[error.context.component] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    return {
      totalErrors: this.errorHistory.length,
      errorsByType,
      errorsBySeverity,
      errorsByComponent,
      recentErrors: recentErrors.slice(-10),
      errorRate: recentErrors.length / Math.max(1, this.errorHistory.length),
      mttr: this.calculateMTTR()
    };
  }

  /**
   * 计算平均恢复时间
   */
  private calculateMTTR(): number {
    // 简化实现，实际中需要跟踪错误恢复时间
    return 300; // 5分钟
  }

  /**
   * 清理错误历史
   */
  public clearErrorHistory(): void {
    this.errorHistory = [];
    console.log('[ErrorHandler] Error history cleared');
  }

  /**
   * 生成错误ID
   */
  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 错误处理装饰器
 */
export function HandleErrors(component: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      try {
        return await method.apply(this, args);
      } catch (error) {
        const errorHandler = UnifiedErrorHandler.getInstance();
        const standardError = await errorHandler.handleError(error, {
          component,
          operation: propertyName,
          requestId: args[0]?.requestId || 'unknown'
        });

        throw standardError;
      }
    };

    return descriptor;
  };
}
