/**
 * V3.0 AI+MCP 爬虫系统 - 结构化日志系统
 * 
 * 这个文件提供了完整的结构化日志记录系统，
 * 支持多级别日志、结构化输出和日志分析。
 * 
 * 核心功能：
 * 1. 多级别日志记录
 * 2. 结构化日志格式
 * 3. 日志过滤和搜索
 * 4. 性能监控集成
 */

import * as fs from 'fs';
import * as path from 'path';

/**
 * 日志级别
 */
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4
}

/**
 * 日志条目接口
 */
export interface LogEntry {
  id: string;
  timestamp: string;
  level: LogLevel;
  levelName: string;
  message: string;
  component: string;
  operation?: string;
  context: {
    userId?: string;
    taskId?: string;
    requestId?: string;
    sessionId?: string;
    [key: string]: any;
  };
  metadata: {
    duration?: number;
    memoryUsage?: number;
    cpuUsage?: number;
    [key: string]: any;
  };
  tags: string[];
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
}

/**
 * 日志配置
 */
export interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableFile: boolean;
  filePath?: string;
  maxFileSize: number;
  maxFiles: number;
  enableStructured: boolean;
  enablePerformanceLogging: boolean;
  filters: {
    components?: string[];
    operations?: string[];
    excludeComponents?: string[];
  };
}

/**
 * 日志输出器接口
 */
export interface LogOutput {
  write(entry: LogEntry): Promise<void>;
  flush?(): Promise<void>;
  close?(): Promise<void>;
}

/**
 * 控制台输出器
 */
export class ConsoleOutput implements LogOutput {
  private enableColors: boolean;

  constructor(enableColors: boolean = true) {
    this.enableColors = enableColors;
  }

  public async write(entry: LogEntry): Promise<void> {
    const formatted = this.formatForConsole(entry);
    
    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(formatted);
        break;
      case LogLevel.INFO:
        console.info(formatted);
        break;
      case LogLevel.WARN:
        console.warn(formatted);
        break;
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        console.error(formatted);
        break;
    }
  }

  private formatForConsole(entry: LogEntry): string {
    const timestamp = new Date(entry.timestamp).toISOString();
    const level = entry.levelName.padEnd(5);
    const component = entry.component.padEnd(20);
    
    let message = `[${timestamp}] ${level} [${component}] ${entry.message}`;
    
    if (entry.context.taskId) {
      message += ` (Task: ${entry.context.taskId})`;
    }
    
    if (entry.metadata.duration) {
      message += ` (${entry.metadata.duration}ms)`;
    }
    
    if (entry.error) {
      message += `\n  Error: ${entry.error.message}`;
      if (entry.error.stack) {
        message += `\n  Stack: ${entry.error.stack}`;
      }
    }
    
    return this.enableColors ? this.colorize(message, entry.level) : message;
  }

  private colorize(message: string, level: LogLevel): string {
    const colors = {
      [LogLevel.DEBUG]: '\x1b[36m', // Cyan
      [LogLevel.INFO]: '\x1b[32m',  // Green
      [LogLevel.WARN]: '\x1b[33m',  // Yellow
      [LogLevel.ERROR]: '\x1b[31m', // Red
      [LogLevel.FATAL]: '\x1b[35m'  // Magenta
    };
    
    const reset = '\x1b[0m';
    return `${colors[level]}${message}${reset}`;
  }
}

/**
 * 文件输出器
 */
export class FileOutput implements LogOutput {
  private filePath: string;
  private maxFileSize: number;
  private maxFiles: number;
  private currentSize: number = 0;

  constructor(filePath: string, maxFileSize: number = 10 * 1024 * 1024, maxFiles: number = 5) {
    this.filePath = filePath;
    this.maxFileSize = maxFileSize;
    this.maxFiles = maxFiles;
    
    // 确保目录存在
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    // 获取当前文件大小
    if (fs.existsSync(filePath)) {
      this.currentSize = fs.statSync(filePath).size;
    }
  }

  public async write(entry: LogEntry): Promise<void> {
    const formatted = JSON.stringify(entry) + '\n';
    
    // 检查是否需要轮转日志
    if (this.currentSize + formatted.length > this.maxFileSize) {
      await this.rotateLog();
    }
    
    fs.appendFileSync(this.filePath, formatted);
    this.currentSize += formatted.length;
  }

  private async rotateLog(): Promise<void> {
    // 删除最旧的日志文件
    const oldestFile = `${this.filePath}.${this.maxFiles}`;
    if (fs.existsSync(oldestFile)) {
      fs.unlinkSync(oldestFile);
    }
    
    // 轮转现有文件
    for (let i = this.maxFiles - 1; i >= 1; i--) {
      const currentFile = i === 1 ? this.filePath : `${this.filePath}.${i}`;
      const nextFile = `${this.filePath}.${i + 1}`;
      
      if (fs.existsSync(currentFile)) {
        fs.renameSync(currentFile, nextFile);
      }
    }
    
    this.currentSize = 0;
  }

  public async flush(): Promise<void> {
    // 文件系统会自动刷新，这里不需要特殊处理
  }

  public async close(): Promise<void> {
    // 文件输出器不需要特殊的关闭操作
  }
}

/**
 * 结构化日志记录器
 */
export class StructuredLogger {
  private static instance: StructuredLogger;
  private config: LoggerConfig;
  private outputs: LogOutput[] = [];
  private logBuffer: LogEntry[] = [];
  private flushInterval: NodeJS.Timeout | null = null;

  private constructor(config: LoggerConfig) {
    this.config = config;
    this.setupOutputs();
    this.startFlushTimer();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(config?: LoggerConfig): StructuredLogger {
    if (!StructuredLogger.instance) {
      const defaultConfig: LoggerConfig = {
        level: LogLevel.INFO,
        enableConsole: true,
        enableFile: true,
        filePath: './logs/system.log',
        maxFileSize: 10 * 1024 * 1024,
        maxFiles: 5,
        enableStructured: true,
        enablePerformanceLogging: true,
        filters: {}
      };
      
      StructuredLogger.instance = new StructuredLogger(config || defaultConfig);
    }
    return StructuredLogger.instance;
  }

  /**
   * 设置输出器
   */
  private setupOutputs(): void {
    if (this.config.enableConsole) {
      this.outputs.push(new ConsoleOutput());
    }
    
    if (this.config.enableFile && this.config.filePath) {
      this.outputs.push(new FileOutput(
        this.config.filePath,
        this.config.maxFileSize,
        this.config.maxFiles
      ));
    }
  }

  /**
   * 启动刷新定时器
   */
  private startFlushTimer(): void {
    this.flushInterval = setInterval(async () => {
      await this.flush();
    }, 5000); // 每5秒刷新一次
  }

  /**
   * 记录调试日志
   */
  public debug(message: string, component: string, context?: any, metadata?: any): void {
    this.log(LogLevel.DEBUG, message, component, context, metadata);
  }

  /**
   * 记录信息日志
   */
  public info(message: string, component: string, context?: any, metadata?: any): void {
    this.log(LogLevel.INFO, message, component, context, metadata);
  }

  /**
   * 记录警告日志
   */
  public warn(message: string, component: string, context?: any, metadata?: any): void {
    this.log(LogLevel.WARN, message, component, context, metadata);
  }

  /**
   * 记录错误日志
   */
  public error(message: string, component: string, error?: Error, context?: any, metadata?: any): void {
    const errorInfo = error ? {
      name: error.name,
      message: error.message,
      stack: error.stack
    } : undefined;
    
    this.log(LogLevel.ERROR, message, component, context, metadata, errorInfo);
  }

  /**
   * 记录致命错误日志
   */
  public fatal(message: string, component: string, error?: Error, context?: any, metadata?: any): void {
    const errorInfo = error ? {
      name: error.name,
      message: error.message,
      stack: error.stack
    } : undefined;
    
    this.log(LogLevel.FATAL, message, component, context, metadata, errorInfo);
  }

  /**
   * 记录性能日志
   */
  public performance(
    operation: string,
    component: string,
    duration: number,
    context?: any,
    additionalMetadata?: any
  ): void {
    if (!this.config.enablePerformanceLogging) return;
    
    const metadata = {
      duration,
      memoryUsage: process.memoryUsage().heapUsed,
      ...additionalMetadata
    };
    
    this.log(LogLevel.INFO, `Performance: ${operation}`, component, context, metadata, undefined, ['performance']);
  }

  /**
   * 核心日志记录方法
   */
  private log(
    level: LogLevel,
    message: string,
    component: string,
    context: any = {},
    metadata: any = {},
    error?: any,
    tags: string[] = []
  ): void {
    // 检查日志级别
    if (level < this.config.level) return;
    
    // 检查过滤器
    if (!this.shouldLog(component)) return;
    
    const entry: LogEntry = {
      id: this.generateLogId(),
      timestamp: new Date().toISOString(),
      level,
      levelName: LogLevel[level],
      message,
      component,
      operation: context.operation,
      context: { ...context },
      metadata: { ...metadata },
      tags,
      error
    };
    
    // 添加到缓冲区
    this.logBuffer.push(entry);
    
    // 如果是错误或致命错误，立即刷新
    if (level >= LogLevel.ERROR) {
      this.flush();
    }
  }

  /**
   * 检查是否应该记录日志
   */
  private shouldLog(component: string): boolean {
    const { filters } = this.config;
    
    // 检查排除组件
    if (filters.excludeComponents?.includes(component)) {
      return false;
    }
    
    // 检查包含组件
    if (filters.components && filters.components.length > 0) {
      return filters.components.includes(component);
    }
    
    return true;
  }

  /**
   * 刷新日志缓冲区
   */
  public async flush(): Promise<void> {
    if (this.logBuffer.length === 0) return;
    
    const entries = [...this.logBuffer];
    this.logBuffer = [];
    
    for (const output of this.outputs) {
      for (const entry of entries) {
        try {
          await output.write(entry);
        } catch (error) {
          console.error('Failed to write log entry:', error);
        }
      }
      
      if (output.flush) {
        await output.flush();
      }
    }
  }

  /**
   * 搜索日志
   */
  public searchLogs(criteria: {
    level?: LogLevel;
    component?: string;
    operation?: string;
    timeRange?: { start: string; end: string };
    message?: string;
    tags?: string[];
  }): LogEntry[] {
    // 这里需要实现从文件读取和搜索的逻辑
    // 目前返回空数组作为占位符
    return [];
  }

  /**
   * 获取日志统计
   */
  public getLogStatistics(): {
    totalLogs: number;
    logsByLevel: Record<string, number>;
    logsByComponent: Record<string, number>;
    recentErrors: LogEntry[];
  } {
    // 这里需要实现统计逻辑
    return {
      totalLogs: 0,
      logsByLevel: {},
      logsByComponent: {},
      recentErrors: []
    };
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 重新设置输出器
    this.outputs = [];
    this.setupOutputs();
  }

  /**
   * 关闭日志记录器
   */
  public async close(): Promise<void> {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
    }
    
    await this.flush();
    
    for (const output of this.outputs) {
      if (output.close) {
        await output.close();
      }
    }
  }

  /**
   * 生成日志ID
   */
  private generateLogId(): string {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 日志装饰器
 */
export function LogExecution(component: string, operation?: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    const operationName = operation || propertyName;
    
    descriptor.value = async function (...args: any[]) {
      const logger = StructuredLogger.getInstance();
      const startTime = Date.now();
      
      logger.debug(`Starting ${operationName}`, component, {
        operation: operationName,
        args: args.length
      });
      
      try {
        const result = await method.apply(this, args);
        const duration = Date.now() - startTime;
        
        logger.performance(operationName, component, duration, {
          operation: operationName,
          success: true
        });
        
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        
        logger.error(`Failed ${operationName}`, component, error as Error, {
          operation: operationName,
          duration
        });
        
        throw error;
      }
    };
    
    return descriptor;
  };
}

// 导出默认日志实例
export const logger = StructuredLogger.getInstance();
