/**
 * 🔥 SimpleAIService - 简化的AI服务
 * 
 * 封装所有AI调用，保留精心制作的prompt工程
 * 
 * 核心设计原则：
 * 1. 保留web/lib/prompts.ts中的所有prompt工程
 * 2. 集成现有的OpenAI客户端配置
 * 3. 保持AI I/O系统的输出格式
 * 4. 提供与现有AI分析函数兼容的接口
 * 5. 使用统一的配置管理系统
 */

import { getPrimaryAIConfig, getDataAnalysisConfig } from './api-config-manager';
import {
  getExpansionPrompt,
  getClusteringPrompt,
  getMapAnalysisPrompt,
  getReduceAnalysisPrompt
} from './prompts';
import {
  AIInputFormat,
  AIOutputFormat,
  StandardCommentData
} from './data-format-standards';
import { AIInputConverter } from './data-converters';

/**
 * AI分析结果接口
 */
export interface AIAnalysisResult<T> {
  success: boolean;
  data: T;
  error?: string;
  tokensUsed?: number;
  duration?: number;
}

/**
 * 聚类结果接口
 */
export interface ClusterResult {
  theme: string;
  keywords: string[];
  selected: boolean;
}

/**
 * 评论分析结果接口
 */
export interface CommentAnalysisResult {
  产品主要优点: string[];
  产品主要缺点: string[];
  潜在机会点: string[];
}

/**
 * 市场洞察报告接口
 */
export interface MarketInsightReport {
  summary: string;
  insights: Array<{
    id: string;
    title: string;
    description: string;
    evidence: string[];
  }>;
  opportunities: Array<{
    id: string;
    title: string;
    description: string;
    priority: 'high' | 'medium' | 'low';
  }>;
}

/**
 * SimpleAIService - 简化的AI服务
 * 
 * 这个类封装所有AI调用，保留现有的精心制作的prompt，
 * 但使用简化的配置和调用方式
 */
export class SimpleAIService {
  private isInitialized: boolean = false;
  private primaryConfig: any;
  private analysisConfig: any;
  private aiInputConverter: AIInputConverter;

  constructor() {
    // 构造函数保持简单，实际初始化在initialize方法中
    this.aiInputConverter = new AIInputConverter();
  }

  /**
   * 初始化AI服务
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('[SimpleAIService] 🚀 Initializing simple AI service...');

      // 获取AI配置
      this.primaryConfig = getPrimaryAIConfig();
      this.analysisConfig = getDataAnalysisConfig();

      console.log('[SimpleAIService] 📋 Primary AI config:', {
        model: this.primaryConfig.model,
        baseURL: this.primaryConfig.baseURL
      });

      console.log('[SimpleAIService] 📋 Analysis AI config:', {
        model: this.analysisConfig.model,
        baseURL: this.analysisConfig.baseURL
      });

      this.isInitialized = true;
      console.log('[SimpleAIService] ✅ Simple AI service initialized successfully');

    } catch (error) {
      console.error('[SimpleAIService] ❌ Failed to initialize:', error);
      throw new Error(`SimpleAIService initialization failed: ${error}`);
    }
  }

  /**
   * 关键词扩展 - 使用现有的expansion prompt和标准化AI I/O格式
   */
  public async expandKeywords(keyword: string): Promise<string[]> {
    if (!this.isInitialized) {
      throw new Error('SimpleAIService not initialized. Call initialize() first.');
    }

    try {
      console.log(`[SimpleAIService] 🔍 Expanding keyword: ${keyword}`);

      // 🔥 使用标准化AI输入格式
      const aiInput = this.aiInputConverter.convertKeywordAnalysis([keyword], '请扩展这个关键词，生成相关的搜索词');

      // 构建prompt（保持现有的expansion prompt逻辑）
      const expansionPrompt = getExpansionPrompt(keyword);
      const enhancedPrompt = `${expansionPrompt}\n\n上下文信息：${aiInput.textDescription}`;

      const result = await this.callAI(enhancedPrompt, this.primaryConfig);

      // 解析JSON响应
      const expandedKeywords = this.parseJSONResponse<string[]>(result);

      if (!Array.isArray(expandedKeywords)) {
        throw new Error('Invalid expansion response format');
      }

      console.log(`[SimpleAIService] ✅ Expanded ${keyword} to ${expandedKeywords.length} keywords`);
      return expandedKeywords;

    } catch (error) {
      console.error(`[SimpleAIService] ❌ Keyword expansion failed for "${keyword}":`, error);
      // 失败时返回原始关键词
      return [keyword];
    }
  }

  /**
   * 关键词聚类 - 使用现有的clustering prompt
   */
  public async clusterKeywords(keywords: string[]): Promise<ClusterResult[]> {
    if (!this.isInitialized) {
      throw new Error('SimpleAIService not initialized. Call initialize() first.');
    }

    try {
      console.log(`[SimpleAIService] 🧠 Clustering ${keywords.length} keywords`);

      const prompt = getClusteringPrompt(keywords);
      const result = await this.callAI(prompt, this.analysisConfig); // 使用分析专用配置

      // 解析JSON响应
      const clusters = this.parseJSONResponse<Array<{theme: string, keywords: string[]}>>(result);
      
      if (!Array.isArray(clusters)) {
        throw new Error('Invalid clustering response format');
      }

      // 添加selected字段，默认为true
      const clusterResults: ClusterResult[] = clusters.map(cluster => ({
        ...cluster,
        selected: true
      }));

      console.log(`[SimpleAIService] ✅ Clustered into ${clusterResults.length} themes`);
      return clusterResults;

    } catch (error) {
      console.error(`[SimpleAIService] ❌ Keyword clustering failed:`, error);
      // 失败时返回简单分组
      return [{
        theme: '默认分组',
        keywords: keywords.slice(0, 10),
        selected: true
      }];
    }
  }

  /**
   * 评论分析 - 使用现有的map analysis prompt和标准化AI I/O格式
   */
  public async analyzeComments(
    comments: string[],
    productTitle?: string
  ): Promise<CommentAnalysisResult> {
    if (!this.isInitialized) {
      throw new Error('SimpleAIService not initialized. Call initialize() first.');
    }

    try {
      console.log(`[SimpleAIService] 📊 Analyzing ${comments.length} comments`);

      // 🔥 转换为标准化评论数据格式
      const standardComments: StandardCommentData[] = comments.map((comment, index) => ({
        id: `comment_${index}`,
        platform: 'taobao', // 默认平台，实际使用时应该传入
        productId: 'unknown',
        productTitle: productTitle || 'Unknown Product',
        content: comment,
        metadata: {
          date: new Date().toISOString(),
        },
        scrapedAt: new Date().toISOString()
      }));

      // 🔥 使用标准化AI输入格式
      const aiInput = this.aiInputConverter.convertCommentAnalysis(standardComments, productTitle);

      // 构建prompt（保持现有的map analysis prompt逻辑）
      const mapPrompt = getMapAnalysisPrompt(comments, productTitle);
      const enhancedPrompt = `${mapPrompt}\n\n上下文信息：${aiInput.textDescription}`;

      const result = await this.callAI(enhancedPrompt, this.analysisConfig); // 使用分析专用配置

      // 解析JSON响应
      const analysis = this.parseJSONResponse<CommentAnalysisResult>(result);

      // 验证响应格式
      if (!analysis || typeof analysis !== 'object') {
        throw new Error('Invalid analysis response format');
      }

      console.log(`[SimpleAIService] ✅ Comment analysis completed`);
      return analysis;

    } catch (error) {
      console.error(`[SimpleAIService] ❌ Comment analysis failed:`, error);
      // 失败时返回空结果
      return {
        产品主要优点: [],
        产品主要缺点: [],
        潜在机会点: []
      };
    }
  }

  /**
   * 生成市场洞察 - 使用现有的reduce analysis prompt
   */
  public async generateMarketInsights(
    analysisSummaries: CommentAnalysisResult[]
  ): Promise<MarketInsightReport> {
    if (!this.isInitialized) {
      throw new Error('SimpleAIService not initialized. Call initialize() first.');
    }

    try {
      console.log(`[SimpleAIService] 🎯 Generating market insights from ${analysisSummaries.length} summaries`);

      // 构建reduce analysis prompt - 使用正确的数据格式
      const summariesForPrompt = analysisSummaries.map((summary, index) => ({
        productTitle: `产品${index + 1}`,
        analysis: summary
      }));

      const prompt = getReduceAnalysisPrompt(summariesForPrompt);
      const result = await this.callAI(prompt, this.analysisConfig); // 使用分析专用配置

      // 解析JSON响应
      const insights = this.parseJSONResponse<MarketInsightReport>(result);
      
      // 验证响应格式
      if (!insights || typeof insights !== 'object') {
        throw new Error('Invalid insights response format');
      }

      console.log(`[SimpleAIService] ✅ Market insights generated`);
      return insights;

    } catch (error) {
      console.error(`[SimpleAIService] ❌ Market insights generation failed:`, error);
      // 失败时返回空结果
      return {
        summary: '分析过程中出现错误，无法生成完整洞察',
        insights: [],
        opportunities: []
      };
    }
  }

  /**
   * 调用AI API - 统一的AI调用方法
   */
  private async callAI(prompt: string, config: any): Promise<string> {
    try {
      // 这里使用fetch直接调用AI API，保持简单
      const response = await fetch(`${config.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.apiKey}`
        },
        body: JSON.stringify({
          model: config.model,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: config.maxTokens || 4000,
          temperature: config.temperature || 0.3
        })
      });

      if (!response.ok) {
        throw new Error(`AI API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.choices || !data.choices[0] || !data.choices[0].message) {
        throw new Error('Invalid AI API response format');
      }

      return data.choices[0].message.content;

    } catch (error) {
      console.error('[SimpleAIService] ❌ AI API call failed:', error);
      throw error;
    }
  }

  /**
   * 解析JSON响应 - 保留现有的解析逻辑
   */
  private parseJSONResponse<T>(content: string): T {
    try {
      // 尝试直接解析
      return JSON.parse(content);
    } catch (error) {
      // 如果直接解析失败，尝试提取JSON部分
      const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/) || 
                       content.match(/```\s*([\s\S]*?)\s*```/) ||
                       content.match(/\{[\s\S]*\}/) ||
                       content.match(/\[[\s\S]*\]/);
      
      if (jsonMatch) {
        try {
          return JSON.parse(jsonMatch[1] || jsonMatch[0]);
        } catch (innerError) {
          console.error('[SimpleAIService] ❌ Failed to parse extracted JSON:', innerError);
        }
      }
      
      console.error('[SimpleAIService] ❌ Failed to parse JSON response:', content);
      throw new Error('Failed to parse AI response as JSON');
    }
  }

  /**
   * 获取服务状态
   */
  public getStatus(): { initialized: boolean; primaryConfig: any; analysisConfig: any } {
    return {
      initialized: this.isInitialized,
      primaryConfig: this.primaryConfig ? {
        model: this.primaryConfig.model,
        baseURL: this.primaryConfig.baseURL
      } : null,
      analysisConfig: this.analysisConfig ? {
        model: this.analysisConfig.model,
        baseURL: this.analysisConfig.baseURL
      } : null
    };
  }

  /**
   * 清理资源
   */
  public async cleanup(): Promise<void> {
    console.log('[SimpleAIService] 🧹 Cleaning up AI service...');
    this.isInitialized = false;
  }
}
