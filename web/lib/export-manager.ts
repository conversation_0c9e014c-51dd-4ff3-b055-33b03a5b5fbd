/**
 * 🔥 真正的数据导出管理器
 * 
 * 彻底解决假Excel导出问题，实现真正的：
 * 1. Excel格式导出 (.xlsx) - 使用xlsx库
 * 2. CSV格式导出 (.csv) - 标准CSV格式
 * 3. JSON格式导出 (.json) - 结构化JSON
 * 
 * 作者: 毒舌界祖师爷的小贱狗 🐕
 * 创建时间: 2025-07-23
 */

import * as XLSX from 'xlsx';
import * as fs from 'fs';
import * as path from 'path';
import { app } from 'electron';
import { Task } from '../types/task';
import { 
  ExportFormat, 
  TaskDataStorage,
  SUPPORTED_EXPORT_FORMATS,
  SUPPORTED_DATA_TYPES 
} from './data-format-standards';
import { exportConverter } from './data-converters';
import { DataCompatibilityHelper } from './data-migration';

/**
 * 🎯 导出选项
 */
export interface ExportOptions {
  format: 'excel' | 'csv' | 'json';
  dataType: 'products' | 'comments' | 'insights' | 'all';
  includeMetadata?: boolean;
  customFileName?: string;
  outputPath?: string;
}

/**
 * 🎯 导出结果
 */
export interface ExportResult {
  success: boolean;
  filePath?: string;
  fileName?: string;
  fileSize?: number;
  recordCount?: number;
  error?: string;
  exportTime: string;
}

/**
 * 🔥 真正的导出管理器
 */
export class ExportManager {
  
  /**
   * 主导出方法 - 根据格式调用相应的导出器
   */
  static async exportTaskData(
    task: Task, 
    options: ExportOptions
  ): Promise<ExportResult> {
    const startTime = Date.now();
    console.log(`[ExportManager] 🚀 Starting ${options.format.toUpperCase()} export for task ${task.id}`);
    
    try {
      // 获取标准化数据
      const standardData = DataCompatibilityHelper.getStandardData(task);
      
      // 转换为导出格式
      const exportData = exportConverter.convert(standardData);
      
      // 生成文件名和路径
      const { fileName, filePath } = this.generateFilePaths(task, options);
      
      let recordCount = 0;
      
      // 根据格式调用相应的导出方法
      switch (options.format) {
        case 'excel':
          recordCount = await this.exportToExcel(exportData, filePath, options);
          break;
        case 'csv':
          recordCount = await this.exportToCSV(exportData, filePath, options);
          break;
        case 'json':
          recordCount = await this.exportToJSON(standardData, filePath, options);
          break;
        default:
          throw new Error(`Unsupported export format: ${options.format}`);
      }
      
      // 获取文件大小
      const stats = fs.statSync(filePath);
      const fileSize = stats.size;
      
      const processingTime = Date.now() - startTime;
      console.log(`[ExportManager] ✅ ${options.format.toUpperCase()} export completed in ${processingTime}ms`);
      console.log(`[ExportManager] 📁 File: ${fileName} (${this.formatFileSize(fileSize)})`);
      console.log(`[ExportManager] 📊 Records: ${recordCount}`);
      
      return {
        success: true,
        filePath,
        fileName,
        fileSize,
        recordCount,
        exportTime: new Date().toISOString()
      };
      
    } catch (error) {
      console.error(`[ExportManager] ❌ Export failed:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        exportTime: new Date().toISOString()
      };
    }
  }
  
  /**
   * 🔥 真正的Excel导出 - 使用xlsx库
   */
  private static async exportToExcel(
    exportData: ExportFormat,
    filePath: string,
    options: ExportOptions
  ): Promise<number> {
    console.log(`[ExportManager] 📊 Creating Excel workbook...`);
    
    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    let totalRecords = 0;
    
    // 根据数据类型添加工作表
    if (options.dataType === 'all' || options.dataType === 'products') {
      if (exportData.products.length > 0) {
        const worksheet = XLSX.utils.json_to_sheet(exportData.products);
        XLSX.utils.book_append_sheet(workbook, worksheet, '商品数据');
        totalRecords += exportData.products.length;
        console.log(`[ExportManager] ✅ Added products sheet: ${exportData.products.length} records`);
      }
    }
    
    if (options.dataType === 'all' || options.dataType === 'comments') {
      if (exportData.comments.length > 0) {
        const worksheet = XLSX.utils.json_to_sheet(exportData.comments);
        XLSX.utils.book_append_sheet(workbook, worksheet, '用户评论');
        totalRecords += exportData.comments.length;
        console.log(`[ExportManager] ✅ Added comments sheet: ${exportData.comments.length} records`);
      }
    }
    
    if (options.dataType === 'all' || options.dataType === 'insights') {
      if (exportData.insights.length > 0) {
        const worksheet = XLSX.utils.json_to_sheet(exportData.insights);
        XLSX.utils.book_append_sheet(workbook, worksheet, '洞察分析');
        totalRecords += exportData.insights.length;
        console.log(`[ExportManager] ✅ Added insights sheet: ${exportData.insights.length} records`);
      }
    }
    
    // 添加汇总表
    if (options.includeMetadata !== false) {
      const summaryWorksheet = XLSX.utils.json_to_sheet(exportData.summary);
      XLSX.utils.book_append_sheet(workbook, summaryWorksheet, '数据汇总');
      console.log(`[ExportManager] ✅ Added summary sheet`);
    }
    
    // 写入文件
    XLSX.writeFile(workbook, filePath);
    console.log(`[ExportManager] 💾 Excel file saved: ${filePath}`);
    
    return totalRecords;
  }
  
  /**
   * 🔥 真正的CSV导出 - 标准CSV格式
   */
  private static async exportToCSV(
    exportData: ExportFormat,
    filePath: string,
    options: ExportOptions
  ): Promise<number> {
    console.log(`[ExportManager] 📄 Creating CSV file...`);
    
    let csvContent = '';
    let totalRecords = 0;
    
    // 根据数据类型生成CSV内容
    switch (options.dataType) {
      case 'products':
        csvContent = this.arrayToCSV(exportData.products);
        totalRecords = exportData.products.length;
        break;
      case 'comments':
        csvContent = this.arrayToCSV(exportData.comments);
        totalRecords = exportData.comments.length;
        break;
      case 'insights':
        csvContent = this.arrayToCSV(exportData.insights);
        totalRecords = exportData.insights.length;
        break;
      case 'all':
        // 合并所有数据，添加数据类型标识
        const allData = [
          ...exportData.products.map(item => ({ ...item, dataType: '商品' })),
          ...exportData.comments.map(item => ({ ...item, dataType: '评论' })),
          ...exportData.insights.map(item => ({ ...item, dataType: '洞察' }))
        ];
        csvContent = this.arrayToCSV(allData);
        totalRecords = allData.length;
        break;
    }
    
    // 写入文件
    fs.writeFileSync(filePath, csvContent, 'utf-8');
    console.log(`[ExportManager] 💾 CSV file saved: ${filePath}`);
    
    return totalRecords;
  }
  
  /**
   * 🔥 JSON导出 - 结构化JSON格式
   */
  private static async exportToJSON(
    standardData: TaskDataStorage,
    filePath: string,
    options: ExportOptions
  ): Promise<number> {
    console.log(`[ExportManager] 📋 Creating JSON file...`);
    
    let jsonData: any;
    let totalRecords = 0;
    
    // 根据数据类型选择导出内容
    switch (options.dataType) {
      case 'products':
        jsonData = {
          dataType: 'products',
          exportTime: new Date().toISOString(),
          taskId: standardData.taskId,
          data: standardData.discoveredProducts
        };
        totalRecords = standardData.discoveredProducts.length;
        break;
      case 'comments':
        jsonData = {
          dataType: 'comments',
          exportTime: new Date().toISOString(),
          taskId: standardData.taskId,
          data: standardData.scrapedComments
        };
        totalRecords = standardData.scrapedComments.length;
        break;
      case 'insights':
        jsonData = {
          dataType: 'insights',
          exportTime: new Date().toISOString(),
          taskId: standardData.taskId,
          data: standardData.analysisResults
        };
        totalRecords = standardData.analysisResults.length;
        break;
      case 'all':
        jsonData = {
          dataType: 'all',
          exportTime: new Date().toISOString(),
          taskInfo: standardData.basicInfo,
          products: standardData.discoveredProducts,
          comments: standardData.scrapedComments,
          insights: standardData.analysisResults,
          statistics: standardData.statistics,
          metadata: standardData.metadata
        };
        totalRecords = standardData.discoveredProducts.length + 
                      standardData.scrapedComments.length + 
                      standardData.analysisResults.length;
        break;
    }
    
    // 写入文件
    const jsonContent = JSON.stringify(jsonData, null, 2);
    fs.writeFileSync(filePath, jsonContent, 'utf-8');
    console.log(`[ExportManager] 💾 JSON file saved: ${filePath}`);
    
    return totalRecords;
  }
  
  /**
   * 数组转CSV格式
   */
  private static arrayToCSV(data: any[]): string {
    if (data.length === 0) return '';
    
    // 获取所有字段名
    const headers = Object.keys(data[0]);
    
    // 生成CSV头部
    const csvHeaders = headers.map(header => `"${header}"`).join(',');
    
    // 生成CSV数据行
    const csvRows = data.map(row => {
      return headers.map(header => {
        const value = row[header];
        // 处理特殊字符和换行符
        const stringValue = String(value || '').replace(/"/g, '""');
        return `"${stringValue}"`;
      }).join(',');
    });
    
    return [csvHeaders, ...csvRows].join('\n');
  }
  
  /**
   * 生成文件名和路径
   */
  private static generateFilePaths(task: Task, options: ExportOptions): {
    fileName: string;
    filePath: string;
  } {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const keywords = task.keywords.slice(0, 3).join('-').replace(/[^\w\u4e00-\u9fa5-]/g, '_');
    
    const fileName = options.customFileName || 
      `${keywords}-${options.dataType}-${timestamp}.${options.format === 'excel' ? 'xlsx' : options.format}`;
    
    const outputDir = options.outputPath || app.getPath('downloads');
    const filePath = path.join(outputDir, fileName);
    
    return { fileName, filePath };
  }
  
  /**
   * 格式化文件大小
   */
  private static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
  
  /**
   * 批量导出多个任务
   */
  static async batchExport(
    tasks: Task[],
    options: ExportOptions
  ): Promise<ExportResult[]> {
    console.log(`[ExportManager] 🔄 Starting batch export for ${tasks.length} tasks`);
    
    const results: ExportResult[] = [];
    
    for (const task of tasks) {
      const result = await this.exportTaskData(task, {
        ...options,
        customFileName: `batch-${task.id}-${options.dataType}.${options.format === 'excel' ? 'xlsx' : options.format}`
      });
      results.push(result);
    }
    
    const successCount = results.filter(r => r.success).length;
    console.log(`[ExportManager] ✅ Batch export completed: ${successCount}/${tasks.length} successful`);
    
    return results;
  }
  
  /**
   * 获取支持的导出格式
   */
  static getSupportedFormats(): typeof SUPPORTED_EXPORT_FORMATS {
    return SUPPORTED_EXPORT_FORMATS;
  }
  
  /**
   * 获取支持的数据类型
   */
  static getSupportedDataTypes(): typeof SUPPORTED_DATA_TYPES {
    return SUPPORTED_DATA_TYPES;
  }
  
  /**
   * 验证导出选项
   */
  static validateExportOptions(options: ExportOptions): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!SUPPORTED_EXPORT_FORMATS.includes(options.format as any)) {
      errors.push(`不支持的导出格式: ${options.format}`);
    }
    
    if (!SUPPORTED_DATA_TYPES.includes(options.dataType as any)) {
      errors.push(`不支持的数据类型: ${options.dataType}`);
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
}

// 导出便捷函数
export const exportTaskData = ExportManager.exportTaskData;
export const batchExportTasks = ExportManager.batchExport;
