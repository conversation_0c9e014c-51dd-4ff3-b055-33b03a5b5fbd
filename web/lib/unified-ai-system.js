"use strict";
/**
 * 🔥 统一AI系统 - 基于OpenAI Agents SDK + 火山API
 *
 * 彻底解决SDK混乱问题，统一所有AI功能到一个系统中：
 * 1. 使用OpenAI Agents SDK作为唯一的AI客户端
 * 2. 火山引擎API作为后端
 * 3. 统一所有AI分析功能（关键词扩展、聚类、评论分析、智能决策）
 * 4. 集成MCP工具调用能力
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.analyzeProductCommentsWithDefaultConfig = exports.clusterKeywordsWithDefaultConfig = exports.expandKeywordsWithDefaultConfig = exports.makeIntelligentDecision = exports.quickAnalyzeKeywords = exports.generateMarketInsights = exports.analyzeProductComments = exports.clusterKeywords = exports.expandKeywords = exports.unifiedAISystem = exports.UnifiedAISystem = void 0;
exports.generateKeywordCloud = generateKeywordCloud;
exports.getAIServiceStatus = getAIServiceStatus;
const agents_1 = require("@openai/agents");
const zod_1 = require("zod");
const api_config_manager_1 = require("./api-config-manager");
const prompts_1 = require("./prompts");
const data_converters_1 = require("./data-converters");
/**
 * 🔥 统一AI系统类
 * 基于OpenAI Agents SDK，整合所有AI功能
 */
class UnifiedAISystem {
    constructor() {
        this.agent = null;
        this.isInitialized = false;
        this.config = {
            maxTokens: 4000,
            temperature: 0.3,
            model: 'default',
            enableMCP: true,
            enableCaching: true
        };
    }
    static getInstance() {
        if (!UnifiedAISystem.instance) {
            UnifiedAISystem.instance = new UnifiedAISystem();
        }
        return UnifiedAISystem.instance;
    }
    /**
     * 初始化统一AI系统
     */
    async initialize() {
        if (this.isInitialized)
            return;
        try {
            console.log('[UnifiedAISystem] 🚀 Initializing unified AI system...');
            // 确保API配置管理器已初始化
            const configManager = api_config_manager_1.APIConfigManager.getInstance();
            await configManager.initialize();
            // 获取配置
            const aiConfig = (0, api_config_manager_1.getPrimaryAIConfig)();
            this.config.model = aiConfig.model;
            this.config.maxTokens = aiConfig.maxTokens || 4000;
            this.config.temperature = aiConfig.temperature || 0.3;
            // 创建OpenAI Agents SDK的Agent实例
            this.agent = new agents_1.Agent({
                name: 'DemandInsightAgent',
                instructions: this.getSystemInstructions(),
                model: aiConfig.model,
                tools: this.createTools(),
            });
            this.isInitialized = true;
            console.log('[UnifiedAISystem] ✅ Unified AI system initialized successfully');
        }
        catch (error) {
            console.error('[UnifiedAISystem] ❌ Failed to initialize:', error);
            throw new Error(`Unified AI system initialization failed: ${error}`);
        }
    }
    /**
     * 🔥 关键词扩展 - 使用标准化数据格式
     */
    async expandKeywords(keyword) {
        return this.executeAITask('expandKeywords', { keyword }, async () => {
            if (!this.agent)
                throw new Error('Agent not initialized');
            // 🔥 使用标准化AI输入格式
            const aiInput = data_converters_1.aiInputConverter.convertKeywordAnalysis([keyword], '请扩展这个关键词，生成相关的搜索词');
            const prompt = `${aiInput.textDescription}\n\n结构化数据：${JSON.stringify(aiInput.structuredData, null, 2)}`;
            const result = await (0, agents_1.run)(this.agent, prompt);
            // 解析JSON响应
            if (!result.finalOutput) {
                throw new Error('No output received from agent');
            }
            const keywords = this.parseJSONResponse(result.finalOutput);
            if (!Array.isArray(keywords) || keywords.length === 0) {
                throw new Error('Invalid keyword expansion response format');
            }
            console.log(`[UnifiedAISystem] ✅ Expanded "${keyword}" to ${keywords.length} keywords using standardized format`);
            return keywords.slice(0, 10);
        });
    }
    /**
     * 🔥 关键词聚类 - 统一实现
     */
    async clusterKeywords(keywords) {
        return this.executeAITask('clusterKeywords', { keywords }, async () => {
            if (!this.agent)
                throw new Error('Agent not initialized');
            const prompt = (0, prompts_1.getClusteringPrompt)(keywords);
            const result = await (0, agents_1.run)(this.agent, prompt);
            if (!result.finalOutput) {
                throw new Error('No output received from agent');
            }
            const clusters = this.parseJSONResponse(result.finalOutput);
            if (!Array.isArray(clusters)) {
                throw new Error('Invalid clustering response format');
            }
            return clusters.map(cluster => ({ ...cluster, selected: true }));
        });
    }
    /**
     * 🔥 评论分析 - 使用标准化数据格式
     */
    async analyzeProductComments(comments, productTitle) {
        return this.executeAITask('analyzeComments', { comments, productTitle }, async () => {
            if (!this.agent)
                throw new Error('Agent not initialized');
            // 🔥 转换为标准化评论数据格式
            const standardComments = comments.map((comment, index) => ({
                id: `comment_${index}`,
                platform: 'taobao', // 默认平台
                productId: 'unknown',
                productTitle: productTitle || '未知商品',
                content: comment,
                engagement: { likes: 0, replies: 0, helpful: 0 },
                metadata: {
                    date: new Date().toISOString(),
                    isVerifiedPurchase: false,
                    hasImages: false,
                    hasVideo: false
                },
                scrapedAt: new Date().toISOString()
            }));
            // 🔥 使用标准化AI输入格式
            const aiInput = data_converters_1.aiInputConverter.convertCommentAnalysis(standardComments, productTitle || undefined);
            const prompt = `${aiInput.textDescription}\n\n结构化数据：${JSON.stringify(aiInput.structuredData, null, 2)}`;
            const result = await (0, agents_1.run)(this.agent, prompt);
            if (!result.finalOutput) {
                throw new Error('No output received from agent');
            }
            console.log(`[UnifiedAISystem] ✅ Analyzed ${comments.length} comments using standardized format`);
            return this.parseJSONResponse(result.finalOutput);
        });
    }
    /**
     * 🔥 市场洞察生成 - 统一实现
     */
    async generateMarketInsights(summaries) {
        return this.executeAITask('generateInsights', { summaries }, async () => {
            if (!this.agent)
                throw new Error('Agent not initialized');
            const prompt = (0, prompts_1.getReduceAnalysisPrompt)(summaries);
            const result = await (0, agents_1.run)(this.agent, prompt);
            if (!result.finalOutput) {
                throw new Error('No output received from agent');
            }
            return this.parseJSONResponse(result.finalOutput);
        });
    }
    /**
     * 🔥 快速关键词分析 - 已废弃
     * @deprecated 根据PRD要求，快速扫描模式不再使用AI分析，直接跳过AI预处理
     */
    async quickAnalyzeKeywords(keywords) {
        console.warn('[UnifiedAISystem] quickAnalyzeKeywords is deprecated. Quick scan mode skips AI preprocessing.');
        // 返回空结果，表示跳过AI分析
        return {
            success: true,
            data: {
                keywords,
                analysis: 'Skipped in quick scan mode',
                mode: 'quick_scan_skip'
            },
            metadata: {
                timestamp: new Date().toISOString(),
                tokensUsed: 0,
                processingTime: 0,
                model: 'skip'
            }
        };
    }
    /**
     * 🔥 智能决策 - 新增功能
     */
    async makeIntelligentDecision(context, options) {
        return this.executeAITask('intelligentDecision', { context, options }, async () => {
            if (!this.agent)
                throw new Error('Agent not initialized');
            const prompt = `基于以下上下文信息，从给定选项中做出最佳决策：

上下文：${JSON.stringify(context, null, 2)}

可选方案：${JSON.stringify(options, null, 2)}

请分析每个选项的优缺点，并推荐最佳方案。返回JSON格式：
{"recommendedOption": "选项ID", "reasoning": "推理过程", "confidence": 0.95}`;
            const result = await (0, agents_1.run)(this.agent, prompt);
            if (!result.finalOutput) {
                throw new Error('No output received from agent');
            }
            return this.parseJSONResponse(result.finalOutput);
        });
    }
    /**
     * 获取系统指令
     */
    getSystemInstructions() {
        return `你是需求洞察助手，一个专业的电商市场分析AI。你的核心能力包括：

1. 关键词扩展和聚类分析
2. 用户评论情感分析
3. 市场趋势洞察生成
4. 智能决策支持

请始终：
- 返回结构化的JSON格式数据
- 保持分析的客观性和准确性
- 基于数据驱动的洞察
- 提供可执行的建议

当前使用火山引擎API，模型：${this.config.model}`;
    }
    /**
     * 创建工具集合
     * 🔥 集成OpenAI Agents SDK的工具和MCP工具
     */
    createTools() {
        return [
            // 🔥 关键词扩展工具
            (0, agents_1.tool)({
                name: 'expand_keywords',
                description: '扩展关键词，生成相关的搜索词',
                parameters: zod_1.z.object({
                    keyword: zod_1.z.string().describe('要扩展的关键词'),
                }),
                execute: async ({ keyword }) => {
                    const result = await this.expandKeywords(keyword);
                    return { keywords: result };
                },
            }),
            // 🔥 关键词聚类工具
            (0, agents_1.tool)({
                name: 'cluster_keywords',
                description: '将关键词按主题聚类',
                parameters: zod_1.z.object({
                    keywords: zod_1.z.array(zod_1.z.string()).describe('要聚类的关键词列表'),
                }),
                execute: async ({ keywords }) => {
                    const result = await this.clusterKeywords(keywords);
                    return { clusters: result };
                },
            }),
            // 🔥 评论分析工具
            (0, agents_1.tool)({
                name: 'analyze_comments',
                description: '分析产品评论，提取优缺点和未满足需求',
                parameters: zod_1.z.object({
                    comments: zod_1.z.array(zod_1.z.string()).describe('评论列表'),
                    productTitle: zod_1.z.string().nullable().describe('产品标题'),
                }),
                execute: async ({ comments, productTitle }) => {
                    const result = await this.analyzeProductComments(comments, productTitle);
                    return result;
                },
            }),
            // 🔥 爬虫工具 - 商品链接发现
            (0, agents_1.tool)({
                name: 'discover_product_links',
                description: '发现指定平台的商品链接',
                parameters: zod_1.z.object({
                    platform: zod_1.z.enum(['taobao', 'xiaohongshu']).describe('目标平台'),
                    keywords: zod_1.z.array(zod_1.z.string()).describe('搜索关键词列表'),
                    maxResults: zod_1.z.number().min(1).max(100).default(50).describe('最大结果数量'),
                    sortBy: zod_1.z.enum(['sales', 'popularity', 'latest', 'price']).default('popularity').describe('排序方式'),
                }),
                execute: async ({ platform, keywords, maxResults, sortBy }) => {
                    // 这里会调用BrowserManager的相关功能
                    console.log(`[UnifiedAISystem] 🔍 Discovering product links on ${platform} for keywords: ${keywords.join(', ')}`);
                    return {
                        platform,
                        keywords,
                        maxResults,
                        sortBy,
                        links: [], // 实际实现会返回真实的链接
                        message: 'Product link discovery functionality integrated with OpenAI Agents SDK'
                    };
                },
            }),
            // 🔥 爬虫工具 - 商品详情抓取
            (0, agents_1.tool)({
                name: 'scrape_product_details',
                description: '抓取商品详情页面信息',
                parameters: zod_1.z.object({
                    urls: zod_1.z.array(zod_1.z.string()).describe('商品详情页URL列表'),
                    fields: zod_1.z.array(zod_1.z.string()).default(['title', 'price', 'description', 'reviews']).describe('要抓取的字段'),
                }),
                execute: async ({ urls, fields }) => {
                    console.log(`[UnifiedAISystem] 📄 Scraping product details for ${urls.length} URLs, fields: ${fields.join(', ')}`);
                    return {
                        urls,
                        fields,
                        products: [], // 实际实现会返回真实的产品数据
                        message: 'Product detail scraping functionality integrated with OpenAI Agents SDK'
                    };
                },
            }),
        ];
    }
    /**
     * 执行AI任务的通用方法
     */
    async executeAITask(taskName, _params, executor) {
        const startTime = Date.now();
        try {
            console.log(`[UnifiedAISystem] 🤖 Executing ${taskName}...`);
            const data = await executor();
            const processingTime = Date.now() - startTime;
            return {
                success: true,
                data,
                metadata: {
                    model: this.config.model,
                    tokensUsed: 0, // TODO: 从响应中获取实际token使用量
                    processingTime,
                    timestamp: new Date().toISOString()
                }
            };
        }
        catch (error) {
            console.error(`[UnifiedAISystem] ❌ ${taskName} failed:`, error);
            return {
                success: false,
                data: null,
                error: error instanceof Error ? error.message : String(error),
                metadata: {
                    model: this.config.model,
                    tokensUsed: 0,
                    processingTime: Date.now() - startTime,
                    timestamp: new Date().toISOString()
                }
            };
        }
    }
    /**
     * 解析JSON响应
     */
    parseJSONResponse(content) {
        try {
            // 尝试直接解析
            return JSON.parse(content);
        }
        catch (error) {
            // 尝试提取JSON部分
            const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/) ||
                content.match(/\{[\s\S]*\}/) ||
                content.match(/\[[\s\S]*\]/);
            if (jsonMatch) {
                try {
                    return JSON.parse(jsonMatch[1] || jsonMatch[0]);
                }
                catch (parseError) {
                    throw new Error(`Failed to parse JSON response: ${parseError}`);
                }
            }
            throw new Error(`No valid JSON found in response: ${content}`);
        }
    }
    /**
     * 获取系统状态
     */
    getStatus() {
        return {
            initialized: this.isInitialized,
            config: this.config,
            agentReady: !!this.agent
        };
    }
}
exports.UnifiedAISystem = UnifiedAISystem;
/**
 * 全局统一AI系统实例
 */
exports.unifiedAISystem = UnifiedAISystem.getInstance();
/**
 * 便捷的AI分析函数 - 统一接口
 * 🔥 完全兼容ai-client.ts的接口，但内部使用OpenAI Agents SDK
 */
// 🔥 关键词扩展 - 兼容ai-client.ts接口
const expandKeywords = async (keyword) => {
    const result = await exports.unifiedAISystem.expandKeywords(keyword);
    if (result.success) {
        return result.data;
    }
    else {
        throw new Error(result.error || 'Keyword expansion failed');
    }
};
exports.expandKeywords = expandKeywords;
// 🔥 关键词聚类 - 兼容ai-client.ts接口
const clusterKeywords = async (keywords) => {
    const result = await exports.unifiedAISystem.clusterKeywords(keywords);
    if (result.success) {
        return result.data;
    }
    else {
        throw new Error(result.error || 'Keyword clustering failed');
    }
};
exports.clusterKeywords = clusterKeywords;
// 🔥 评论分析 - 兼容ai-client.ts接口
const analyzeProductComments = async (comments, _config, // 向后兼容参数，但不使用
productTitle) => {
    const result = await exports.unifiedAISystem.analyzeProductComments(comments, productTitle);
    if (result.success) {
        return result.data;
    }
    else {
        throw new Error(result.error || 'Product comments analysis failed');
    }
};
exports.analyzeProductComments = analyzeProductComments;
// 🔥 市场洞察生成 - 兼容ai-client.ts接口
const generateMarketInsights = async (summaries) => {
    const result = await exports.unifiedAISystem.generateMarketInsights(summaries);
    if (result.success) {
        return result.data;
    }
    else {
        throw new Error(result.error || 'Market insights generation failed');
    }
};
exports.generateMarketInsights = generateMarketInsights;
// 🔥 快速关键词分析 - 兼容ai-client.ts接口
const quickAnalyzeKeywords = async (keywords) => {
    const result = await exports.unifiedAISystem.quickAnalyzeKeywords(keywords);
    if (result.success) {
        return result.data;
    }
    else {
        throw new Error(result.error || 'Quick keyword analysis failed');
    }
};
exports.quickAnalyzeKeywords = quickAnalyzeKeywords;
// 🔥 智能决策 - 新增功能
const makeIntelligentDecision = async (context, options) => {
    const result = await exports.unifiedAISystem.makeIntelligentDecision(context, options);
    if (result.success) {
        return result.data;
    }
    else {
        throw new Error(result.error || 'Intelligent decision failed');
    }
};
exports.makeIntelligentDecision = makeIntelligentDecision;
// 🔥 向后兼容的便捷函数
exports.expandKeywordsWithDefaultConfig = exports.expandKeywords;
exports.clusterKeywordsWithDefaultConfig = exports.clusterKeywords;
const analyzeProductCommentsWithDefaultConfig = (comments, productTitle) => (0, exports.analyzeProductComments)(comments, undefined, productTitle);
exports.analyzeProductCommentsWithDefaultConfig = analyzeProductCommentsWithDefaultConfig;
// 🔥 关键词云生成（本地函数，不调用API）
function generateKeywordCloud(keywords, frequencies) {
    return keywords.map((word, index) => ({
        word,
        frequency: (frequencies && frequencies[index]) || Math.floor(Math.random() * 50) + 10
    }));
}
// 🔥 AI服务状态检查（向后兼容）
async function getAIServiceStatus() {
    console.warn("getAIServiceStatus is deprecated. Availability should be checked server-side.");
    return false;
}
