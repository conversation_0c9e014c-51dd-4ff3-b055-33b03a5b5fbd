/**
 * 🔥 统一API配置管理器
 *
 * 彻底解决硬编码问题，所有API配置通过函数传参
 * 配置优先级：环境变量 > 配置文件 > 默认值
 *
 * 🎯 OpenAI官方SDK兼容性：
 * - 使用setDefaultOpenAIClient()设置全局客户端
 * - 支持火山引擎API的OpenAI兼容接口
 * - 遵循OpenAI Agents SDK最佳实践
 */

import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { OpenAI } from 'openai';

// 🔥 OpenAI Agents SDK支持（必需）
import {
  setDefaultOpenAIClient,
  setDefaultOpenAIKey,
  setOpenAIAPI,
  setTracingDisabled
} from '@openai/agents';

/**
 * AI服务提供商配置接口
 */
export interface AIProviderConfig {
  apiKey: string;
  baseURL: string;
  model: string;
  maxTokens?: number;
  temperature?: number;
  enabled?: boolean;
}

/**
 * 统一API配置接口
 */
export interface UnifiedAPIConfig {
  // 主要AI服务
  primary: AIProviderConfig;
  
  // 专用模型配置
  playwrightMCP?: AIProviderConfig;
  dataAnalysis?: AIProviderConfig;
  
  // 系统配置
  system: {
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    nodeEnv: 'development' | 'production';
    browserHeadless: boolean;
    browserTimeout: number;
  };
}

/**
 * 统一API配置管理器
 *
 * 🎯 核心原则：
 * 1. 所有API配置通过函数传参，禁止硬编码
 * 2. 环境变量优先，配置文件次之，默认值兜底
 * 3. 生产环境强制使用环境变量
 * 4. 配置验证和错误处理
 * 5. 遵循OpenAI官方SDK配置最佳实践
 */
export class APIConfigManager {
  private static instance: APIConfigManager;
  private config: UnifiedAPIConfig;
  private configLoaded: boolean = false;
  private openaiClient: any | null = null; // OpenAI client managed by @openai/agents

  private constructor() {
    this.config = this.getDefaultConfig();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): APIConfigManager {
    if (!APIConfigManager.instance) {
      APIConfigManager.instance = new APIConfigManager();
    }
    return APIConfigManager.instance;
  }

  /**
   * 初始化配置
   * 必须在使用前调用
   */
  public async initialize(): Promise<void> {
    if (this.configLoaded) {
      return;
    }

    console.log('[APIConfigManager] 🚀 Initializing unified API configuration...');

    // 1. 加载默认配置
    this.config = this.getDefaultConfig();

    // 2. 从配置文件加载（如果存在）
    this.loadFromConfigFile();

    // 3. 从环境变量加载（优先级最高）
    this.loadFromEnvironment();

    // 4. 验证配置
    this.validateConfig();

    // 5. 🔥 初始化OpenAI Agents SDK（遵循官方最佳实践）
    this.initializeOpenAIAgentsSDK();

    this.configLoaded = true;
    console.log('[APIConfigManager] ✅ Configuration loaded successfully');
  }

  /**
   * 获取主要AI服务配置
   * 🎯 所有AI调用必须通过此函数获取配置
   */
  public getPrimaryAIConfig(): AIProviderConfig {
    this.ensureInitialized();
    return { ...this.config.primary };
  }

  /**
   * 获取Playwright MCP专用配置
   */
  public getPlaywrightMCPConfig(): AIProviderConfig {
    this.ensureInitialized();
    return this.config.playwrightMCP || this.getPrimaryAIConfig();
  }

  /**
   * 获取数据分析专用配置
   */
  public getDataAnalysisConfig(): AIProviderConfig {
    this.ensureInitialized();
    return this.config.dataAnalysis || this.getPrimaryAIConfig();
  }

  /**
   * 根据用途获取AI配置
   * 🎯 统一的配置获取入口
   */
  public getAIConfigByPurpose(purpose: 'primary' | 'playwrightMCP' | 'dataAnalysis'): AIProviderConfig {
    switch (purpose) {
      case 'primary':
        return this.getPrimaryAIConfig();
      case 'playwrightMCP':
        return this.getPlaywrightMCPConfig();
      case 'dataAnalysis':
        return this.getDataAnalysisConfig();
      default:
        return this.getPrimaryAIConfig();
    }
  }

  /**
   * 获取系统配置
   */
  public getSystemConfig() {
    this.ensureInitialized();
    return { ...this.config.system };
  }

  /**
   * 获取完整配置（仅用于调试）
   */
  public getFullConfig(): UnifiedAPIConfig {
    this.ensureInitialized();
    return JSON.parse(JSON.stringify(this.config));
  }

  /**
   * 🔥 获取OpenAI客户端实例
   * 遵循OpenAI官方SDK最佳实践
   */
  public getOpenAIClient(): any { // OpenAI client type managed by @openai/agents
    this.ensureInitialized();
    if (!this.openaiClient) {
      throw new Error('OpenAI client not initialized. Call initialize() first.');
    }
    return this.openaiClient;
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): UnifiedAPIConfig {
    return {
      primary: {
        apiKey: '',
        baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
        model: 'ep-20250620085515-dr5cs',
        maxTokens: 4000,
        temperature: 0.3,
        enabled: true
      },
      system: {
        logLevel: 'info',
        nodeEnv: 'development',
        browserHeadless: true,
        browserTimeout: 30000
      }
    };
  }

  /**
   * 从配置文件加载
   */
  private loadFromConfigFile(): void {
    const configPaths = [
      path.join(os.homedir(), 'Library/Application Support/demand-insight-assistant/.insight.config.json'),
      path.join(process.cwd(), '.insight.config.json'),
      path.join(process.cwd(), 'config/ai-config.json')
    ];

    for (const configPath of configPaths) {
      if (fs.existsSync(configPath)) {
        try {
          const configData = JSON.parse(fs.readFileSync(configPath, 'utf-8'));
          
          // 向后兼容旧配置格式
          if (configData.apiKey && configData.modelId) {
            this.config.primary.apiKey = configData.apiKey;
            this.config.primary.model = configData.modelId;
            this.config.primary.baseURL = configData.baseURL || configData.endpoint || this.config.primary.baseURL;
            console.log(`[APIConfigManager] 📄 Legacy config loaded from: ${configPath}`);
          }

          break;
        } catch (error) {
          console.warn(`[APIConfigManager] ⚠️ Failed to load config file ${configPath}:`, error);
        }
      }
    }
  }

  /**
   * 从环境变量加载（优先级最高）
   */
  private loadFromEnvironment(): void {
    // 主要AI服务配置
    if (process.env.AI_API_KEY) this.config.primary.apiKey = process.env.AI_API_KEY;
    if (process.env.AI_BASE_URL) this.config.primary.baseURL = process.env.AI_BASE_URL;
    if (process.env.AI_MODEL) this.config.primary.model = process.env.AI_MODEL;
    if (process.env.AI_MAX_TOKENS) this.config.primary.maxTokens = parseInt(process.env.AI_MAX_TOKENS);
    if (process.env.AI_TEMPERATURE) this.config.primary.temperature = parseFloat(process.env.AI_TEMPERATURE);

    // Playwright MCP专用配置
    if (process.env.PLAYWRIGHT_MCP_API_KEY) {
      this.config.playwrightMCP = {
        apiKey: process.env.PLAYWRIGHT_MCP_API_KEY,
        baseURL: process.env.PLAYWRIGHT_MCP_BASE_URL || this.config.primary.baseURL,
        model: process.env.PLAYWRIGHT_MCP_MODEL || this.config.primary.model,
        maxTokens: 2000,
        temperature: 0.1,
        enabled: true
      };
    }

    // 数据分析专用配置
    if (process.env.DATA_ANALYSIS_API_KEY) {
      this.config.dataAnalysis = {
        apiKey: process.env.DATA_ANALYSIS_API_KEY,
        baseURL: process.env.DATA_ANALYSIS_BASE_URL || 'https://api.deepseek.com/v1',
        model: process.env.DATA_ANALYSIS_MODEL || 'deepseek-chat',
        maxTokens: 8000,
        temperature: 0.3,
        enabled: true
      };
    }

    // 系统配置
    if (process.env.LOG_LEVEL) this.config.system.logLevel = process.env.LOG_LEVEL as any;
    if (process.env.NODE_ENV) this.config.system.nodeEnv = process.env.NODE_ENV as any;
    if (process.env.BROWSER_HEADLESS) this.config.system.browserHeadless = process.env.BROWSER_HEADLESS === 'true';
    if (process.env.BROWSER_TIMEOUT) this.config.system.browserTimeout = parseInt(process.env.BROWSER_TIMEOUT);

    console.log('[APIConfigManager] 🌍 Environment variables loaded');
  }

  /**
   * 验证配置
   */
  private validateConfig(): void {
    const errors: string[] = [];

    // 验证主要AI配置
    if (!this.config.primary.apiKey) {
      errors.push('Primary AI API key is required (AI_API_KEY)');
    }
    if (!this.config.primary.baseURL) {
      errors.push('Primary AI base URL is required (AI_BASE_URL)');
    }
    if (!this.config.primary.model) {
      errors.push('Primary AI model is required (AI_MODEL)');
    }

    // 生产环境强制检查
    if (this.config.system.nodeEnv === 'production') {
      if (this.config.primary.apiKey.includes('your_') || this.config.primary.apiKey.includes('HERE')) {
        errors.push('Production environment requires real API keys, not placeholder values');
      }
    }

    if (errors.length > 0) {
      console.error('[APIConfigManager] ❌ Configuration validation failed:', errors);
      throw new Error(`API configuration validation failed: ${errors.join(', ')}`);
    }

    console.log('[APIConfigManager] ✅ Configuration validation passed');
  }

  /**
   * 🔥 初始化OpenAI Agents SDK
   * 遵循OpenAI官方最佳实践 - 参照官方文档配置
   */
  private initializeOpenAIAgentsSDK(): void {
    try {
      // 🔥 按照官方文档创建OpenAI客户端实例
      this.openaiClient = new OpenAI({
        apiKey: this.config.primary.apiKey,
        baseURL: this.config.primary.baseURL,
        defaultHeaders: {
          'User-Agent': 'DemandInsightAssistant/3.0'
        }
      });

      console.log(`[APIConfigManager] 🔥 OpenAI client created with baseURL: ${this.config.primary.baseURL}`);
      console.log(`[APIConfigManager] 🔥 Using model: ${this.config.primary.model}`);

      // 🎯 按照官方文档设置OpenAI Agents SDK全局配置
      // 1. 设置默认OpenAI客户端
      setDefaultOpenAIClient(this.openaiClient);

      // 2. 设置默认API Key
      setDefaultOpenAIKey(this.config.primary.apiKey);

      // 3. 配置API类型（使用Chat Completions API）
      setOpenAIAPI('chat_completions');

      // 4. 禁用追踪（避免额外开销）
      setTracingDisabled(true);

      console.log('[APIConfigManager] ✅ OpenAI Agents SDK global configuration completed');

    } catch (error) {
      console.error('[APIConfigManager] ❌ Failed to initialize OpenAI Agents SDK:', error);
      throw new Error(`OpenAI Agents SDK initialization failed: ${error}`);
    }
  }

  /**
   * 确保配置已初始化
   */
  private ensureInitialized(): void {
    if (!this.configLoaded) {
      throw new Error('APIConfigManager not initialized. Call initialize() first.');
    }
  }
}

/**
 * 🎯 统一的API配置获取函数
 * 所有组件都应该使用这些函数获取配置，而不是直接访问配置对象
 */

// 获取配置管理器实例
export const apiConfigManager = APIConfigManager.getInstance();

// 便捷的配置获取函数
export const getPrimaryAIConfig = () => apiConfigManager.getPrimaryAIConfig();
export const getPlaywrightMCPConfig = () => apiConfigManager.getPlaywrightMCPConfig();
export const getDataAnalysisConfig = () => apiConfigManager.getDataAnalysisConfig();
export const getAIConfigByPurpose = (purpose: 'primary' | 'playwrightMCP' | 'dataAnalysis') =>
  apiConfigManager.getAIConfigByPurpose(purpose);

// 🔥 OpenAI客户端获取函数
export const getOpenAIClient = () => apiConfigManager.getOpenAIClient();
