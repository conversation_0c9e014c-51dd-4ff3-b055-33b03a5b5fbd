/**
 * V2.0 专业日志管理器
 * 
 * 使用 electron-log 替换所有的 console.log，实现：
 * 1. 日志的持久化存储
 * 2. 分级管理（debug, info, warn, error）
 * 3. 文件轮转和大小限制
 * 4. 结构化日志格式
 * 5. 性能优化的异步写入
 */

import log from 'electron-log';
import * as path from 'path';
import { app } from 'electron';
import { getLoggingConfig } from '../config';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogContext {
  component?: string;
  taskId?: string;
  platform?: string;
  workerId?: string;
  userId?: string;
  sessionId?: string;
  [key: string]: any;
}

/**
 * 日志管理器类
 */
export class Logger {
  private static instance: Logger;
  private initialized = false;

  private constructor() {
    // 私有构造函数，确保单例
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  /**
   * 初始化日志系统
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    const config = getLoggingConfig();

    try {
      // 设置日志级别
      log.transports.console.level = config.logLevel;
      log.transports.file.level = config.logLevel;

      // 配置文件日志
      if (config.enableFileLogging) {
        const logDir = app ? path.join(app.getPath('userData'), 'logs') : './logs';
        
        // 主日志文件
        log.transports.file.resolvePathFn = () => path.join(logDir, 'main.log');
        log.transports.file.maxSize = config.maxLogFileSize * 1024 * 1024; // 转换为字节
        
        // 启用文件轮转
        log.transports.file.archiveLogFn = (file) => {
          const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
          return file.toString().replace('.log', `-${timestamp}.log`);
        };
      } else {
        // 禁用文件日志
        log.transports.file.level = false;
      }

      // 自定义日志格式
      log.transports.console.format = '[{y}-{m}-{d} {h}:{i}:{s}.{ms}] [{level}] {text}';
      log.transports.file.format = '[{y}-{m}-{d} {h}:{i}:{s}.{ms}] [{level}] [{processType}] {text}';

      // 设置控制台颜色
      log.transports.console.useStyles = true;

      this.initialized = true;
      this.info('Logger initialized successfully', { component: 'Logger' });
    } catch (error) {
      console.error('Failed to initialize logger:', error);
      // 降级到 console 日志
      this.initialized = false;
    }
  }

  /**
   * 格式化日志消息
   */
  private formatMessage(message: string, context?: LogContext): string {
    if (!context || Object.keys(context).length === 0) {
      return message;
    }

    const contextStr = Object.entries(context)
      .map(([key, value]) => `${key}=${value}`)
      .join(' ');
    
    return `${message} | ${contextStr}`;
  }

  /**
   * Debug 级别日志
   */
  public debug(message: string, context?: LogContext): void {
    const formattedMessage = this.formatMessage(message, context);
    if (this.initialized) {
      log.debug(formattedMessage);
    } else {
      console.debug(`[DEBUG] ${formattedMessage}`);
    }
  }

  /**
   * Info 级别日志
   */
  public info(message: string, context?: LogContext): void {
    const formattedMessage = this.formatMessage(message, context);
    if (this.initialized) {
      log.info(formattedMessage);
    } else {
      console.info(`[INFO] ${formattedMessage}`);
    }
  }

  /**
   * Warning 级别日志
   */
  public warn(message: string, context?: LogContext): void {
    const formattedMessage = this.formatMessage(message, context);
    if (this.initialized) {
      log.warn(formattedMessage);
    } else {
      console.warn(`[WARN] ${formattedMessage}`);
    }
  }

  /**
   * Error 级别日志
   */
  public error(message: string, error?: Error | any, context?: LogContext): void {
    let formattedMessage = this.formatMessage(message, context);
    
    if (error) {
      if (error instanceof Error) {
        formattedMessage += ` | Error: ${error.message} | Stack: ${error.stack}`;
      } else {
        formattedMessage += ` | Error: ${JSON.stringify(error)}`;
      }
    }

    if (this.initialized) {
      log.error(formattedMessage);
    } else {
      console.error(`[ERROR] ${formattedMessage}`);
    }
  }

  /**
   * 性能日志 - 记录操作耗时
   */
  public performance(operation: string, startTime: number, context?: LogContext): void {
    const duration = Date.now() - startTime;
    this.info(`Performance: ${operation} completed in ${duration}ms`, {
      ...context,
      operation,
      duration,
      type: 'performance'
    });
  }

  /**
   * 任务相关日志
   */
  public task(level: LogLevel, message: string, taskId: string, context?: LogContext): void {
    const taskContext = { ...context, taskId, component: 'Task' };
    this[level](message, taskContext);
  }

  /**
   * Worker相关日志
   */
  public worker(level: LogLevel, message: string, workerId: string, context?: LogContext): void {
    const workerContext = { ...context, workerId, component: 'Worker' };
    this[level](message, workerContext);
  }

  /**
   * 平台相关日志
   */
  public platform(level: LogLevel, message: string, platform: string, context?: LogContext): void {
    const platformContext = { ...context, platform, component: 'Platform' };
    this[level](message, platformContext);
  }

  /**
   * 系统相关日志
   */
  public system(level: LogLevel, message: string, context?: LogContext): void {
    const systemContext = { ...context, component: 'System' };
    this[level](message, systemContext);
  }

  /**
   * 网络相关日志
   */
  public network(level: LogLevel, message: string, context?: LogContext): void {
    const networkContext = { ...context, component: 'Network' };
    this[level](message, networkContext);
  }

  /**
   * 获取日志文件路径
   */
  public getLogFilePath(): string {
    if (this.initialized && log.transports.file.getFile) {
      return log.transports.file.getFile().path;
    }
    return '';
  }

  /**
   * 清理旧日志文件
   */
  public async cleanupOldLogs(daysToKeep: number = 7): Promise<void> {
    try {
      const fs = require('fs').promises;
      const logDir = path.dirname(this.getLogFilePath());
      
      if (!logDir) return;

      const files = await fs.readdir(logDir);
      const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);

      for (const file of files) {
        if (file.endsWith('.log') && file !== 'main.log') {
          const filePath = path.join(logDir, file);
          const stats = await fs.stat(filePath);
          
          if (stats.mtime.getTime() < cutoffTime) {
            await fs.unlink(filePath);
            this.info(`Cleaned up old log file: ${file}`, { component: 'Logger' });
          }
        }
      }
    } catch (error) {
      this.error('Failed to cleanup old logs', error, { component: 'Logger' });
    }
  }

  /**
   * 获取日志统计信息
   */
  public getLogStats(): { filePath: string; fileSize: number; isEnabled: boolean } {
    const filePath = this.getLogFilePath();
    let fileSize = 0;

    try {
      if (filePath) {
        const fs = require('fs');
        const stats = fs.statSync(filePath);
        fileSize = stats.size;
      }
    } catch (error) {
      // 文件不存在或无法访问
    }

    return {
      filePath,
      fileSize,
      isEnabled: this.initialized && getLoggingConfig().enableFileLogging
    };
  }
}

/**
 * 全局日志实例
 */
export const logger = Logger.getInstance();

/**
 * 便捷的日志函数
 */
export const logDebug = (message: string, context?: LogContext) => logger.debug(message, context);
export const logInfo = (message: string, context?: LogContext) => logger.info(message, context);
export const logWarn = (message: string, context?: LogContext) => logger.warn(message, context);
export const logError = (message: string, error?: Error | any, context?: LogContext) => logger.error(message, error, context);

/**
 * 性能监控装饰器
 */
export function logPerformance(operation: string, context?: LogContext) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      try {
        const result = await method.apply(this, args);
        logger.performance(`${operation || propertyName}`, startTime, context);
        return result;
      } catch (error) {
        logger.performance(`${operation || propertyName} (failed)`, startTime, context);
        throw error;
      }
    };

    return descriptor;
  };
}
