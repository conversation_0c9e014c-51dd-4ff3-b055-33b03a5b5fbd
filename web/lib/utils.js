"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.cn = cn;
exports.retry = retry;
const clsx_1 = require("clsx");
const tailwind_merge_1 = require("tailwind-merge");
/**
 * Utility function to merge Tailwind CSS classes with clsx
 * This is required for shadcn/ui components
 */
function cn(...inputs) {
    return (0, tailwind_merge_1.twMerge)((0, clsx_1.clsx)(inputs));
}
/**
 * A utility function to retry an async function a specified number of times.
 * @param asyncFn The async function to retry.
 * @param attempts The maximum number of attempts. Default is 3.
 * @param delay The delay between retries in milliseconds. Default is 1000ms.
 * @param logPrefix A prefix for console logs to identify which operation is being retried.
 * @returns The result of the async function if it succeeds.
 * @throws The error of the last attempt if all attempts fail.
 */
async function retry(asyncFn, attempts = 3, delay = 1000, logPrefix = '[Retry]') {
    for (let i = 0; i < attempts; i++) {
        try {
            return await asyncFn(); // Attempt to execute the function
        }
        catch (error) {
            if (i === attempts - 1) {
                // This was the last attempt, re-throw the error
                console.error(`${logPrefix} All ${attempts} attempts failed.`, error);
                throw error;
            }
            console.warn(`${logPrefix} Attempt ${i + 1}/${attempts} failed. Retrying in ${delay}ms...`, error.message);
            // Wait for the specified delay before the next attempt
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
    // This line is theoretically unreachable but required for TypeScript to be happy
    throw new Error('Retry logic exited unexpectedly.');
}
