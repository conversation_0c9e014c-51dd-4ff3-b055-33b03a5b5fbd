"use strict";
/**
 * 🔥 数据格式标准化规范
 *
 * 解决系统中数据格式混乱问题，建立统一的数据格式标准：
 * 1. API通信格式 - 与AI交互的标准化数据格式
 * 2. 本地存储格式 - 任务数据的标准化存储结构
 * 3. 用户导出格式 - CSV/Excel/JSON的标准化导出格式
 *
 * 作者: 毒舌界祖师爷的小贱狗 🐕
 * 创建时间: 2025-07-23
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AI_INPUT_TYPES = exports.SUPPORTED_DATA_TYPES = exports.SUPPORTED_EXPORT_FORMATS = exports.DATA_FORMAT_VERSION = void 0;
// ==================== 常量定义 ====================
/**
 * 🎯 数据格式版本
 */
exports.DATA_FORMAT_VERSION = '1.0.0';
/**
 * 🎯 支持的导出格式
 */
exports.SUPPORTED_EXPORT_FORMATS = ['csv', 'excel', 'json'];
/**
 * 🎯 支持的数据类型
 */
exports.SUPPORTED_DATA_TYPES = ['products', 'comments', 'insights', 'all'];
/**
 * 🎯 AI输入数据类型
 */
exports.AI_INPUT_TYPES = [
    'keyword_analysis',
    'product_discovery',
    'comment_analysis',
    'market_insights'
];
