/**
 * V2.0 配置中心 - 统一管理所有魔法数字和配置项
 * 
 * 这个文件集中管理了应用中所有的配置参数，避免硬编码的"魔法数字"
 * 散落在代码各处，提高可维护性和可配置性。
 */

export interface AppConfig {
  // 并发控制配置
  concurrency: {
    // 每个平台的最大并发数
    maxConcurrentTasksPerPlatform: number
    // 页面池大小限制
    maxPagesPerPlatform: number
    // 关键词处理并发数
    keywordProcessingConcurrency: number
  }

  // 数据采集配置
  scraping: {
    // 淘宝单页最大商品数
    taobaoMaxLinksPerPage: number
    // 小红书单页最大帖子数
    xiaohongshuMaxLinksPerPage: number
    // 评论采集最大数量
    maxCommentsPerProduct: number
    // 链接发现超时时间（毫秒）
    linkDiscoveryTimeout: number
    // 评论采集超时时间（毫秒）
    commentScrapingTimeout: number
  }

  // 健康检查与容错配置
  health: {
    // Worker失败阈值（连续失败多少次视为不健康）
    workerFailureThreshold: number
    // Worker无活动超时时间（毫秒）
    workerInactivityTimeout: number
    // 页面池等待超时时间（毫秒）
    pagePoolWaitTimeout: number
    // 任务重试次数
    taskRetryCount: number
    // 重试间隔时间（毫秒）
    retryInterval: number
  }

  // 延时配置
  delays: {
    // 关键词间随机延时范围（毫秒）
    keywordDelayMin: number
    keywordDelayMax: number
    // 页面加载等待时间（毫秒）
    pageLoadWait: number
    // 内容加载最大等待时间（毫秒）
    contentLoadMaxWait: number
  }

  // 健康检查配置（从health移动到这里）
  monitoring: {
    // 检查间隔时间（毫秒）
    healthCheckInterval: number
  }

  // 登录与会话配置
  session: {
    // 登录会话超时时间（毫秒）
    loginSessionTimeout: number
    // 登录状态检查间隔（毫秒）
    loginStatusCheckInterval: number
    // 自动登录重试次数
    autoLoginRetryCount: number
  }

  // 日志配置
  logging: {
    // 最大日志条目数
    maxLogEntries: number
    // 日志级别
    logLevel: 'debug' | 'info' | 'warn' | 'error'
    // 是否启用文件日志
    enableFileLogging: boolean
    // 日志文件最大大小（MB）
    maxLogFileSize: number
  }

  // 性能配置
  performance: {
    // 内存使用警告阈值（MB）
    memoryWarningThreshold: number
    // CPU使用警告阈值（百分比）
    cpuWarningThreshold: number
    // 性能监控间隔（毫秒）
    performanceMonitorInterval: number
  }

  // 网络配置
  network: {
    // 请求超时时间（毫秒）
    requestTimeout: number
    // 页面导航超时时间（毫秒）
    navigationTimeout: number
    // 网络重试次数
    networkRetryCount: number
    // 用户代理字符串
    userAgent: string
  }
}

/**
 * 默认配置 - 基于当前代码中的魔法数字整理
 */
export const DEFAULT_CONFIG: AppConfig = {
  concurrency: {
    maxConcurrentTasksPerPlatform: 5,        // 原 pLimit(5)
    maxPagesPerPlatform: 5,                  // 原 MAX_PAGES_PER_PLATFORM = 5
    keywordProcessingConcurrency: 5,         // 关键词并发处理数
  },

  scraping: {
    taobaoMaxLinksPerPage: 44,               // 原 links.slice(0, 44)
    xiaohongshuMaxLinksPerPage: 50,          // 原 links.slice(0, 50)
    maxCommentsPerProduct: 100,              // 每个商品最大评论数
    linkDiscoveryTimeout: 15000,             // 原 timeout: 15000
    commentScrapingTimeout: 30000,           // 评论采集超时
  },

  health: {
    workerFailureThreshold: 3,               // 原 getFailureThreshold(): 3
    workerInactivityTimeout: 600000,         // 原 10 * 60 * 1000 (10分钟)
    pagePoolWaitTimeout: 30000,              // 原 30000 (30秒)
    taskRetryCount: 3,                       // 任务重试次数
    retryInterval: 1000,                     // 原 retry 函数中的延时
  },

  delays: {
    keywordDelayMin: 1000,                   // 原 Math.random() * 2000 + 1000 的最小值
    keywordDelayMax: 3000,                   // 原 Math.random() * 2000 + 1000 的最大值
    pageLoadWait: 1000,                      // 原 waitForTimeout(1000)
    contentLoadMaxWait: 10000,               // 内容加载最大等待时间
  },

  monitoring: {
    healthCheckInterval: 1000,               // 原 setInterval(..., 1000)
  },

  session: {
    loginSessionTimeout: 300000,             // 原 5 * 60 * 1000 (5分钟)
    loginStatusCheckInterval: 5000,          // 登录状态检查间隔
    autoLoginRetryCount: 3,                  // 自动登录重试次数
  },

  logging: {
    maxLogEntries: 100,                      // 日志面板最大条目数
    logLevel: 'info',                        // 默认日志级别
    enableFileLogging: true,                 // 启用文件日志
    maxLogFileSize: 10,                      // 日志文件最大10MB
  },

  performance: {
    memoryWarningThreshold: 512,             // 内存使用超过512MB时警告
    cpuWarningThreshold: 80,                 // CPU使用超过80%时警告
    performanceMonitorInterval: 5000,        // 每5秒监控一次性能
  },

  network: {
    requestTimeout: 30000,                   // 原 timeout: 30000
    navigationTimeout: 30000,                // 页面导航超时
    networkRetryCount: 3,                    // 网络请求重试次数
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
  },
}

/**
 * 配置管理器类
 */
export class ConfigManager {
  private static instance: ConfigManager
  private config: AppConfig

  private constructor() {
    this.config = { ...DEFAULT_CONFIG }
  }

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager()
    }
    return ConfigManager.instance
  }

  /**
   * 获取完整配置
   */
  public getConfig(): AppConfig {
    return { ...this.config }
  }

  /**
   * 获取特定配置项
   */
  public get<K extends keyof AppConfig>(key: K): AppConfig[K] {
    return this.config[key]
  }

  /**
   * 更新配置项
   */
  public update<K extends keyof AppConfig>(key: K, value: Partial<AppConfig[K]>): void {
    this.config[key] = { ...this.config[key], ...value }
  }

  /**
   * 重置为默认配置
   */
  public reset(): void {
    this.config = { ...DEFAULT_CONFIG }
  }

  /**
   * 从JSON文件加载配置
   */
  public loadFromFile(configPath: string): void {
    try {
      const fs = require('fs')
      if (fs.existsSync(configPath)) {
        const fileConfig = JSON.parse(fs.readFileSync(configPath, 'utf-8'))
        this.config = { ...DEFAULT_CONFIG, ...fileConfig }
        console.log(`[ConfigManager] ✅ 配置已从文件加载: ${configPath}`)
      }
    } catch (error) {
      console.error(`[ConfigManager] ❌ 配置文件加载失败: ${error}`)
      console.log(`[ConfigManager] 🔄 使用默认配置`)
    }
  }

  /**
   * 保存配置到JSON文件
   */
  public saveToFile(configPath: string): void {
    try {
      const fs = require('fs')
      const path = require('path')
      
      // 确保目录存在
      const dir = path.dirname(configPath)
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true })
      }
      
      fs.writeFileSync(configPath, JSON.stringify(this.config, null, 2))
      console.log(`[ConfigManager] ✅ 配置已保存到文件: ${configPath}`)
    } catch (error) {
      console.error(`[ConfigManager] ❌ 配置文件保存失败: ${error}`)
    }
  }

  /**
   * 验证配置的有效性
   */
  public validate(): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    // 验证并发配置
    if (this.config.concurrency.maxConcurrentTasksPerPlatform <= 0) {
      errors.push('maxConcurrentTasksPerPlatform must be greater than 0')
    }

    if (this.config.concurrency.maxPagesPerPlatform <= 0) {
      errors.push('maxPagesPerPlatform must be greater than 0')
    }

    // 验证健康检查配置
    if (this.config.health.workerFailureThreshold <= 0) {
      errors.push('workerFailureThreshold must be greater than 0')
    }

    // 验证超时配置
    if (this.config.delays.keywordDelayMin < 0) {
      errors.push('keywordDelayMin must be non-negative')
    }

    if (this.config.delays.keywordDelayMax < this.config.delays.keywordDelayMin) {
      errors.push('keywordDelayMax must be greater than or equal to keywordDelayMin')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

/**
 * 全局配置实例
 */
export const config = ConfigManager.getInstance()

/**
 * 便捷的配置访问函数
 */
export const getConfig = () => config.getConfig()
export const getConcurrencyConfig = () => config.get('concurrency')
export const getScrapingConfig = () => config.get('scraping')
export const getHealthConfig = () => config.get('health')
export const getDelaysConfig = () => config.get('delays')
export const getMonitoringConfig = () => config.get('monitoring')
export const getSessionConfig = () => config.get('session')
export const getLoggingConfig = () => config.get('logging')
export const getPerformanceConfig = () => config.get('performance')
export const getNetworkConfig = () => config.get('network')
