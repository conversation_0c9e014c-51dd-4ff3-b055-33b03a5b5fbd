"use client"
import type { InsightReport } from "@/types/task"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { InsightCard } from "./InsightCard"
import { DemandKeywordsCloud } from "./DemandKeywordsCloud"
import { ContinuationInput } from "./ContinuationInput"

interface ReportContainerProps {
  report: InsightReport
}

export function ReportContainer({ report }: ReportContainerProps) {
  return (
    <div className="space-y-6">
      {/* Summary Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <span>📊 分析摘要</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-lg leading-relaxed text-gray-700">{report.summary}</p>
        </CardContent>
      </Card>

      {/* Demand Keywords Cloud */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <span>🔍 需求关键词热度</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DemandKeywordsCloud keywords={report.demandKeywords} />
        </CardContent>
      </Card>

      {/* Insights List */}
      <div className="space-y-4">
        <h2 className="text-xl font-bold text-gray-900 flex items-center space-x-2">
          <span>💡 未被满足的需求洞察</span>
          <Badge variant="secondary">{report.insights.length}个机会</Badge>
        </h2>

        <div className="space-y-4">
          {report.insights.map((insight, index) => (
            <InsightCard key={insight.id} insight={insight} index={index + 1} />
          ))}
        </div>
      </div>

      {/* AI Follow-up Questions */}
      <Card>
        <CardHeader>
          <CardTitle>🤖 AI 追问</CardTitle>
        </CardHeader>
        <CardContent>
          <ContinuationInput baseCode={JSON.stringify(report, null, 2)} />
        </CardContent>
      </Card>
    </div>
  )
}
