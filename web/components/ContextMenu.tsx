"use client"

import { useEffect, useRef } from "react"

interface ContextMenuProps {
  onClose: () => void
  onRename: () => void
  onDelete: () => void
}

export function ContextMenu({ onClose, onRename, onDelete }: ContextMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [onClose])

  return (
    <div
      ref={menuRef}
      className="absolute right-0 top-full mt-1 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-10"
    >
      <button onClick={onRename} className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 rounded-t-lg">
        Rename
      </button>
      <button
        onClick={onDelete}
        className="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 rounded-b-lg"
      >
        Delete
      </button>
    </div>
  )
}
