"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { SystemLogPanel, useSystemLog } from "./SystemLogPanel"
import {
  Play,
  Pause,
  Square,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock,
  Activity,
  Wifi,
  WifiOff,
  Download,
  FileSpreadsheet,
  Database,
  FileText,
  Eye,
  EyeOff,
  ExternalLink
} from "lucide-react"
import { Task } from "@/types/task"
import { WorkerStatus, SystemStatus, RealTimeStats } from "@/types/window"
import { cn } from "@/lib/utils"
import {
  mapUIStatusToChinese,
  mapScanModeToChinese,
  UI_STATUS_TO_CHINESE
} from "@/lib/task-status-mapping"

interface EnhancedTaskMonitorProps {
  task: Task | null
  className?: string
  showFollowTab?: boolean
  showDownloadTab?: boolean
}

export function EnhancedTaskMonitor({ task, className, showFollowTab = true, showDownloadTab = true }: EnhancedTaskMonitorProps) {
  // 🔧 V2.5架构：移除无效的Worker状态，简化为真实的系统状态
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    isOnline: true,
    lastHeartbeat: new Date(),
    activeConnections: 0,
    memoryUsage: 0,
    cpuUsage: 0,
    diskUsage: 0
  })
  const [realTimeStats, setRealTimeStats] = useState<RealTimeStats>({
    tasksProcessed: 0,
    successRate: 0,
    avgResponseTime: 0,
    errorsInLastHour: 0,
    activeWorkers: 0,
    queueLength: 0
  })
  const { addLog } = useSystemLog()

  // 🎯 实时进度状态监听
  const [realTimeProgress, setRealTimeProgress] = useState<{
    message?: string;
    phase?: string;
    uiStatus?: string;
    chineseStatus?: string;
    timestamp?: string;
  } | null>(null)

  // 🎯 使用映射表获取任务状态颜色
  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      'COMPLETED': 'text-green-600',
      'FAILED': 'text-red-600',
      'PENDING': 'text-gray-600',
      'EXPENTING': 'text-amber-600',
      'DISCOVERING': 'text-blue-600',
      'WAITING_CONFIRMATION': 'text-purple-600',
      'SCRAPING': 'text-blue-600',
      'ANALYZING': 'text-purple-600',
      'WAITING_USER_LOGIN': 'text-yellow-600',
      'WAITING_CAPTCHA': 'text-orange-600',
      'CANCELLED': 'text-red-600',
      'PAUSED': 'text-gray-500'
    };
    return colorMap[status] || 'text-gray-600';
  }

  // 🎯 使用映射表获取任务状态图标
  const getStatusIcon = (status: string) => {
    const iconMap: Record<string, JSX.Element> = {
      'COMPLETED': <CheckCircle className="h-4 w-4 text-green-600" />,
      'FAILED': <AlertTriangle className="h-4 w-4 text-red-600" />,
      'PENDING': <Clock className="h-4 w-4 text-gray-600" />,
      'EXPENTING': <Activity className="h-4 w-4 text-amber-600 animate-pulse" />,
      'DISCOVERING': <Activity className="h-4 w-4 text-blue-600 animate-pulse" />,
      'WAITING_CONFIRMATION': <Clock className="h-4 w-4 text-purple-600" />,
      'SCRAPING': <Activity className="h-4 w-4 text-blue-600 animate-pulse" />,
      'ANALYZING': <Activity className="h-4 w-4 text-purple-600 animate-pulse" />,
      'WAITING_USER_LOGIN': <Clock className="h-4 w-4 text-yellow-600" />,
      'WAITING_CAPTCHA': <Clock className="h-4 w-4 text-orange-600" />,
      'CANCELLED': <Square className="h-4 w-4 text-red-600" />,
      'PAUSED': <Clock className="h-4 w-4 text-gray-500" />
    };
    return iconMap[status] || <Clock className="h-4 w-4 text-gray-600" />;
  }

  // 🎯 获取中文状态显示
  const getChineseStatus = (status: string): string => {
    return mapUIStatusToChinese(status);
  }

  // 🔧 V2.5架构：简化的系统状态更新
  const updateSystemStatus = async () => {
    try {
      // 获取浏览器状态（真实存在的状态）
      const browserStatus = await window.electronAPI?.getBrowserStatus?.()
      if (browserStatus) {
        setSystemStatus(prev => ({
          ...prev,
          isOnline: browserStatus.isRunning || false,
          lastHeartbeat: new Date(),
          activeConnections: browserStatus.isRunning ? 1 : 0
        }))
      }

      // 获取任务统计（真实存在的统计）
      const taskStats = await window.electronAPI?.getTaskStats?.()
      if (taskStats) {
        setRealTimeStats(prev => ({
          ...prev,
          tasksProcessed: taskStats.completed || 0,
          successRate: taskStats.successRate || 0,
          queueLength: taskStats.pending || 0
        }))
      }
    } catch (error) {
      console.error('[EnhancedTaskMonitor] Failed to update system status:', error)
      // 保持当前状态，不更新
    }
  }

  // 处理任务控制
  const handleTaskControl = async (action: 'stop') => {
    if (!task) return

    try {
      addLog({
        level: 'info',
        category: 'task',
        message: `任务停止操作已触发`,
        taskId: task.id
      })

      // 调用实际的任务控制API
      await window.electronAPI?.controlTask?.(task.id, action)

      addLog({
        level: 'success',
        category: 'task',
        message: `任务停止操作成功`,
        taskId: task.id
      })

    } catch (error) {
      addLog({
        level: 'error',
        category: 'task',
        message: `任务控制操作失败: ${action}`,
        details: error instanceof Error ? error.message : String(error),
        taskId: task.id
      })
    }
  }

  // 处理任务重试
  const handleTaskRetry = async () => {
    if (!task) return

    try {
      addLog({
        level: 'info',
        category: 'task',
        message: `任务重试操作已触发`,
        taskId: task.id
      })

      // 调用重试任务的API
      await window.electronAPI?.retryTask?.(task.id)

      addLog({
        level: 'success',
        category: 'task',
        message: `任务重试操作成功`,
        taskId: task.id
      })

    } catch (error) {
      addLog({
        level: 'error',
        category: 'task',
        message: `任务重试操作失败`,
        details: error instanceof Error ? error.message : String(error),
        taskId: task.id
      })
    }
  }

  // 🔧 V2.5架构：重启浏览器（替代Worker重启）
  const restartBrowser = async () => {
    if (!task) return

    try {
      addLog({
        level: 'info',
        category: 'system',
        message: `正在重启浏览器...`,
        taskId: task.id
      })

      // 调用浏览器重启API
      await window.electronAPI?.restartBrowser?.()

      addLog({
        level: 'success',
        category: 'system',
        message: `浏览器重启成功`,
        taskId: task.id
      })

      // 重新获取系统状态
      await updateSystemStatus()

    } catch (error) {
      addLog({
        level: 'error',
        category: 'system',
        message: `浏览器重启失败`,
        details: error instanceof Error ? error.message : String(error),
        taskId: task.id
      })
    }
  }

  // 🎯 监听增强的任务进度事件
  useEffect(() => {
    if (!task?.id) return;

    const handleProgressUpdate = (event: any) => {
      console.log(`[EnhancedTaskMonitor] 📡 Received enhanced progress event:`, event);
      setRealTimeProgress({
        message: event.message,
        phase: event.phase,
        uiStatus: event.uiStatus,
        chineseStatus: event.chineseStatus,
        timestamp: event.timestamp
      });
    };

    // 监听增强的进度事件
    window.electronAPI?.on?.('task-progress', handleProgressUpdate);

    return () => {
      window.electronAPI?.off?.('task-progress', handleProgressUpdate);
    };
  }, [task?.id]);

  // 🔧 V2.5架构：简化的定期状态更新
  useEffect(() => {
    if (!task) return

    updateSystemStatus()

    const interval = setInterval(() => {
      updateSystemStatus()
    }, 5000)

    return () => clearInterval(interval)
  }, [task?.id])

  // 监听任务进度更新
  useEffect(() => {
    if (task?.progress?.message && task.id) {
      addLog({
        level: 'info',
        category: 'task',
        message: task.progress.message,
        taskId: task.id
      })
    }
  }, [task?.progress?.message, task?.id, addLog])

  const progressPercentage = (() => {
    if (!task?.progress) return 0

    // 根据不同阶段计算进度
    if (task.progress.discovered !== undefined && task.progress.discovered_total !== undefined) {
      return Math.round((task.progress.discovered / task.progress.discovered_total) * 100)
    }
    if (task.progress.scraped !== undefined && task.progress.scraped_total !== undefined) {
      return Math.round((task.progress.scraped / task.progress.scraped_total) * 100)
    }

    return 0
  })()

  // 处理数据下载
  const handleDownloadData = async (format: 'excel' | 'csv' | 'json', dataType: 'links' | 'comments' | 'insights' | 'all') => {
    if (!task) return;

    try {
      addLog({
        level: 'info',
        category: 'task',
        message: `开始下载${dataType}数据，格式：${format}`,
        taskId: task.id
      });

      // 调用主进程的数据导出API
      const filePath = await window.electronAPI?.exportTaskData?.(task.id, dataType, format);

      addLog({
        level: 'success',
        category: 'task',
        message: `数据下载完成：${dataType}.${format}，文件保存至：${filePath}`,
        taskId: task.id
      });
    } catch (error) {
      addLog({
        level: 'error',
        category: 'task',
        message: `数据下载失败：${error instanceof Error ? error.message : String(error)}`,
        taskId: task.id
      });
    }
  };

  if (!task) {
    return (
      <div className={cn("space-y-4", className)}>
        <Card>
          <CardContent className="p-6 text-center text-gray-500">
            <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>等待任务数据...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* 任务状态概览 */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              {getStatusIcon(task.status)}
              任务状态
            </CardTitle>
            <Badge variant={systemStatus.isOnline ? "default" : "destructive"} className="text-xs">
              {systemStatus.isOnline ? (
                <><Wifi className="h-3 w-3 mr-1" />在线</>
              ) : (
                <><WifiOff className="h-3 w-3 mr-1" />离线</>
              )}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between text-sm">
            <span>状态</span>
            <Badge className={getStatusColor(task.status)} variant="outline">
              {getChineseStatus(task.status)}
            </Badge>
          </div>

          {task.progress && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>进度</span>
                <span>{progressPercentage}%</span>
              </div>
              <Progress value={progressPercentage} className="h-1.5" />
              {task.progress.message && (
                <p className="text-xs text-gray-600 truncate" title={task.progress.message}>
                  {task.progress.message}
                </p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 跟随和下载面板 */}
      <Card className="flex-1">
        <Tabs defaultValue={showFollowTab ? "follow" : "download"} className="h-full flex flex-col">
          <CardHeader className="pb-2">
            <TabsList className="grid w-full grid-cols-2">
              {showFollowTab && (
                <TabsTrigger value="follow" className="flex items-center gap-1">
                  <Eye className="h-3 w-3" />
                  跟随
                </TabsTrigger>
              )}
              {showDownloadTab && (
                <TabsTrigger value="download" className="flex items-center gap-1">
                  <Download className="h-3 w-3" />
                  下载
                </TabsTrigger>
              )}
            </TabsList>
          </CardHeader>

          <CardContent className="flex-1 pt-0">
            {/* 跟随Tab - 显示任务进度节点 */}
            {showFollowTab && (
              <TabsContent value="follow" className="h-full mt-0">
                <div className="space-y-3 h-full overflow-y-auto">
                  {/* 任务进度节点 */}
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-700">任务进度</h4>
                    <div className="space-y-2">
                      {/* 动态显示当前步骤 */}
                      <div className="flex items-center gap-2 p-2 bg-blue-50 rounded-lg">
                        <Activity className="h-4 w-4 text-blue-500 animate-pulse" />
                        <div className="flex-1">
                          <div className="text-sm font-medium text-blue-700">
                            {realTimeProgress?.chineseStatus || getChineseStatus(task.status)}
                          </div>
                          <div className="text-xs text-blue-600">
                            {realTimeProgress?.message || task.progress?.message || '正在处理...'}
                          </div>
                        </div>
                      </div>

                      {/* 数据统计 */}
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div className="p-2 bg-gray-50 rounded">
                          <div className="font-medium">发现链接</div>
                          <div className="text-gray-600">
                            {task.progress?.discovered || 0} / {task.progress?.discovered_total || 0}
                          </div>
                        </div>
                        <div className="p-2 bg-gray-50 rounded">
                          <div className="font-medium">采集评论</div>
                          <div className="text-gray-600">
                            {task.progress?.scraped || 0} / {task.progress?.scraped_total || 0}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 任务控制 */}
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-700">任务控制</h4>
                    <div className="flex flex-col gap-2">
                      {/* 🔥 V3.0 AI+MCP系统：移除无效的暂停/恢复功能 */}
                      <div className="text-sm text-gray-500 p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                          <Activity className="h-4 w-4" />
                          <span className="font-medium">智能任务系统</span>
                        </div>
                        <p className="text-xs leading-relaxed">
                          系统采用智能流式处理，任务会根据实际情况自动调整执行策略。
                          如需中断任务，请使用下方的"停止任务"功能。
                        </p>
                      </div>

                      {/* 停止任务按钮 */}
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleTaskControl('stop')}
                        disabled={!['DISCOVERING', 'SCRAPING', 'ANALYZING', 'PENDING'].includes(task.status)}
                        className="justify-start text-red-600 hover:bg-red-50 border-red-200"
                      >
                        <Square className="h-3 w-3 mr-2" />
                        停止任务
                      </Button>

                      {/* 重试任务按钮 */}
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleTaskRetry}
                        disabled={task.status !== 'FAILED'}
                        className="justify-start text-blue-600 hover:bg-blue-50 border-blue-200"
                      >
                        <RefreshCw className="h-3 w-3 mr-2" />
                        重试任务
                      </Button>
                    </div>
                  </div>
                </div>
              </TabsContent>
            )}

            {/* 下载Tab - 数据下载功能 */}
            {showDownloadTab && (
              <TabsContent value="download" className="h-full mt-0">
                <div className="space-y-4 h-full overflow-y-auto">
                  {/* 数据类型选择 */}
                  <div className="space-y-3">
                    <h4 className="text-sm font-medium text-gray-700">数据下载</h4>

                    {/* 商品链接数据 */}
                    <div className="p-3 border rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <ExternalLink className="h-4 w-4 text-blue-500" />
                        <span className="text-sm font-medium">商品链接</span>
                        <Badge variant="secondary" className="text-xs">
                          {task.progress?.discovered || 0} 条
                        </Badge>
                      </div>
                      <div className="flex gap-1">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDownloadData('excel', 'links')}
                          className="flex-1"
                        >
                          <FileSpreadsheet className="h-3 w-3 mr-1" />
                          Excel
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDownloadData('csv', 'links')}
                          className="flex-1"
                        >
                          <Database className="h-3 w-3 mr-1" />
                          CSV
                        </Button>
                      </div>
                    </div>

                    {/* 评论数据 */}
                    <div className="p-3 border rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <FileText className="h-4 w-4 text-green-500" />
                        <span className="text-sm font-medium">用户评论</span>
                        <Badge variant="secondary" className="text-xs">
                          {task.progress?.scraped || 0} 条
                        </Badge>
                      </div>
                      <div className="flex gap-1">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDownloadData('excel', 'comments')}
                          className="flex-1"
                        >
                          <FileSpreadsheet className="h-3 w-3 mr-1" />
                          Excel
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDownloadData('csv', 'comments')}
                          className="flex-1"
                        >
                          <Database className="h-3 w-3 mr-1" />
                          CSV
                        </Button>
                      </div>
                    </div>

                    {/* 洞察报告 */}
                    <div className="p-3 border rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Activity className="h-4 w-4 text-purple-500" />
                        <span className="text-sm font-medium">洞察报告</span>
                        <Badge variant="secondary" className="text-xs">
                          {task.status === 'COMPLETED' ? '已完成' : '生成中'}
                        </Badge>
                      </div>
                      <div className="flex gap-1">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDownloadData('excel', 'insights')}
                          disabled={task.status !== 'COMPLETED'}
                          className="flex-1"
                        >
                          <FileSpreadsheet className="h-3 w-3 mr-1" />
                          Excel
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDownloadData('json', 'insights')}
                          disabled={task.status !== 'COMPLETED'}
                          className="flex-1"
                        >
                          <FileText className="h-3 w-3 mr-1" />
                          JSON
                        </Button>
                      </div>
                    </div>

                    {/* 完整数据包 */}
                    <div className="p-3 border rounded-lg bg-blue-50">
                      <div className="flex items-center gap-2 mb-2">
                        <Download className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-medium text-blue-700">完整数据包</span>
                      </div>
                      <Button
                        size="sm"
                        onClick={() => handleDownloadData('excel', 'all')}
                        className="w-full bg-blue-600 hover:bg-blue-700"
                      >
                        <Download className="h-3 w-3 mr-2" />
                        下载全部数据 (Excel)
                      </Button>
                    </div>
                  </div>
                </div>
              </TabsContent>
            )}
          </CardContent>
        </Tabs>
      </Card>
    </div>
  )
}
