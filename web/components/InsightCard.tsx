"use client"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Badge } from "@/components/ui/badge"
import { Quote } from "lucide-react"

interface Insight {
  id: string
  title: string
  description: string
  evidence: string[]
}

interface InsightCardProps {
  insight: Insight
  index: number
}

export function InsightCard({ insight, index }: InsightCardProps) {
  return (
    <Card className="border-l-4 border-l-amber-500">
      <CardHeader>
        <CardTitle className="flex items-start space-x-3">
          <Badge variant="outline" className="mt-1 shrink-0">
            {index}
          </Badge>
          <span className="text-lg leading-tight">{insight.title}</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-gray-700 leading-relaxed">{insight.description}</p>

        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="evidence" className="border-none">
            <AccordionTrigger className="text-sm font-medium text-gray-600 hover:text-gray-900 py-2">
              查看用户评论佐证 ({insight.evidence.length}条)
            </AccordionTrigger>
            <AccordionContent className="pt-2">
              <div className="space-y-3">
                {insight.evidence.map((comment, idx) => (
                  <div key={idx} className="bg-gray-50 p-3 rounded-lg border-l-2 border-gray-300">
                    <div className="flex items-start space-x-2">
                      <Quote className="h-4 w-4 text-gray-400 mt-1 shrink-0" />
                      <p className="text-sm text-gray-700 italic leading-relaxed">{comment}</p>
                    </div>
                  </div>
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </CardContent>
    </Card>
  )
}
