"use client"
import { Badge } from "@/components/ui/badge"

interface KeywordData {
  word: string
  frequency: number
}

interface DemandKeywordsCloudProps {
  keywords: KeywordData[]
}

export function DemandKeywordsCloud({ keywords }: DemandKeywordsCloudProps) {
  // Sort keywords by frequency
  const sortedKeywords = [...keywords].sort((a, b) => b.frequency - a.frequency)

  // Calculate relative sizes
  const maxFreq = sortedKeywords[0]?.frequency || 1
  const minFreq = sortedKeywords[sortedKeywords.length - 1]?.frequency || 1

  const getSize = (frequency: number) => {
    const ratio = (frequency - minFreq) / (maxFreq - minFreq)
    const minSize = 0.8
    const maxSize = 2
    return minSize + ratio * (maxSize - minSize)
  }

  const getColor = (frequency: number) => {
    const ratio = (frequency - minFreq) / (maxFreq - minFreq)
    if (ratio > 0.7) return "bg-amber-500 text-white"
    if (ratio > 0.4) return "bg-amber-200 text-amber-800"
    return "bg-gray-200 text-gray-700"
  }

  return (
    <div className="flex flex-wrap gap-3 p-4 bg-gradient-to-br from-amber-50 to-orange-50 rounded-lg">
      {sortedKeywords.map((keyword, index) => (
        <Badge
          key={keyword.word}
          variant="secondary"
          className={`${getColor(keyword.frequency)} transition-all hover:scale-105 cursor-default`}
          style={{
            fontSize: `${getSize(keyword.frequency)}rem`,
            padding: `${0.3 * getSize(keyword.frequency)}rem ${0.6 * getSize(keyword.frequency)}rem`,
          }}
        >
          {keyword.word}
          <span className="ml-1 text-xs opacity-75">{keyword.frequency}</span>
        </Badge>
      ))}
    </div>
  )
}
