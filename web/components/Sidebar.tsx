"use client"

import { usePathname, useParams } from "next/navigation"
import { NewTaskButton } from "@/components/NewTaskButton"
import { TaskList } from "@/components/TaskList"
import { PromptDisplay } from "@/components/PromptDisplay"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { Settings, User } from "lucide-react"
import { useState } from "react"
import { AccountManagementPanel } from "@/components/AccountManagementPanel"

export function Sidebar() {
  const pathname = usePathname()
  const params = useParams()
  const router = useRouter()
  const [showAccountPanel, setShowAccountPanel] = useState(false)

  const isDashboardPage = pathname.startsWith("/dashboard/")

  // Get current task ID from URL
  const currentTaskId = isDashboardPage && params.id ? (params.id as string) : null

  return (
    <aside className="w-80 flex-shrink-0 border-r border-gray-200 bg-white p-4 flex flex-col">
      {isDashboardPage ? (
        currentTaskId && <PromptDisplay sessionId={currentTaskId} />
      ) : (
        <>
          <NewTaskButton />
          <div className="mt-6">
            <h2 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">最近的任务</h2>
            <TaskList />
          </div>
        </>
      )}

      {/* V2.0 平台账号管理面板 */}
      {showAccountPanel && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto">
            <AccountManagementPanel onClose={() => setShowAccountPanel(false)} />
          </div>
        </div>
      )}

      {/* 底部操作区：返回按钮和设置 */}
      <div className="mt-auto pt-4 space-y-2">
        {/* V2.0 平台账号管理按钮 */}
        <Button
          variant="outline"
          className="w-full bg-transparent flex items-center gap-2"
          onClick={() => setShowAccountPanel(true)}
        >
          <User className="h-4 w-4" />
          平台账号管理
        </Button>

        {isDashboardPage && (
          <Button variant="outline" className="w-full bg-transparent" onClick={() => router.push("/app")}>
            ← 返回主页
          </Button>
        )}
      </div>
    </aside>
  )
}
