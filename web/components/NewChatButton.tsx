"use client"

import { useRouter } from "next/navigation"
import { Plus } from "lucide-react"

export function NewChatButton() {
  const router = useRouter()

  const handleNewChat = () => {
    router.push("/app")
  }

  return (
    <button
      onClick={handleNewChat}
      className="flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-white bg-amber-500 rounded-lg hover:bg-amber-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500"
    >
      <Plus className="w-4 h-4 mr-2" />
      New Chat
    </button>
  )
}
