"use client"

import type React from "react"

import { useState } from "react"
import { ArrowR<PERSON>, Loader2 } from "lucide-react"
import { useChatHistory } from "@/contexts/ChatHistoryContext"

interface ContinuationInputProps {
  /** 完整的上一轮生成代码 */
  baseCode: string
}

export function ContinuationInput({ baseCode }: ContinuationInputProps) {
  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const { addContinuation, activeSessionId } = useChatHistory()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim()) return

    if (!activeSessionId) {
      alert("No active session found")
      return
    }

    setIsLoading(true)

    try {
      // 组合新的 prompt：上一轮代码 + 本轮需求
      const displayPrompt = input.trim()
      const combinedPrompt = `${baseCode}\n\n/* === 改动需求 === */\n${displayPrompt}`

      // 在当前会话追加记录并更新完整 prompt
      await addContinuation(activeSessionId, displayPrompt, combinedPrompt)
      // 不跳转页面，Dashboard 会自动检测 prompt 更新并重新生成

      // 清空输入
      setInput("")
    } catch (error) {
      console.error('Failed to add continuation:', error)
      alert("Failed to add continuation. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="text-sm font-medium text-gray-700 mb-2">Continue editing this component:</div>
      <div className="relative">
        <input
          type="text"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="Add a forgot password link..."
          className="w-full p-3 pr-12 border border-gray-200 rounded-lg input-focus"
        />
        <button
          type="submit"
          disabled={!input.trim() || isLoading}
          className="absolute right-2 top-1/2 -translate-y-1/2 p-2 bg-amber-500 hover:bg-amber-600 disabled:bg-gray-300 text-white rounded-full transition-colors"
        >
          {isLoading ? <Loader2 size={16} className="animate-spin" /> : <ArrowRight size={16} />}
        </button>
      </div>
    </form>
  )
}
