'use client';

import { PrismLight as SyntaxHighlighter } from 'react-syntax-highlighter';
import tsx from 'react-syntax-highlighter/dist/esm/languages/prism/tsx';
import { a11yDark } from 'react-syntax-highlighter/dist/esm/styles/prism';

SyntaxHighlighter.registerLanguage('tsx', tsx);

interface CodeBlockProps {
  code: string;
}

const CodeBlock = ({ code }: CodeBlockProps) => {
  return (
    <SyntaxHighlighter
      language="tsx"
      style={a11yDark}
      customStyle={{
        margin: 0,
        borderRadius: '0.5rem',
        padding: '1.5rem',
        backgroundColor: '#1A1A1A', // A slightly darker background
        height: '100%',
        overflow: 'auto'
      }}
      codeTagProps={{
        style: {
          fontFamily: 'var(--font-jetbrains-mono)',
        },
      }}
    >
      {code}
    </SyntaxHighlighter>
  );
};

export default CodeBlock;
