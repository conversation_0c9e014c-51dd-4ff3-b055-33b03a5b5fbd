import type React from "react"
import { Sidebar } from "@/components/Sidebar"
import Header from "@/components/Header"

interface AppLayoutProps {
  children: React.ReactNode
  hideHeader?: boolean
}

export function AppLayout({ children, hideHeader = false }: AppLayoutProps) {
  return (
    <div className="flex h-screen bg-[#FDFBF4]">
      <Sidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        {!hideHeader && <Header />}
        <main className="flex-1 p-8 overflow-auto">{children}</main>
      </div>
    </div>
  )
}
