"use client"

import React, { useState, useEffect, useRef } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Brain,
  Search,
  Target,
  CheckCircle,
  Clock,
  Loader2,
  ChevronDown,
  ChevronUp,
  Download,
  Trash2,
  Eye,
  EyeOff,
  Zap,
  XCircle
} from 'lucide-react';
import { AIOutput } from '@/types/window';

interface AIThinkingProcessProps {
  taskId?: string;
  className?: string;
  maxHeight?: string;
  showControls?: boolean;
}

export function AIThinkingProcess({
  taskId,
  className = '',
  maxHeight = '100%',
  showControls = true
}: AIThinkingProcessProps) {
  const [aiOutputs, setAiOutputs] = useState<AIOutput[]>([]);
  const [isVisible, setIsVisible] = useState(true);
  const [isAutoScroll, setIsAutoScroll] = useState(true);
  const [expandedOutputs, setExpandedOutputs] = useState<Set<string>>(new Set());
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // 监听真实AI输出数据
  useEffect(() => {
    if (!taskId) return;

    // 监听AI输出事件
    const handleAIOutput = (_event: any, data: AIOutput) => {
      if (data.metadata.taskId === taskId) {
        setAiOutputs(prev => {
          // 避免重复添加相同的输出
          if (prev.find(output => output.id === data.id)) {
            return prev;
          }
          return [...prev, data];
        });
      }
    };

    // 监听任务进度更新
    const handleTaskProgress = (_event: any, progress: any) => {
      if (progress.taskId === taskId && progress.message) {
        // 将任务进度转换为AI输出格式
        const progressOutput: AIOutput = {
          id: `progress_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: 'thinking',
          content: progress.message,
          metadata: {
            timestamp: new Date().toISOString(),
            taskId: progress.taskId
          }
        };

        setAiOutputs(prev => {
          // 避免重复添加相同的进度消息
          if (prev.find(output => output.content === progress.message && output.type === 'thinking')) {
            return prev;
          }
          return [...prev, progressOutput];
        });
      }
    };

    // 注册事件监听器
    let removeAIOutputListener: (() => void) | undefined;
    let removeTaskProgressListener: (() => void) | undefined;

    if (window.electron) {
      removeAIOutputListener = window.electron.onAIOutput?.(handleAIOutput);
      removeTaskProgressListener = window.electron.onTaskProgress?.(handleTaskProgress);
    }

    // 清理函数
    return () => {
      removeAIOutputListener?.();
      removeTaskProgressListener?.();
    };
  }, [taskId]);

  // 切换输出展开状态
  const toggleOutputExpanded = (outputId: string) => {
    setExpandedOutputs(prev => {
      const newSet = new Set(prev);
      if (newSet.has(outputId)) {
        newSet.delete(outputId);
      } else {
        newSet.add(outputId);
      }
      return newSet;
    });
  };

  // 清空AI输出
  const clearOutputs = () => {
    setAiOutputs([]);
    setExpandedOutputs(new Set());
  };

  // 导出AI输出
  const exportOutputs = () => {
    const exportData = {
      taskId,
      timestamp: new Date().toISOString(),
      aiOutputs: aiOutputs
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ai-thinking-process-${taskId}-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 获取AI输出图标
  const getOutputIcon = (output: AIOutput) => {
    switch (output.type) {
      case 'thinking':
        return <Brain className="h-4 w-4 text-blue-500" />;
      case 'decision':
        return <Target className="h-4 w-4 text-yellow-500" />;
      case 'action':
        return <Zap className="h-4 w-4 text-purple-500" />;
      case 'result':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Brain className="h-4 w-4 text-gray-500" />;
    }
  };

  // 获取输出类型标签
  const getTypeLabel = (type: AIOutput['type']) => {
    switch (type) {
      case 'thinking': return 'AI思考';
      case 'decision': return 'AI决策';
      case 'action': return '执行动作';
      case 'result': return '执行结果';
      case 'error': return '错误';
      default: return '输出';
    }
  };

  // 获取输出样式
  const getOutputStyle = (type: AIOutput['type']) => {
    switch (type) {
      case 'thinking': return 'bg-blue-50 border-blue-200 hover:bg-blue-100';
      case 'decision': return 'bg-yellow-50 border-yellow-200 hover:bg-yellow-100';
      case 'action': return 'bg-purple-50 border-purple-200 hover:bg-purple-100';
      case 'result': return 'bg-green-50 border-green-200 hover:bg-green-100';
      case 'error': return 'bg-red-50 border-red-200 hover:bg-red-100';
      default: return 'bg-white border-gray-200 hover:bg-gray-50';
    }
  };

  // 格式化时间
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // 自动滚动到底部
  useEffect(() => {
    if (isAutoScroll && scrollAreaRef.current) {
      setTimeout(() => {
        const scrollElement = scrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]');
        if (scrollElement) {
          scrollElement.scrollTop = scrollElement.scrollHeight;
        }
      }, 100);
    }
  }, [aiOutputs, isAutoScroll]);

  if (!isVisible) {
    return (
      <div className={`flex items-center justify-center p-4 ${className}`}>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsVisible(true)}
          className="flex items-center gap-2"
        >
          <Eye className="h-4 w-4" />
          显示AI思考过程
        </Button>
      </div>
    );
  }

  return (
    <Card className={`${className}`}>
      {showControls && (
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <Brain className="h-5 w-5 text-blue-500" />
              AI思考过程
            </CardTitle>
            
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsAutoScroll(!isAutoScroll)}
                className="flex items-center gap-1"
              >
                <Clock className="h-4 w-4" />
                {isAutoScroll ? '自动滚动' : '手动滚动'}
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsVisible(false)}
                className="flex items-center gap-1"
              >
                <EyeOff className="h-4 w-4" />
                隐藏
              </Button>
            </div>
          </div>
        </CardHeader>
      )}
      
      <CardContent className="p-0">
        <ScrollArea 
          ref={scrollAreaRef}
          className="w-full"
          style={{ height: maxHeight }}
        >
          <div className="p-4 space-y-3">
            {aiOutputs.length === 0 ? (
              <div className="text-center text-gray-500 py-8">
                <Brain className="h-8 w-8 mx-auto mb-3 text-gray-400" />
                <p className="text-sm text-gray-500">AI输出将在任务开始后实时显示，请耐心等待！</p>
              </div>
            ) : (
              aiOutputs.map((output) => {
                const isExpanded = expandedOutputs.has(output.id);
                return (
                  <div
                    key={output.id}
                    className={`rounded-lg border transition-all duration-200 ${getOutputStyle(output.type)}`}
                  >
                    <div
                      className="flex items-start gap-3 p-4 cursor-pointer"
                      onClick={() => toggleOutputExpanded(output.id)}
                    >
                      <div className="flex-shrink-0 mt-0.5">
                        {getOutputIcon(output)}
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <Badge variant="outline" className="text-xs">
                                {getTypeLabel(output.type)}
                              </Badge>
                              <span className="text-xs text-gray-500">
                                {formatTime(output.metadata.timestamp)}
                              </span>
                              {output.metadata.confidence && (
                                <Badge variant="secondary" className="text-xs">
                                  置信度: {Math.round(output.metadata.confidence * 100)}%
                                </Badge>
                              )}
                              {output.metadata.duration && (
                                <Badge variant="secondary" className="text-xs">
                                  耗时: {output.metadata.duration}ms
                                </Badge>
                              )}
                              {output.metadata.toolName && (
                                <Badge variant="secondary" className="text-xs">
                                  工具: {output.metadata.toolName}
                                </Badge>
                              )}
                            </div>

                            {/* 始终显示内容的第一行作为预览 */}
                            {!isExpanded && (
                              <p className="text-sm text-gray-700 line-clamp-2 leading-relaxed">
                                {output.content.split('\n')[0]}
                              </p>
                            )}
                          </div>

                          <div className="flex items-center gap-2 ml-4 flex-shrink-0">
                            {isExpanded ? (
                              <ChevronUp className="h-4 w-4 text-gray-400" />
                            ) : (
                              <ChevronDown className="h-4 w-4 text-gray-400" />
                            )}
                          </div>
                        </div>

                        {/* 展开时显示完整内容 */}
                        {isExpanded && (
                          <div className="mt-3 pt-3 border-t border-gray-100">
                            <p className="text-sm text-gray-700 whitespace-pre-wrap leading-relaxed">
                              {output.content}
                            </p>

                            {/* 显示结构化数据 */}
                            {output.structured && (
                              <details className="mt-3">
                                <summary className="text-xs text-gray-500 cursor-pointer hover:text-gray-700">
                                  查看结构化数据
                                </summary>
                                <pre className="mt-2 text-xs bg-gray-50 p-3 rounded overflow-x-auto">
                                  {JSON.stringify(output.structured, null, 2)}
                                </pre>
                              </details>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
