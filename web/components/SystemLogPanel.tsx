"use client"

import { useState, useEffect, useRef } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { 
  AlertTriangle, 
  Info, 
  CheckCircle, 
  XCircle, 
  Clock,
  Trash2,
  Download,
  Filter
} from "lucide-react"
import { cn } from "@/lib/utils"

export interface SystemLogEntry {
  id: string
  timestamp: Date
  level: 'info' | 'warning' | 'error' | 'success'
  category: 'system' | 'task' | 'login' | 'worker' | 'network'
  message: string
  details?: string
  taskId?: string
  platform?: string
}

interface SystemLogPanelProps {
  className?: string
  maxEntries?: number
  showCategories?: string[]
  autoScroll?: boolean
}

export function SystemLogPanel({ 
  className, 
  maxEntries = 100, 
  showCategories = ['system', 'task', 'login', 'worker', 'network'],
  autoScroll = true 
}: SystemLogPanelProps) {
  const [logs, setLogs] = useState<SystemLogEntry[]>([])
  const [filteredLogs, setFilteredLogs] = useState<SystemLogEntry[]>([])
  const [selectedLevel, setSelectedLevel] = useState<string>('all')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const scrollAreaRef = useRef<HTMLDivElement>(null)

  // 获取日志图标
  const getLogIcon = (level: string) => {
    switch (level) {
      case 'info': return <Info className="h-4 w-4 text-blue-500" />
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'error': return <XCircle className="h-4 w-4 text-red-500" />
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />
      default: return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  // 获取日志级别样式
  const getLogLevelStyle = (level: string) => {
    switch (level) {
      case 'info': return "border-l-blue-500 bg-blue-50"
      case 'warning': return "border-l-yellow-500 bg-yellow-50"
      case 'error': return "border-l-red-500 bg-red-50"
      case 'success': return "border-l-green-500 bg-green-50"
      default: return "border-l-gray-500 bg-gray-50"
    }
  }

  // 获取分类徽章样式
  const getCategoryBadgeStyle = (category: string) => {
    switch (category) {
      case 'system': return "bg-purple-100 text-purple-800"
      case 'task': return "bg-blue-100 text-blue-800"
      case 'login': return "bg-green-100 text-green-800"
      case 'worker': return "bg-orange-100 text-orange-800"
      case 'network': return "bg-gray-100 text-gray-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  // 添加日志条目
  const addLogEntry = (entry: Omit<SystemLogEntry, 'id' | 'timestamp'>) => {
    const newEntry: SystemLogEntry = {
      ...entry,
      id: `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date()
    }

    setLogs(prevLogs => {
      const updatedLogs = [newEntry, ...prevLogs].slice(0, maxEntries)
      return updatedLogs
    })
  }

  // 过滤日志
  useEffect(() => {
    let filtered = logs

    if (selectedLevel !== 'all') {
      filtered = filtered.filter(log => log.level === selectedLevel)
    }

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(log => log.category === selectedCategory)
    }

    if (showCategories.length > 0) {
      filtered = filtered.filter(log => showCategories.includes(log.category))
    }

    setFilteredLogs(filtered)
  }, [logs, selectedLevel, selectedCategory, showCategories])

  // 自动滚动到底部
  useEffect(() => {
    if (autoScroll && scrollAreaRef.current) {
      const scrollElement = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]')
      if (scrollElement) {
        scrollElement.scrollTop = 0 // 滚动到顶部（因为我们是倒序显示）
      }
    }
  }, [filteredLogs, autoScroll])

  // 清空日志
  const clearLogs = () => {
    setLogs([])
  }

  // 导出日志
  const exportLogs = () => {
    const logText = filteredLogs.map(log => 
      `[${log.timestamp.toISOString()}] [${log.level.toUpperCase()}] [${log.category}] ${log.message}${log.details ? '\n  Details: ' + log.details : ''}`
    ).join('\n')

    const blob = new Blob([logText], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `system-logs-${new Date().toISOString().split('T')[0]}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // 监听系统日志事件
  useEffect(() => {
    // 监听自定义日志事件
    const handleSystemLog = (event: CustomEvent) => {
      addLogEntry(event.detail)
    }

    window.addEventListener('system-log', handleSystemLog as EventListener)

    return () => {
      window.removeEventListener('system-log', handleSystemLog as EventListener)
    }
  }, [])

  return (
    <Card className={cn("h-full flex flex-col", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base">系统日志</CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {filteredLogs.length} 条记录
            </Badge>
            <Button variant="ghost" size="sm" onClick={exportLogs}>
              <Download className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={clearLogs}>
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* 过滤器 */}
        <div className="flex items-center gap-2 mt-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <select 
            value={selectedLevel} 
            onChange={(e) => setSelectedLevel(e.target.value)}
            className="text-xs border rounded px-2 py-1"
          >
            <option value="all">所有级别</option>
            <option value="info">信息</option>
            <option value="warning">警告</option>
            <option value="error">错误</option>
            <option value="success">成功</option>
          </select>
          <select 
            value={selectedCategory} 
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="text-xs border rounded px-2 py-1"
          >
            <option value="all">所有分类</option>
            <option value="system">系统</option>
            <option value="task">任务</option>
            <option value="login">登录</option>
            <option value="worker">工作器</option>
            <option value="network">网络</option>
          </select>
        </div>
      </CardHeader>

      <CardContent className="flex-1 p-0">
        <ScrollArea className="h-full px-4" ref={scrollAreaRef}>
          <div className="space-y-2 pb-4">
            {filteredLogs.length === 0 ? (
              <div className="text-center text-gray-500 py-8">
                <Info className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">暂无日志记录</p>
              </div>
            ) : (
              filteredLogs.map((log, index) => (
                <div key={log.id}>
                  <div className={cn(
                    "border-l-4 p-3 rounded-r-md transition-colors",
                    getLogLevelStyle(log.level)
                  )}>
                    <div className="flex items-start justify-between gap-2">
                      <div className="flex items-start gap-2 flex-1">
                        {getLogIcon(log.level)}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <Badge 
                              variant="secondary" 
                              className={cn("text-xs", getCategoryBadgeStyle(log.category))}
                            >
                              {log.category}
                            </Badge>
                            {log.platform && (
                              <Badge variant="outline" className="text-xs">
                                {log.platform}
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm font-medium text-gray-900 break-words">
                            {log.message}
                          </p>
                          {log.details && (
                            <p className="text-xs text-gray-600 mt-1 break-words">
                              {log.details}
                            </p>
                          )}
                        </div>
                      </div>
                      <span className="text-xs text-gray-500 whitespace-nowrap">
                        {log.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                  {index < filteredLogs.length - 1 && <Separator className="my-2" />}
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}

// 导出添加日志的工具函数
export const useSystemLog = () => {
  const addLog = (entry: Omit<SystemLogEntry, 'id' | 'timestamp'>) => {
    // 这里可以通过事件系统或状态管理来添加日志
    // 暂时使用 window 对象作为简单的事件总线
    window.dispatchEvent(new CustomEvent('system-log', { detail: entry }))
  }

  return { addLog }
}
