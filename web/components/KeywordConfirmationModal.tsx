"use client"

import React, { useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import type { KeywordConfirmation } from "@/types/task"

interface KeywordConfirmationModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: (selectedKeywords: string[]) => void
  keywords: string[] | KeywordConfirmation[]
  isLoading?: boolean
}

export function KeywordConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  keywords,
  isLoading = false,
}: KeywordConfirmationModalProps) {
  // 统计所有唯一关键词，作为总数
  const isGrouped = keywords.length > 0 && typeof keywords[0] === "object" && "theme" in keywords[0]
  const groupedKeywords = isGrouped ? (keywords as KeywordConfirmation[]) : null
  const flatKeywords = !isGrouped ? (keywords as string[]) : null

  // 计算所有唯一关键词
  const uniqueKeywords = React.useMemo(() => {
    if (groupedKeywords) {
      return Array.from(new Set(groupedKeywords.flatMap((g) => g.keywords)))
    } else if (flatKeywords) {
      return Array.from(new Set(flatKeywords))
    }
    return []
  }, [groupedKeywords, flatKeywords])

  // 默认全选所有唯一关键词
  const [selectedKeywords, setSelectedKeywords] = useState<Set<string>>(new Set())
  const [selectedThemes, setSelectedThemes] = useState<Set<string>>(new Set())

  React.useEffect(() => {
    if (isOpen) {
      if (groupedKeywords) {
        // 默认全选所有主题和所有唯一关键词
        const allThemes = new Set(groupedKeywords.map((g) => g.theme))
        setSelectedThemes(allThemes)
        setSelectedKeywords(new Set(uniqueKeywords))
      } else if (flatKeywords) {
        setSelectedKeywords(new Set(uniqueKeywords))
      }
    }
  }, [isOpen, groupedKeywords, flatKeywords, uniqueKeywords])

  // 主题全选/取消时，按唯一关键词逻辑处理
  const handleThemeToggle = (theme: string, keywords: string[]) => {
    const newSelectedThemes = new Set(selectedThemes)
    const newSelectedKeywords = new Set(selectedKeywords)
    if (selectedThemes.has(theme)) {
      newSelectedThemes.delete(theme)
      // 取消该主题下所有关键词（如果这些关键词没有在其他主题下被选中则移除）
      keywords.forEach((keyword) => {
        // 检查该关键词是否只在本主题下出现
        if (groupedKeywords) {
          const appearCount = groupedKeywords.filter(g => g.keywords.includes(keyword)).length
          if (appearCount === 1) {
            newSelectedKeywords.delete(keyword)
          }
        } else {
          newSelectedKeywords.delete(keyword)
        }
      })
    } else {
      newSelectedThemes.add(theme)
      keywords.forEach((keyword) => newSelectedKeywords.add(keyword))
    }
    setSelectedThemes(newSelectedThemes)
    setSelectedKeywords(newSelectedKeywords)
  }

  // 单个关键词互斥选择逻辑
  const handleKeywordToggle = (keyword: string) => {
    const newSelectedKeywords = new Set(selectedKeywords)
    if (selectedKeywords.has(keyword)) {
      newSelectedKeywords.delete(keyword)
    } else {
      newSelectedKeywords.add(keyword)
    }
    setSelectedKeywords(newSelectedKeywords)
  }

  // 统计选中数量和总数（去重）
  const selectedCount = selectedKeywords.size
  const totalCount = uniqueKeywords.length

  // 判断某个关键词是否已被选中（全局唯一）
  const isKeywordSelected = (keyword: string) => selectedKeywords.has(keyword)
  // 判断某个关键词是否应禁用（同名关键词已被选中且不是当前第一个出现的位置）
  const isKeywordDisabled = (keyword: string, groupIdx: number, kwIdx: number) => {
    if (!isGrouped) return false
    // 找到所有出现该关键词的分组和索引
    const allPositions: Array<{gIdx: number, kIdx: number}> = []
    groupedKeywords?.forEach((g, gIdx) => {
      g.keywords.forEach((k, kIdx) => {
        if (k === keyword) allPositions.push({gIdx, kIdx})
      })
    })
    // 如果该关键词已被选中，且当前不是第一个出现的位置，则禁用
    if (isKeywordSelected(keyword)) {
      const first = allPositions[0]
      if (!(first.gIdx === groupIdx && first.kIdx === kwIdx)) {
        return true
      }
    }
    return false
  }

  const handleConfirm = () => {
    onConfirm(Array.from(selectedKeywords))
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>确认分析关键词</DialogTitle>
          <DialogDescription>
            AI已为您扩展了关键词列表，请确认要分析的关键词。已选择 {selectedCount} / {totalCount} 个关键词。
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-auto py-4">
          {groupedKeywords ? (
            <Accordion type="multiple" defaultValue={groupedKeywords.map((g) => g.theme)} className="space-y-2">
              {groupedKeywords.map((group, gIdx) => (
                <AccordionItem key={group.theme} value={group.theme} className="border rounded-lg">
                  <div className="flex items-center space-x-3 px-4">
                      <Checkbox
                        checked={selectedThemes.has(group.theme)}
                        onCheckedChange={() => handleThemeToggle(group.theme, group.keywords)}
                      className="mt-4" // 对齐触发器文本
                      />
                    <AccordionTrigger className="flex-1 hover:no-underline justify-start">
                      <div className="flex items-center space-x-3">
                      <span className="font-medium">{group.theme}</span>
                      <Badge variant="secondary">{group.keywords.length}个</Badge>
                    </div>
                  </AccordionTrigger>
                  </div>
                  <AccordionContent className="px-4 pb-4">
                    <div className="grid grid-cols-2 gap-2 pl-7">
                      {group.keywords.map((keyword, kIdx) => (
                        <div key={keyword + '-' + gIdx + '-' + kIdx} className="flex items-center space-x-2">
                          <Checkbox
                            checked={isKeywordSelected(keyword)}
                            onCheckedChange={() => handleKeywordToggle(keyword)}
                            disabled={isKeywordDisabled(keyword, gIdx, kIdx)}
                          />
                          <span className="text-sm">{keyword}</span>
                        </div>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          ) : (
            <div className="grid grid-cols-2 gap-3">
              {flatKeywords?.map((keyword) => (
                <div key={keyword} className="flex items-center space-x-2 p-2 border rounded">
                  <Checkbox
                    checked={isKeywordSelected(keyword)}
                    onCheckedChange={() => handleKeywordToggle(keyword)}
                  />
                  <span className="text-sm">{keyword}</span>
                </div>
              ))}
            </div>
          )}
        </div>

        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            取消
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={selectedCount === 0 || isLoading}
            className="bg-amber-500 hover:bg-amber-600"
          >
            {isLoading ? "处理中..." : `确认分析 (${selectedCount}个关键词)`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
