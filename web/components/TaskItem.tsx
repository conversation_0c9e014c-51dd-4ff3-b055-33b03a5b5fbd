"use client"

import React, { useState } from "react"
import { useRout<PERSON>, usePara<PERSON> } from "next/navigation"
import { MoreH<PERSON>zon<PERSON>, Trash2, Clock, CheckCircle, XCircle, AlertCircle, Square } from "lucide-react"
import type { Task } from "@/types/task"
import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

interface TaskItemProps {
  task: Task
  onTaskDeleted?: (taskId: string) => void; // 新增回调函数
}

export function TaskItem({ task, onTaskDeleted }: TaskItemProps) {
  const router = useRouter()
  const params = useParams()
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false)
  const [isStoppingTask, setIsStoppingTask] = useState(false)
  const isActive = params.id === task.jobId

  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation() // 阻止事件冒泡到 Link
    console.log(`[TaskItem] Attempting to delete task: ${task.id}`);
    try {
      if (window.electronAPI) {
        // 🔧 修复：检查任务状态，如果是活跃任务则先停止再删除
        const isActiveTask = ['PENDING', 'DISCOVERING', 'SCRAPING', 'ANALYZING'].includes(task.status);

        if (isActiveTask) {
          console.log(`[TaskItem] Task ${task.id} is active (${task.status}), stopping before deletion`);
        }

        await window.electronAPI.deleteTask(task.id, { force: false });
        console.log(`[TaskItem] Task ${task.id} deleted successfully`);
      } else {
        throw new Error("Electron context not available");
      }
      onTaskDeleted?.(task.id); // 调用回调
      setIsDeleteDialogOpen(false)
    } catch (error) {
      console.error(`[TaskItem] Delete task error for ${task.id}:`, error)

      // 🔧 修复：提供更友好的错误处理
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('currently pending') || errorMessage.includes('currently discovering') ||
          errorMessage.includes('currently scraping') || errorMessage.includes('currently analyzing')) {
        console.log(`[TaskItem] Task is active, user should use stop function first`);
        // 这里可以显示一个提示，告诉用户先停止任务
      }
      // TODO: Show a toast notification to the user
    }
  }

  const handleStopTask = async (e: React.MouseEvent) => {
    e.stopPropagation() // 阻止事件冒泡到 Link
    console.log(`[TaskItem] Attempting to stop task: ${task.id}`);
    setIsStoppingTask(true);

    try {
      if (window.electronAPI?.controlTask) {
        await window.electronAPI.controlTask(task.id, 'stop');
        console.log(`[TaskItem] Task ${task.id} stopped successfully`);
      } else {
        throw new Error("Electron API not available");
      }
    } catch (error) {
      console.error(`[TaskItem] Stop task error for ${task.id}:`, error);
    } finally {
      setIsStoppingTask(false);
    }
  }

  const handleItemClick = () => {
    router.push(`/dashboard/${task.jobId}`)
  }

  const getStatusIcon = () => {
    switch (task.status) {
      case "COMPLETED":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "FAILED":
        return <XCircle className="h-4 w-4 text-red-500" />
      case "WAITING_CONFIRMATION":
      case "WAITING_USER_LOGIN":
      case "WAITING_CAPTCHA":
        return <AlertCircle className="h-4 w-4 text-amber-500" />
      default:
        return <Clock className="h-4 w-4 text-blue-500" />
    }
  }

  const getStatusText = () => {
    switch (task.status) {
      case "PENDING":
        return "排队中"
      case "EXPENTING":
        return "智能拓词"
      case "DISCOVERING":
        return "发现链接"
      case "WAITING_USER_LOGIN":
        return "等待登录"
      case "WAITING_CAPTCHA":
        return "等待验证"
      case "WAITING_CONFIRMATION":
        return "等待确认"
      case "SCRAPING":
        return "采集评论"
      case "ANALYZING":
        return "AI分析"
      case "COMPLETED":
        return "已完成"
      case "FAILED":
        return "失败"
      default:
        return task.status
    }
  }

  const getDisplayName = () => {
    if (task.initialKeywords.length === 1) {
      return task.initialKeywords[0]
    }
    return `${task.initialKeywords[0]} 等${task.initialKeywords.length}个关键词`
  }

  return (
    <>
      <div
        className={cn(
          "group flex items-center justify-between p-3 rounded-md hover:bg-gray-100 cursor-pointer transition-colors",
          isActive && "bg-amber-50 border border-amber-200",
        )}
        onClick={handleItemClick}
      >
        <div className="flex-1 min-w-0 space-y-1">
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <span className="text-sm font-medium truncate">{getDisplayName()}</span>
          </div>

          <div className="flex items-center space-x-2">
            <Badge variant={task.scanMode === "deep" ? "default" : "secondary"} className="text-xs">
              {task.scanMode === "deep" ? "深度" : "快速"}
            </Badge>
            <span className="text-xs text-gray-500">{getStatusText()}</span>
          </div>

          <div className="text-xs text-gray-400">
            {new Date(task.createdAt).toLocaleString("zh-CN", {
              month: "short",
              day: "numeric",
              hour: "2-digit",
              minute: "2-digit",
            })}
          </div>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={(e) => e.stopPropagation()}
            >
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent onClick={(e) => e.stopPropagation()}>
            {/* 🔧 修复：为活跃任务添加停止选项 */}
            {['PENDING', 'DISCOVERING', 'SCRAPING', 'ANALYZING'].includes(task.status) && (
              <DropdownMenuItem
                onClick={handleStopTask}
                disabled={isStoppingTask}
                className="text-orange-500"
              >
                <Square className="mr-2 h-4 w-4" />
                {isStoppingTask ? '停止中...' : '停止'}
              </DropdownMenuItem>
            )}
            <DropdownMenuItem onClick={() => setIsDeleteDialogOpen(true)} className="text-red-500">
              <Trash2 className="mr-2 h-4 w-4" />
              删除
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              {['PENDING', 'DISCOVERING', 'SCRAPING', 'ANALYZING'].includes(task.status) ? (
                <>
                  这个任务正在执行中（状态：{getStatusText()}）。删除操作会先停止任务，然后删除所有相关数据。
                  <br />
                  <strong>此操作无法撤销。</strong>
                </>
              ) : (
                '确定要删除这个分析任务吗？此操作无法撤销。'
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={(e) => e.stopPropagation()}>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-500 hover:bg-red-600">
              {['PENDING', 'DISCOVERING', 'SCRAPING', 'ANALYZING'].includes(task.status) ? '停止并删除' : '删除'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
