"use client"

import { useRouter } from "next/navigation"
import { Plus } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export function NewTaskButton() {
  const router = useRouter()

  const handleNewTask = () => {
    router.push("/app")
  }

  return (
    <Button onClick={handleNewTask} className="w-full bg-amber-500 hover:bg-amber-600 text-white">
      <Plus className="w-4 h-4 mr-2" />
      新建分析任务
    </Button>
  )
}
