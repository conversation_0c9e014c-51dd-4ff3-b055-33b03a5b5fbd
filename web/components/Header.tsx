'use client'

import Link from 'next/link'

interface HeaderProps { className?: string }

export default function Header({ className = "" }: HeaderProps) {
  return (
    <header className={`w-full px-4 py-2 flex items-center justify-between border-b bg-white fixed top-0 left-0 z-50 ${className}`}>
      <Link href="/app" className="text-xl font-bold text-amber-600">聚需求</Link>
      <div className="text-sm text-gray-500">需求洞察助手</div>
    </header>
  )
}
