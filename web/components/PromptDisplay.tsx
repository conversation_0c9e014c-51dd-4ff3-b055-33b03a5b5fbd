"use client"

import React from 'react';
import { useChatHistory } from '@/contexts/ChatHistoryContext';

interface PromptDisplayProps {
  sessionId: string;
}

export function PromptDisplay({ sessionId }: PromptDisplayProps) {
  const { getSessionById } = useChatHistory();
  const session = getSessionById(sessionId);

  if (!session) return null;

  return (
    <div className="p-4 bg-white rounded-lg border border-gray-200 space-y-4">
      <div>
        <h3 className="text-xs font-semibold text-gray-500 uppercase mb-1">Origin Prompt</h3>
        <p className="text-sm text-gray-700 whitespace-pre-wrap break-words">{session.prompt}</p>
      </div>

      {session.addPrompts && session.addPrompts.length > 0 && (
        <div>
          <h3 className="text-xs font-semibold text-gray-500 uppercase mb-1">Add Prompts</h3>
          <ul className="space-y-1 list-disc list-inside text-sm text-gray-700">
            {session.addPrompts.map((p, idx) => (
              <li key={idx} className="whitespace-pre-wrap break-words">{p}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
