{"name": "demand-insight-assistant", "version": "1.1.0", "private": true, "homepage": "./", "main": "main/dist/main/main.js", "scripts": {"dev": "concurrently --kill-others --kill-others-on-fail --handle-input \"npm:dev:next\" \"npm:dev:electron\"", "dev:simple": "npm run dev:next & sleep 5 && npm run electron:build && npm run dev:standalone", "dev:next": "next dev --port 3000", "dev:electron": "concurrently --kill-others --kill-others-on-fail --handle-input \"tsc -p main -w\" \"wait-on http://127.0.0.1:3000 --timeout 30000 --interval 1000 && npm run electron:dev\"", "dev:clean": "pkill -f \"next dev\" || true && pkill -f \"tsc.*-w\" || true && pkill -f \"electron\" || true", "electron:build": "tsc -p main", "electron:dev": "NODE_ENV=development electron . --enable-logging", "next:build": "next build && next export -o renderer/out", "prebuild": "node ../scripts/download-browsers.js", "build": "npm run build:next && npm run build:electron && npm run build:package", "build:next": "next build", "build:electron": "tsc -p main", "build:package": "electron-builder", "build:win": "npm run prebuild && npm run build:next && npm run build:electron && electron-builder --win", "build:mac": "npm run prebuild && npm run build:next && npm run build:electron && electron-builder --mac", "build:linux": "npm run prebuild && npm run build:next && npm run build:electron && electron-builder --linux", "lint": "next lint", "start": "next start", "dev:standalone": "NODE_ENV=development electron .", "build:quick": "next build && next export -o renderer/out && electron ."}, "build": {"appId": "com.example.demand-insight-assistant", "productName": "Demand Insight Assistant", "directories": {"output": "dist", "buildResources": "build"}, "files": ["main/**/*", "renderer/out/**/*", "../browsers/**/*"], "extraResources": [{"from": "../browsers", "to": "browsers", "filter": ["**/*"]}], "mac": {"target": "dmg", "extraFiles": [{"from": "../browsers/mac", "to": "Resources/browsers/mac", "filter": ["**/*"]}]}, "win": {"target": "nsis", "extraFiles": [{"from": "../browsers/win", "to": "browsers/win", "filter": ["**/*"]}]}, "linux": {"target": "AppImage", "extraFiles": [{"from": "../browsers/linux", "to": "browsers/linux", "filter": ["**/*"]}]}}, "author": "Your Name", "license": "MIT", "pnpm": {"only-allow-build": ["electron", "sharp"], "ignoredBuiltDependencies": ["electron"]}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@openai/agents": "^0.0.12", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "latest", "@radix-ui/react-aspect-ratio": "latest", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "latest", "@radix-ui/react-context-menu": "latest", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "latest", "@radix-ui/react-hover-card": "latest", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "latest", "@radix-ui/react-navigation-menu": "latest", "@radix-ui/react-popover": "latest", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "latest", "@radix-ui/react-scroll-area": "latest", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "latest", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "latest", "@radix-ui/react-toggle-group": "latest", "@radix-ui/react-tooltip": "latest", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "latest", "date-fns": "4.1.0", "dotenv": "^17.2.0", "electron-log": "^5.4.1", "embla-carousel-react": "latest", "input-otp": "latest", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "latest", "playwright": "^1.54.0", "proper-lockfile": "^4.1.2", "react": "^19", "react-day-picker": "latest", "react-dom": "^19", "react-hook-form": "latest", "react-live": "latest", "react-resizable-panels": "latest", "react-syntax-highlighter": "latest", "recharts": "latest", "sonner": "latest", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "latest", "xlsx": "^0.18.5", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22", "@types/proper-lockfile": "^4.1.4", "@types/react": "^19", "@types/react-dom": "^19", "chokidar-cli": "^3.0.0", "concurrently": "^9.2.0", "electron": "^37.2.1", "electron-builder": "^26.0.12", "eslint": "9.31.0", "eslint-config-next": "15.3.5", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5", "wait-on": "^8.0.3"}}