"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { useRouter } from 'next/navigation';

export interface ChatSession {
  id: string;
  /** 用于 UI 展示的简短指令 */
  prompt: string;
  /** 发送给 AI 的完整 prompt，可含代码 */
  fullPrompt?: string;
  timestamp: string;
  status?: 'waiting' | 'streaming' | 'completed';
  /** 追加修改请求列表 */
  addPrompts?: string[];
}

interface ChatHistoryContextType {
  sessions: ChatSession[];
  isLoading: boolean;
  addSession: (prompt: string, fullPrompt?: string) => Promise<string | null>;
  addContinuation: (id: string, addPrompt: string, newFullPrompt: string) => Promise<void>;
  deleteSession: (id: string) => Promise<void>;
  updateSessionPrompt: (id: string, newPrompt: string) => Promise<void>;
  updateSessionStatus: (id: string, status: 'waiting' | 'streaming' | 'completed') => Promise<void>;
  getPromptById: (id: string) => string | null;
  getFullPromptById: (id: string) => string | null;
  getSessionById: (id: string) => ChatSession | null;
  clearHistory: () => Promise<void>;
  activeSessionId: string | null;
  setActiveSessionId: (id: string | null) => void;
  refreshSessions: () => Promise<void>;
}

const ChatHistoryContext = createContext<ChatHistoryContextType | undefined>(undefined);

export const useChatHistory = () => {
  const context = useContext(ChatHistoryContext);
  if (!context) {
    throw new Error('useChatHistory must be used within a ChatHistoryProvider');
  }
  return context;
};

// localStorage相关工具函数
const STORAGE_KEY = 'chat-sessions';

const loadSessionsFromStorage = (): ChatSession[] => {
  if (typeof window === 'undefined') return [];
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Failed to load sessions from localStorage:', error);
    return [];
  }
};

const saveSessionsToStorage = (sessions: ChatSession[]): void => {
  if (typeof window === 'undefined') return;
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(sessions));
  } catch (error) {
    console.error('Failed to save sessions to localStorage:', error);
  }
};

const generateSessionId = (): string => {
  return `session-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
};

export const ChatHistoryProvider = ({ children }: { children: ReactNode }) => {
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [activeSessionId, setActiveSessionId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  // 初始化时从localStorage加载数据
  const refreshSessions = useCallback(async () => {
    setIsLoading(true);
    try {
      const loadedSessions = loadSessionsFromStorage();
      setSessions(loadedSessions);
    } catch (error) {
      console.error('Failed to refresh sessions:', error);
      setSessions([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 组件初始化时加载数据
  useEffect(() => {
    refreshSessions();
  }, [refreshSessions]);

  // 保存到localStorage的效果
  useEffect(() => {
    if (!isLoading) {
      saveSessionsToStorage(sessions);
    }
  }, [sessions, isLoading]);

  const addSession = useCallback(async (prompt: string, fullPrompt?: string): Promise<string | null> => {
    const newSession: ChatSession = {
      id: generateSessionId(),
      prompt,
      fullPrompt: fullPrompt || prompt,
      timestamp: new Date().toISOString(),
      status: 'waiting',
    };

    setSessions(prev => [newSession, ...prev]);
    return newSession.id;
  }, []);

  const addContinuation = useCallback(async (id: string, addPrompt: string, newFullPrompt: string) => {
    setSessions(prev => prev.map(session => {
      if (session.id === id) {
        return {
          ...session,
          fullPrompt: newFullPrompt,
          status: 'waiting' as const,
          addPrompts: [...(session.addPrompts || []), addPrompt],
        };
      }
      return session;
    }));
  }, []);

  const deleteSession = useCallback(async (id: string) => {
    setSessions(prev => prev.filter(session => session.id !== id));
    // 如果删除的是当前活跃会话，导航回主页
    if (activeSessionId === id) {
      router.push('/app');
    }
  }, [activeSessionId, router]);

  const updateSessionPrompt = useCallback(async (id: string, newPrompt: string) => {
    setSessions(prev => prev.map(session =>
      session.id === id ? { ...session, prompt: newPrompt } : session
    ));
  }, []);

  const updateSessionStatus = useCallback(async (id: string, status: 'waiting' | 'streaming' | 'completed') => {
    setSessions(prev => prev.map(session =>
      session.id === id ? { ...session, status } : session
    ));
  }, []);

  const stableSetActiveSessionId = useCallback((id: string | null) => {
    setActiveSessionId(id);
  }, []);

  const getPromptById = useCallback((id: string) => {
    const session = sessions.find(s => s.id === id);
    return session ? session.prompt : null;
  }, [sessions]);

  const getFullPromptById = useCallback((id: string) => {
    const session = sessions.find(s => s.id === id);
    if (!session) return null;
    return session.fullPrompt ?? session.prompt;
  }, [sessions]);

  const getSessionById = useCallback((id: string) => {
    return sessions.find(s => s.id === id) || null;
  }, [sessions]);

  const clearHistory = useCallback(async () => {
    setSessions([]);
    router.push('/app');
  }, [router]);

  const value: ChatHistoryContextType = {
    sessions,
    isLoading,
    addSession,
    addContinuation,
    deleteSession,
    updateSessionPrompt,
    updateSessionStatus,
    getPromptById,
    getFullPromptById,
    getSessionById,
    clearHistory,
    activeSessionId,
    setActiveSessionId: stableSetActiveSessionId,
    refreshSessions,
  };

  return (
    <ChatHistoryContext.Provider value={value}>
      {children}
    </ChatHistoryContext.Provider>
  );
};
