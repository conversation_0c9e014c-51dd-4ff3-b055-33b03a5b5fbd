# 电商需求洞察助手 🔍

**基于AI的电商用户需求分析工具**，通过爬取淘宝、小红书等平台的用户评论，深度分析未被满足的市场需求，为商家提供精准的产品开发方向。

## 🌟 核心功能

- **🤖 智能关键词扩展** - AI自动补充相关搜索词，发现更多市场机会
- **🔍 多平台内容采集** - 淘宝 + 小红书双平台爬虫，获取真实用户反馈
- **🧠 评论智能分析** - 火山引擎AI深度分析用户需求和市场痛点
- **📊 洞察报告生成** - 可视化展示未被满足的需求和商业机会
- **⚡ 双扫描模式** - 支持快速分析和深度调研两种模式
- **🖥️ 桌面应用** - Electron桌面应用，支持本地数据存储和导出

## 🚀 技术栈 (V2.5 简化架构)

- **桌面应用**: Electron + TypeScript + React
- **前端**: React 18 + TypeScript + Tailwind CSS
- **UI组件**: shadcn/ui + Lucide Icons
- **数据采集**: Playwright + 本地浏览器管理
- **AI分析**: 火山引擎API (兼容OpenAI格式)
- **数据存储**: 本地JSON文件 + 数据导出(Excel/CSV/JSON)
- **架构**: 简化的爬虫+AI分析架构，移除复杂的MCP协议

## 📦 本地开发设置 (V2.5 桌面应用)

### 1. 克隆项目并安装依赖

```bash
git clone <your-repo-url>
cd Demand_Insight_Assistant/web
npm install
```

### 2. 环境配置

创建 `.env` 文件并设置AI配置：

```bash
# 火山引擎API配置
VOLCENGINE_API_KEY=your_api_key_here
VOLCENGINE_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
VOLCENGINE_MODEL=ep-20241220161543-8xqzr

# AI参数配置
AI_MAX_TOKENS=4000
AI_TEMPERATURE=0.3

# 日志配置
AI_LOG_LEVEL=info
```

### 3. 开发运行

```bash
# 开发模式运行
npm run dev

# 构建应用
npm run build

# 打包桌面应用
npm run dist
```

# 应用迁移
supabase db push
```

### 3. 环境变量配置

创建 `.env.local` 文件：

```env
# Supabase 配置 (必需)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# DeepSeek AI 配置 (必需)
DEEPSEEK_API_KEY=your-deepseek-api-key

# Bright Data 爬虫配置 (必需)
BRIGHTDATA_API_TOKEN=your-brightdata-token
BRIGHTDATA_TAOBAO_SEARCH_COLLECTOR_ID=your-taobao-search-collector-id
BRIGHTDATA_TAOBAO_PRODUCT_COLLECTOR_ID=your-taobao-product-collector-id  
BRIGHTDATA_XIAOHONGSHU_COLLECTOR_ID=your-xiaohongshu-collector-id

# 应用配置 (可选)
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret
```

### 4. 启动开发服务器

```bash
pnpm dev
```

访问 [http://localhost:3000](http://localhost:3000) 开始使用！

## 🗄️ 数据库架构

项目使用3个核心表实现完整的数据流：

### 📋 核心表结构
- **`tasks`** - 分析任务记录 (状态管理 + 报告存储)
- **`discovered_links`** - 发现的商品/内容链接 (去重机制)
- **`scraped_comments`** - 采集的用户评论 (真实反馈数据)

### 🔐 安全特性
- **行级安全策略 (RLS)** - 用户只能访问自己的数据
- **联合唯一约束** - 避免重复采集，节省成本
- **自动时间戳** - 完整的数据创建和更新记录

完整的表结构和索引设计见 `supabase/migrations/001_create_demand_insight_tables.sql`

## 🎯 使用流程

### 深度扫描模式 (推荐)
1. **输入关键词** - 如"蓝牙耳机"、"瑜伽垫"等
2. **AI扩展分析** - 系统自动扩展相关关键词
3. **确认关键词** - 用户确认要分析的关键词范围
4. **多平台采集** - 自动爬取淘宝、小红书相关数据
5. **AI深度分析** - Map-Reduce算法分析用户需求
6. **洞察报告** - 生成详细的市场机会报告

### 快速分析模式
1. **输入关键词** - 直接输入要分析的产品词
2. **AI快速分析** - 基于关键词进行快速市场分析
3. **即时报告** - 快速获得初步的市场洞察

## 🏗️ 项目架构

```
电商需求洞察助手/
├── 🎨 前端组件
│   ├── TaskMonitor (任务状态监控)
│   ├── ReportContainer (洞察报告展示)
│   ├── KeywordConfirmationModal (关键词确认)
│   └── DemandKeywordsCloud (需求词云图)
│
├── 🔧 后端服务
│   ├── 异步任务处理 (避免超时限制)
│   ├── AI分析引擎 (DeepSeek集成)
│   ├── 爬虫采集引擎 (Bright Data)
│   └── 数据存储层 (Supabase)
│
└── 🗃️ 数据流程
    ├── 关键词预处理 → 链接发现
    ├── 评论采集 → AI分析  
    └── 洞察生成 → 报告展示
```

## 🔧 API文档

### 任务管理
- `POST /api/start-task` - 启动新的分析任务
- `GET /api/task-status?id={jobId}` - 查询任务状态  
- `POST /api/confirm-keywords` - 确认扩展的关键词
- `GET /api/tasks` - 获取用户任务列表

### 核心处理
- `POST /api/process-task` - 异步任务处理引擎

## 🚀 部署指南

### Vercel部署 (推荐)
1. 连接GitHub仓库到Vercel
2. 配置所有环境变量
3. 部署后验证功能正常

### 其他平台
项目基于标准Next.js架构，支持部署到任何支持Node.js的平台。

### 部署检查清单
- [ ] 配置所有环境变量 (Supabase + DeepSeek + Bright Data)
- [ ] 验证数据库迁移已执行
- [ ] 测试用户注册和登录功能
- [ ] 验证任务创建和状态轮询
- [ ] 测试完整的深度扫描流程

## 📈 性能优化

- **异步任务处理** - 避免API超时，支持长时间运行
- **数据库索引优化** - 查询性能优化
- **联合去重机制** - 避免重复采集，节省成本
- **Map-Reduce分析** - 提高AI分析质量和效率

## 🛡️ 安全措施

- **环境变量隔离** - API密钥不暴露到前端
- **行级安全策略** - 数据库级别的用户隔离
- **认证授权** - Supabase Auth集成
- **输入验证** - 防止SQL注入和XSS攻击

## 📞 支持与反馈

如果遇到问题或有建议，请：
1. 查看项目文档和代码注释
2. 检查环境变量配置是否正确
3. 验证数据库表是否正确创建
4. 查看浏览器控制台错误信息

## 📄 开源协议

MIT License - 详见 LICENSE 文件

---

**⭐ 如果这个项目对你有帮助，请给个星星支持一下！** 