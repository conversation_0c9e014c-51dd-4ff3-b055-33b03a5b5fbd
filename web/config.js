"use strict";
/**
 * V2.0 配置中心 - 统一管理所有魔法数字和配置项
 *
 * 这个文件集中管理了应用中所有的配置参数，避免硬编码的"魔法数字"
 * 散落在代码各处，提高可维护性和可配置性。
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getNetworkConfig = exports.getPerformanceConfig = exports.getLoggingConfig = exports.getSessionConfig = exports.getMonitoringConfig = exports.getDelaysConfig = exports.getHealthConfig = exports.getScrapingConfig = exports.getConcurrencyConfig = exports.getConfig = exports.config = exports.ConfigManager = exports.DEFAULT_CONFIG = void 0;
/**
 * 默认配置 - 基于当前代码中的魔法数字整理
 */
exports.DEFAULT_CONFIG = {
    concurrency: {
        maxConcurrentTasksPerPlatform: 5, // 原 pLimit(5)
        maxPagesPerPlatform: 5, // 原 MAX_PAGES_PER_PLATFORM = 5
        keywordProcessingConcurrency: 5, // 关键词并发处理数
    },
    scraping: {
        taobaoMaxLinksPerPage: 44, // 原 links.slice(0, 44)
        xiaohongshuMaxLinksPerPage: 50, // 原 links.slice(0, 50)
        maxCommentsPerProduct: 100, // 每个商品最大评论数
        linkDiscoveryTimeout: 15000, // 原 timeout: 15000
        commentScrapingTimeout: 30000, // 评论采集超时
    },
    health: {
        workerFailureThreshold: 3, // 原 getFailureThreshold(): 3
        workerInactivityTimeout: 600000, // 原 10 * 60 * 1000 (10分钟)
        pagePoolWaitTimeout: 30000, // 原 30000 (30秒)
        taskRetryCount: 3, // 任务重试次数
        retryInterval: 1000, // 原 retry 函数中的延时
    },
    delays: {
        keywordDelayMin: 1000, // 原 Math.random() * 2000 + 1000 的最小值
        keywordDelayMax: 3000, // 原 Math.random() * 2000 + 1000 的最大值
        pageLoadWait: 1000, // 原 waitForTimeout(1000)
        contentLoadMaxWait: 10000, // 内容加载最大等待时间
    },
    monitoring: {
        healthCheckInterval: 1000, // 原 setInterval(..., 1000)
    },
    session: {
        loginSessionTimeout: 300000, // 原 5 * 60 * 1000 (5分钟)
        loginStatusCheckInterval: 5000, // 登录状态检查间隔
        autoLoginRetryCount: 3, // 自动登录重试次数
    },
    logging: {
        maxLogEntries: 100, // 日志面板最大条目数
        logLevel: 'info', // 默认日志级别
        enableFileLogging: true, // 启用文件日志
        maxLogFileSize: 10, // 日志文件最大10MB
    },
    performance: {
        memoryWarningThreshold: 512, // 内存使用超过512MB时警告
        cpuWarningThreshold: 80, // CPU使用超过80%时警告
        performanceMonitorInterval: 5000, // 每5秒监控一次性能
    },
    network: {
        requestTimeout: 30000, // 原 timeout: 30000
        navigationTimeout: 30000, // 页面导航超时
        networkRetryCount: 3, // 网络请求重试次数
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    },
};
/**
 * 配置管理器类
 */
class ConfigManager {
    constructor() {
        this.config = { ...exports.DEFAULT_CONFIG };
    }
    static getInstance() {
        if (!ConfigManager.instance) {
            ConfigManager.instance = new ConfigManager();
        }
        return ConfigManager.instance;
    }
    /**
     * 获取完整配置
     */
    getConfig() {
        return { ...this.config };
    }
    /**
     * 获取特定配置项
     */
    get(key) {
        return this.config[key];
    }
    /**
     * 更新配置项
     */
    update(key, value) {
        this.config[key] = { ...this.config[key], ...value };
    }
    /**
     * 重置为默认配置
     */
    reset() {
        this.config = { ...exports.DEFAULT_CONFIG };
    }
    /**
     * 从JSON文件加载配置
     */
    loadFromFile(configPath) {
        try {
            const fs = require('fs');
            if (fs.existsSync(configPath)) {
                const fileConfig = JSON.parse(fs.readFileSync(configPath, 'utf-8'));
                this.config = { ...exports.DEFAULT_CONFIG, ...fileConfig };
                console.log(`[ConfigManager] ✅ 配置已从文件加载: ${configPath}`);
            }
        }
        catch (error) {
            console.error(`[ConfigManager] ❌ 配置文件加载失败: ${error}`);
            console.log(`[ConfigManager] 🔄 使用默认配置`);
        }
    }
    /**
     * 保存配置到JSON文件
     */
    saveToFile(configPath) {
        try {
            const fs = require('fs');
            const path = require('path');
            // 确保目录存在
            const dir = path.dirname(configPath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
            fs.writeFileSync(configPath, JSON.stringify(this.config, null, 2));
            console.log(`[ConfigManager] ✅ 配置已保存到文件: ${configPath}`);
        }
        catch (error) {
            console.error(`[ConfigManager] ❌ 配置文件保存失败: ${error}`);
        }
    }
    /**
     * 验证配置的有效性
     */
    validate() {
        const errors = [];
        // 验证并发配置
        if (this.config.concurrency.maxConcurrentTasksPerPlatform <= 0) {
            errors.push('maxConcurrentTasksPerPlatform must be greater than 0');
        }
        if (this.config.concurrency.maxPagesPerPlatform <= 0) {
            errors.push('maxPagesPerPlatform must be greater than 0');
        }
        // 验证健康检查配置
        if (this.config.health.workerFailureThreshold <= 0) {
            errors.push('workerFailureThreshold must be greater than 0');
        }
        // 验证超时配置
        if (this.config.delays.keywordDelayMin < 0) {
            errors.push('keywordDelayMin must be non-negative');
        }
        if (this.config.delays.keywordDelayMax < this.config.delays.keywordDelayMin) {
            errors.push('keywordDelayMax must be greater than or equal to keywordDelayMin');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
}
exports.ConfigManager = ConfigManager;
/**
 * 全局配置实例
 */
exports.config = ConfigManager.getInstance();
/**
 * 便捷的配置访问函数
 */
const getConfig = () => exports.config.getConfig();
exports.getConfig = getConfig;
const getConcurrencyConfig = () => exports.config.get('concurrency');
exports.getConcurrencyConfig = getConcurrencyConfig;
const getScrapingConfig = () => exports.config.get('scraping');
exports.getScrapingConfig = getScrapingConfig;
const getHealthConfig = () => exports.config.get('health');
exports.getHealthConfig = getHealthConfig;
const getDelaysConfig = () => exports.config.get('delays');
exports.getDelaysConfig = getDelaysConfig;
const getMonitoringConfig = () => exports.config.get('monitoring');
exports.getMonitoringConfig = getMonitoringConfig;
const getSessionConfig = () => exports.config.get('session');
exports.getSessionConfig = getSessionConfig;
const getLoggingConfig = () => exports.config.get('logging');
exports.getLoggingConfig = getLoggingConfig;
const getPerformanceConfig = () => exports.config.get('performance');
exports.getPerformanceConfig = getPerformanceConfig;
const getNetworkConfig = () => exports.config.get('network');
exports.getNetworkConfig = getNetworkConfig;
