"use client"

import { useState, useEffect, useCallback, useRef } from 'react'
import type { Task } from '@/types/task'

/**
 * 🔥 任务状态管理中心 - 解决"暂无任务"频繁出现的根本问题
 *
 * 核心思想：
 * 1. 单一数据源：所有组件共享同一个任务状态
 * 2. 智能缓存：避免频繁的API调用
 * 3. 防抖机制：避免竞争条件
 * 4. 状态持久化：页面刷新时保持状态
 * 5. Electron API 安全检查：确保API可用后再调用
 */

// 等待 Electron API 准备就绪
const waitForElectronAPI = (): Promise<void> => {
  return new Promise((resolve) => {
    if (typeof window !== 'undefined' && window.electronAPI && typeof window.electronAPI.getTasks === 'function') {
      resolve()
      return
    }

    const checkAPI = () => {
      if (typeof window !== 'undefined' && window.electronAPI && typeof window.electronAPI.getTasks === 'function') {
        resolve()
      } else {
        setTimeout(checkAPI, 100)
      }
    }

    checkAPI()
  })
}

interface TaskStoreState {
  tasks: Task[]
  isLoading: boolean
  lastUpdated: number
  error: string | null
}

// 全局状态存储
let globalState: TaskStoreState = {
  tasks: [],
  isLoading: false,
  lastUpdated: 0,
  error: null
}

// 订阅者列表
const subscribers = new Set<() => void>()

// 通知所有订阅者
const notifySubscribers = () => {
  subscribers.forEach(callback => callback())
}

// 更新全局状态
const updateGlobalState = (newState: Partial<TaskStoreState>) => {
  globalState = { ...globalState, ...newState }
  notifySubscribers()
  
  // 持久化到sessionStorage
  try {
    sessionStorage.setItem('taskStore', JSON.stringify({
      tasks: globalState.tasks,
      lastUpdated: globalState.lastUpdated
    }))
  } catch (error) {
    console.warn('[TaskStore] Failed to persist state:', error)
  }
}

// 从sessionStorage恢复状态
const restoreState = () => {
  try {
    const stored = sessionStorage.getItem('taskStore')
    if (stored) {
      const parsed = JSON.parse(stored)
      // 只恢复5分钟内的缓存
      if (Date.now() - parsed.lastUpdated < 5 * 60 * 1000) {
        globalState.tasks = parsed.tasks || []
        globalState.lastUpdated = parsed.lastUpdated || 0
        console.log('[TaskStore] 🔄 Restored state from cache:', globalState.tasks.length, 'tasks')
        return true
      }
    }
  } catch (error) {
    console.warn('[TaskStore] Failed to restore state:', error)
  }
  return false
}

// 防抖控制
let refreshDebounceTimer: NodeJS.Timeout | null = null
let isRefreshing = false

// 核心刷新函数
const refreshTasks = async (force = false): Promise<Task[]> => {
  // 防抖：如果已经在刷新，直接返回当前状态
  if (isRefreshing && !force) {
    console.log('[TaskStore] 🚫 Refresh already in progress, skipping')
    return globalState.tasks
  }

  // 缓存检查：如果数据很新且不是强制刷新，直接返回缓存
  const cacheAge = Date.now() - globalState.lastUpdated
  if (!force && cacheAge < 2000 && globalState.tasks.length > 0) {
    console.log('[TaskStore] 📋 Using cached tasks (age:', Math.round(cacheAge/1000), 's)')
    return globalState.tasks
  }

  isRefreshing = true
  updateGlobalState({ isLoading: true, error: null })

  try {
    console.log('[TaskStore] 🔄 Fetching tasks from API...')

    // 等待 Electron API 准备就绪
    await waitForElectronAPI()

    const tasks = await window.electronAPI.getTasks()
    
    updateGlobalState({
      tasks,
      isLoading: false,
      lastUpdated: Date.now(),
      error: null
    })

    console.log('[TaskStore] ✅ Tasks updated:', tasks.length, 'tasks')
    return tasks
  } catch (error) {
    console.error('[TaskStore] ❌ Failed to fetch tasks:', error)
    updateGlobalState({
      isLoading: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    })
    return globalState.tasks // 返回缓存的任务
  } finally {
    isRefreshing = false
  }
}

// 防抖刷新
const debouncedRefresh = (delay = 500) => {
  if (refreshDebounceTimer) {
    clearTimeout(refreshDebounceTimer)
  }
  
  refreshDebounceTimer = setTimeout(() => {
    refreshTasks(false)
  }, delay)
}

/**
 * 任务状态管理Hook
 */
export const useTaskStore = () => {
  const [, forceUpdate] = useState({})
  const isInitialized = useRef(false)

  // 强制重新渲染
  const triggerUpdate = useCallback(() => {
    forceUpdate({})
  }, [])

  // 订阅状态变化
  useEffect(() => {
    subscribers.add(triggerUpdate)
    
    // 初始化：尝试恢复状态或首次加载
    if (!isInitialized.current) {
      isInitialized.current = true
      
      if (!restoreState()) {
        console.log('[TaskStore] 🚀 Initial load')
        refreshTasks(true)
      } else {
        // 恢复状态后，异步验证数据新鲜度
        setTimeout(() => refreshTasks(false), 100)
      }
    }

    return () => {
      subscribers.delete(triggerUpdate)
    }
  }, [triggerUpdate])

  // 监听任务更新事件
  useEffect(() => {
    let removeListener: (() => void) | undefined

    const setupListener = async () => {
      try {
        // 等待 Electron API 准备就绪
        await waitForElectronAPI()

        // 🔧 修复：确保事件监听器正确设置
        removeListener = window.electronAPI.onTasksUpdated((updatedTasks: Task[]) => {
          console.log('[TaskStore] 📡 Received tasks-updated event:', updatedTasks.length, 'tasks')

          // 🔧 修复：确保任务数据有效性
          const validTasks = Array.isArray(updatedTasks) ? updatedTasks : []

          updateGlobalState({
            tasks: validTasks,
            lastUpdated: Date.now(),
            isLoading: false,
            error: null
          })
        })

        console.log('[TaskStore] ✅ Tasks update listener setup successfully')
      } catch (error) {
        console.error('[TaskStore] Failed to setup tasks listener:', error)
      }
    }

    setupListener()

    return () => {
      if (removeListener) {
        console.log('[TaskStore] 🧹 Cleaning up tasks update listener')
        removeListener()
      }
    }
  }, [])

  return {
    // 状态
    tasks: globalState.tasks,
    isLoading: globalState.isLoading,
    error: globalState.error,
    lastUpdated: globalState.lastUpdated,
    
    // 操作
    refreshTasks: useCallback((force = false) => refreshTasks(force), []),
    debouncedRefresh: useCallback((delay = 500) => debouncedRefresh(delay), []),
    
    // 工具方法
    getTaskById: useCallback((id: string) => {
      return globalState.tasks.find(task => task.id === id || task.jobId === id) || null
    }, []),
    
    hasTask: useCallback((id: string) => {
      return globalState.tasks.some(task => task.id === id || task.jobId === id)
    }, []),
    
    // 状态信息
    isEmpty: globalState.tasks.length === 0 && !globalState.isLoading,
    isStale: Date.now() - globalState.lastUpdated > 10000, // 10秒算过期
  }
}

/**
 * 任务详情Hook - 专门用于TaskMonitor
 */
export const useTaskDetail = (jobId: string) => {
  const store = useTaskStore()
  const [task, setTask] = useState<Task | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [notFound, setNotFound] = useState(false)
  const [hasTriedBackend, setHasTriedBackend] = useState(false)

  useEffect(() => {
    // 从store中查找任务
    const foundTask = store.getTaskById(jobId)

    if (foundTask) {
      setTask(foundTask)
      setIsLoading(false)
      setNotFound(false)
      setHasTriedBackend(false) // 重置后端尝试标志
    } else if (!store.isLoading && !hasTriedBackend) {
      // store已加载完成但没找到任务，尝试从后端获取
      console.log(`[useTaskDetail] Task ${jobId} not found in store, trying backend...`)
      setHasTriedBackend(true)

      const fetchFromBackend = async () => {
        try {
          await waitForElectronAPI()
          const backendTask = await window.electronAPI.getTaskById(jobId)

          if (backendTask) {
            console.log(`[useTaskDetail] Task ${jobId} found in backend, updating store...`)
            setTask(backendTask)
            setIsLoading(false)
            setNotFound(false)
            // 强制刷新store以同步任务
            store.refreshTasks(true)
          } else {
            console.log(`[useTaskDetail] Task ${jobId} not found in backend either`)
            setTask(null)
            setIsLoading(false)
            setNotFound(true)
          }
        } catch (error) {
          console.error(`[useTaskDetail] Failed to fetch task ${jobId} from backend:`, error)
          setTask(null)
          setIsLoading(false)
          setNotFound(true)
        }
      }

      fetchFromBackend()
    } else if (!store.isLoading && hasTriedBackend) {
      // 已经尝试过后端，确认任务不存在
      setTask(null)
      setIsLoading(false)
      setNotFound(true)
    } else {
      // store还在加载中
      setIsLoading(store.isLoading)
    }
  }, [jobId, store.tasks, store.isLoading, hasTriedBackend])

  // 监听特定任务的更新
  useEffect(() => {
    let removeListener: (() => void) | undefined

    const setupListener = async () => {
      try {
        // 等待 Electron API 准备就绪
        await waitForElectronAPI()

        removeListener = window.electronAPI.onTaskUpdated(jobId, (updatedTask: Task | null) => {
          if (updatedTask === null) {
            setTask(null)
            setNotFound(true)
          } else if (updatedTask.id === jobId || updatedTask.jobId === jobId) {
            setTask(updatedTask)
            setNotFound(false)
          }
          setIsLoading(false)
        })
      } catch (error) {
        console.error('[TaskStore] Failed to setup task update listener:', error)
      }
    }

    setupListener()

    return () => {
      if (removeListener) {
        removeListener()
      }
    }
  }, [jobId])

  return {
    task,
    isLoading,
    notFound,
    refresh: store.refreshTasks
  }
}
