#!/usr/bin/env node

/**
 * 🚀 构建时浏览器下载脚本
 * 支持多平台、国内镜像、智能重试
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

// 🌏 国内镜像配置 - 为大陆用户提供稳定的下载源
const CHINA_MIRRORS = {
  // 淘宝NPM镜像的Playwright CDN（推荐，速度快且稳定）
  taobao: 'https://registry.npmmirror.com/-/binary/playwright/',
  // 华为云镜像（备用选择）
  huawei: 'https://mirrors.huaweicloud.com/playwright/',
  // 腾讯云镜像（备用选择）
  tencent: 'https://mirrors.cloud.tencent.com/playwright/',
  // 官方镜像（海外用户或镜像失效时使用）
  official: 'https://playwright.azureedge.net/'
};

// 🎯 平台配置
const PLATFORM_CONFIG = {
  win32: {
    browserDir: 'browsers/win',
    executable: 'chrome.exe',
    playwrightPath: 'chromium-*/chrome-win/chrome.exe'
  },
  darwin: {
    browserDir: 'browsers/mac',
    executable: 'Chromium.app',
    playwrightPath: 'chromium-*/chrome-mac/Chromium.app'
  },
  linux: {
    browserDir: 'browsers/linux',
    executable: 'chrome',
    playwrightPath: 'chromium-*/chrome-linux/chrome'
  }
};

class BrowserDownloader {
  constructor() {
    this.platform = os.platform();
    this.config = PLATFORM_CONFIG[this.platform];
    this.projectRoot = process.cwd();
    
    if (!this.config) {
      throw new Error(`Unsupported platform: ${this.platform}`);
    }
  }

  /**
   * 检测网络环境并选择最佳镜像
   */
  async detectBestMirror() {
    console.log('🌐 检测最佳下载镜像...');
    
    // 检测是否在中国大陆
    const isInChina = await this.isInChina();
    
    if (isInChina) {
      console.log('🇨🇳 检测到中国大陆网络环境，使用国内镜像');
      return await this.testChinaMirrors();
    } else {
      console.log('🌍 使用官方镜像');
      return null; // 使用默认官方镜像
    }
  }

  /**
   * 检测是否在中国大陆
   */
  async isInChina() {
    try {
      // 方法1: 检查环境变量
      if (process.env.CHINA_MIRROR === 'true') {
        return true;
      }

      // 方法2: 检查时区
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      if (timezone.includes('Asia/Shanghai') || timezone.includes('Asia/Beijing')) {
        return true;
      }

      // 方法3: 尝试访问国外网站（更可靠的检测）
      const https = require('https');
      return new Promise((resolve) => {
        const req = https.get('https://www.google.com', { timeout: 3000 }, () => {
          resolve(false); // 能访问Google，不在中国
        });

        req.on('error', () => {
          resolve(true); // 无法访问Google，可能在中国
        });

        req.on('timeout', () => {
          req.destroy();
          resolve(true);
        });
      });
    } catch {
      return true; // 默认使用中国镜像
    }
  }

  /**
   * 测试中国镜像可用性
   */
  async testChinaMirrors() {
    for (const [name, url] of Object.entries(CHINA_MIRRORS)) {
      try {
        console.log(`🧪 测试镜像: ${name}`);

        // 实际测试镜像连通性
        const https = require('https');
        const testUrl = new URL(url);

        const isAvailable = await new Promise((resolve) => {
          const req = https.get({
            hostname: testUrl.hostname,
            port: testUrl.port || 443,
            path: '/',
            timeout: 5000
          }, (res) => {
            resolve(res.statusCode < 400);
          });

          req.on('error', () => resolve(false));
          req.on('timeout', () => {
            req.destroy();
            resolve(false);
          });
        });

        if (isAvailable) {
          console.log(`✅ 镜像 ${name} 可用`);
          return url;
        } else {
          console.log(`❌ 镜像 ${name} 不可用`);
        }
      } catch (error) {
        console.log(`❌ 镜像 ${name} 测试失败: ${error.message}`);
      }
    }

    console.log('⚠️ 所有镜像测试失败，使用默认淘宝镜像');
    return CHINA_MIRRORS.taobao; // 默认使用淘宝镜像
  }

  /**
   * 下载浏览器
   */
  async downloadBrowser() {
    console.log(`🚀 开始为 ${this.platform} 平台下载浏览器...`);
    
    try {
      // 检测最佳镜像
      const mirror = await this.detectBestMirror();
      
      // 设置环境变量
      const env = { ...process.env };
      if (mirror) {
        env.PLAYWRIGHT_DOWNLOAD_HOST = mirror;
        console.log(`📡 使用镜像: ${mirror}`);
      }

      // 执行下载
      console.log('⬇️ 执行 Playwright 浏览器下载...');
      execSync('npx playwright install chromium', {
        stdio: 'inherit',
        env: env,
        timeout: 300000 // 5分钟超时
      });

      // 复制到项目目录
      await this.copyBrowserToProject();
      
      console.log('✅ 浏览器下载完成');
      
    } catch (error) {
      console.error('❌ 浏览器下载失败:', error.message);
      
      // 尝试备用方案
      await this.fallbackDownload();
    }
  }

  /**
   * 复制浏览器到项目目录
   */
  async copyBrowserToProject() {
    const playwrightCacheDir = path.join(os.homedir(), 
      this.platform === 'win32' ? 'AppData/Local/ms-playwright' : 
      this.platform === 'darwin' ? 'Library/Caches/ms-playwright' : 
      '.cache/ms-playwright'
    );

    const targetDir = path.join(this.projectRoot, this.config.browserDir);
    
    console.log(`📁 复制浏览器到项目目录: ${targetDir}`);
    
    // 创建目标目录
    fs.mkdirSync(targetDir, { recursive: true });
    
    // 查找最新的chromium版本
    const chromiumDirs = fs.readdirSync(playwrightCacheDir)
      .filter(dir => dir.startsWith('chromium-'))
      .sort()
      .reverse();
    
    if (chromiumDirs.length === 0) {
      throw new Error('未找到已下载的Chromium浏览器');
    }

    const latestChromium = chromiumDirs[0];
    const sourcePath = path.join(playwrightCacheDir, latestChromium);
    
    // 复制浏览器文件
    const { execSync } = require('child_process');
    if (this.platform === 'win32') {
      execSync(`xcopy "${sourcePath}" "${targetDir}" /E /I /Y`, { stdio: 'inherit' });
    } else {
      execSync(`cp -R "${sourcePath}"/* "${targetDir}/"`, { stdio: 'inherit' });
    }
    
    console.log('✅ 浏览器文件复制完成');
  }

  /**
   * 备用下载方案
   */
  async fallbackDownload() {
    console.log('🔄 尝试备用下载方案...');
    
    // 方案1: 使用不同的镜像
    for (const [name, url] of Object.entries(CHINA_MIRRORS)) {
      try {
        console.log(`🔄 尝试镜像: ${name}`);
        const env = { ...process.env, PLAYWRIGHT_DOWNLOAD_HOST: url };
        execSync('npx playwright install chromium', {
          stdio: 'inherit',
          env: env,
          timeout: 300000
        });
        await this.copyBrowserToProject();
        return;
      } catch (error) {
        console.log(`❌ 镜像 ${name} 失败: ${error.message}`);
      }
    }
    
    throw new Error('所有下载方案都失败了');
  }
}

// 主执行逻辑
async function main() {
  try {
    const downloader = new BrowserDownloader();
    await downloader.downloadBrowser();
    console.log('🎉 浏览器下载和配置完成！');
  } catch (error) {
    console.error('💥 下载失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { BrowserDownloader };
