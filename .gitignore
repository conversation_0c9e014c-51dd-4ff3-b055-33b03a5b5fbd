# 构建产物
.next/
out/
build/
dist/
Electron.app/

# 依赖包
node_modules/
web/node_modules/
*.node

# 临时和敏感文件
.env*
.env
.env.local
.env.development
.env.production
memory.md
prd*.md
taobao*.md
project_rule.md
开发计划.md
log.md

# API配置文件（包含敏感信息）
.insight.config.json
**/ai-config.json
**/.insight.config.json

# 编辑器/调试工具
.DS_Store
.vscode/
.idea/
.cursor
mcp.json
.kiro

# TypeScript 增量编译缓存
*.tsbuildinfo
tsconfig.tsbuildinfo

# 浏览器文件（构建时动态下载）
browsers/
web/browsers/

# 忽略所有 .app 应用包（比如 Electron 打包产物）
*.app/

# 忽略所有 .icns 图标文件
*.icns

# 忽略 Chromium 可执行文件或其框架（包括名字含 Chromium 的大型文件）
*Chromium*